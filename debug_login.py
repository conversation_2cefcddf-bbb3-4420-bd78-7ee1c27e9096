#!/usr/bin/env python3
"""
登录问题调试脚本
"""

import requests
import json

def test_login_direct():
    """直接测试后端登录API"""
    print("🔍 测试后端登录API...")
    
    url = "http://localhost:8000/api/auth/login/"
    data = {
        "email": "<EMAIL>",
        "password": "admin123"
    }
    
    try:
        response = requests.post(url, json=data, timeout=10)
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            print("✅ 后端登录API正常")
            return True
        else:
            print(f"❌ 后端登录API异常: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return False

def test_login_via_proxy():
    """通过前端代理测试登录API"""
    print("\n🔍 测试前端代理登录API...")
    
    url = "http://localhost:3000/api/auth/login/"
    data = {
        "email": "<EMAIL>", 
        "password": "admin123"
    }
    
    try:
        response = requests.post(url, json=data, timeout=10)
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            print("✅ 前端代理登录API正常")
            return True
        else:
            print(f"❌ 前端代理登录API异常: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return False

def test_user_exists():
    """测试用户是否存在"""
    print("\n🔍 检查用户数据...")
    
    import os
    import sys
    
    # 添加Django项目路径
    sys.path.append('/home/<USER>/Desktop/program/Mentia/backend')
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'mentia_backend.settings')
    
    try:
        import django
        django.setup()
        
        from apps.users.models import User
        
        user = User.objects.filter(email='<EMAIL>').first()
        if user:
            print(f"✅ 用户存在: {user.email}")
            print(f"   用户ID: {user.user_id}")
            print(f"   是否激活: {user.is_active}")
            print(f"   密码已设置: {bool(user.password)}")
            
            # 测试密码验证
            if user.check_password('admin123'):
                print("✅ 密码验证正确")
                return True
            else:
                print("❌ 密码验证失败")
                return False
        else:
            print("❌ 用户不存在")
            return False
            
    except Exception as e:
        print(f"❌ 数据库检查失败: {e}")
        return False

def main():
    print("🚀 开始登录问题诊断...")
    print("=" * 50)
    
    # 测试用户数据
    user_ok = test_user_exists()
    
    # 测试后端API
    backend_ok = test_login_direct()
    
    # 测试前端代理
    proxy_ok = test_login_via_proxy()
    
    print("\n" + "=" * 50)
    print("📊 诊断结果:")
    print(f"   用户数据: {'✅' if user_ok else '❌'}")
    print(f"   后端API: {'✅' if backend_ok else '❌'}")
    print(f"   前端代理: {'✅' if proxy_ok else '❌'}")
    
    if all([user_ok, backend_ok, proxy_ok]):
        print("\n🎉 所有测试通过，登录功能正常")
    else:
        print("\n⚠️  发现问题，需要进一步调试")

if __name__ == "__main__":
    main()
