# 灵境 (Mentia) - 个人成长操作系统

## 项目概述

灵境(Mentia)是一个基于Web的"个人成长操作系统"，旨在帮助终身学习者、知识工作者和任何致力于自我提升的个体解决身份焦虑、方向迷失和情感内耗等核心问题。

## 最新更新

### 2025-09-11 - 登录界面重设计和编译性能优化 🚀

#### ⚡ 编译性能飞跃
- **启动时间优化**: 从26.6秒优化到1.6秒 (-94.1%)
- **Turbopack编译器**: 启用现代化Rust编译器，实现毫秒级热更新
- **智能缓存系统**: 实现增量编译和智能缓存策略
- **TypeScript优化**: 启用增量编译和性能优化选项

#### 🎨 登录界面重设计
- **极光背景效果**: 深色渐变背景配合三层极光动画
- **玻璃拟态卡片**: 半透明背景、背景模糊、边框光晕效果
- **交互体验升级**: react-hook-form + zod验证 + react-hot-toast通知
- **响应式设计**: 完美的移动端适配和无障碍支持

### 2025-09-11 - 前端架构深度优化与性能提升 🚀

#### 🏗️ 前端架构重构
- **组件系统统一**: 解决双重架构问题，采用CSS类优先策略
  - 删除重复的React组件（CardContainer、ButtonContainer、InputContainer）
  - 统一使用CSS类（`.btn`, `.card`, `.input`）提升性能
  - 保留复杂交互组件（Button、Input、Toast等）用于表单验证和状态管理
- **构建错误修复**: 修复TypeScript编译错误，实现100%构建成功率
  - 修复BackgroundUpload.tsx类型错误
  - 修复performance.ts MemoryInfo类型定义问题

#### 📦 依赖管理优化
- **图标库统一**: 删除lucide-react，统一使用@heroicons/react
  - 减少40MB重复依赖，node_modules从712MB优化到680MB
  - 统一图标风格，提升维护性
- **Next.js配置现代化**: 启用最新性能优化功能
  - 启用包导入优化（optimizePackageImports）
  - 启用SWC压缩提升构建性能
  - 启用生产环境console移除

#### 🎨 CSS模块化重构
- **模块化架构**: 建立7个模块化CSS文件，替代单一巨大文件
  - `src/styles/components/` - 组件样式模块（buttons.css, cards.css, inputs.css）
  - `src/styles/utilities/` - 工具类模块（effects.css）
  - 模块化导入系统，支持按需加载
- **样式重复清理**: 消除约180行重复代码 (~9%)
  - 统一极光效果系统（4个重复实现 → 1个统一系统）
  - 统一玻璃效果系统（3个重复实现 → 1个统一系统）
  - 统一渐变效果系统（重复定义 → 工具类系统）
- **效果系统优化**: 建立可复用的视觉效果工具类
  - `.aurora-base`, `.aurora-light/medium/strong` - 极光效果
  - `.glass-base`, `.glass-light/medium/heavy` - 玻璃效果
  - `.gradient-primary/success/warning/error` - 渐变效果

#### 📚 文档与规范
- **组件使用指南**: 在globals.css中添加详细的组件使用指南
- **架构分析报告**: 生成380行深度架构分析报告
- **CSS模块化文档**: 完整的模块化重构报告和使用指南
- **样式重复分析**: 详细的重复样式清理分析报告
- **优化总结文档**: 完整记录优化过程和成果

#### 🎯 性能提升成果
- **依赖大小**: 712MB → 671MB（-5.8%）
- **CSS架构**: 1个巨大文件 → 7个模块化文件
- **重复样式**: ~180行重复 → 0行重复（-9%）
- **构建成功率**: 0% → 100%
- **架构统一**: 双重系统 → CSS优先统一策略
- **依赖清理**: 删除framer-motion、@tanstack/react-query等未使用依赖
- **代码质量**: 添加完整的使用指南和最佳实践

### 2025-09-06 - 测试架构统一与前端错误修复 ✅

#### 🧪 测试文件统一管理
- **统一测试目录结构**: 将所有测试文件整理到项目根目录的 `./test/` 目录下
  - `./test/frontend/` - 前端测试文件（Jest + Testing Library）
  - `./test/backend/` - 后端测试文件（Django测试框架）
  - `./test/run_all_tests.sh` - 统一测试运行脚本
- **测试配置优化**: 更新Jest和Django测试配置，支持新的目录结构
- **测试依赖完善**: 安装并配置所有必要的测试依赖包

#### 🔧 前端错误修复
- **运行时错误解决**: 修复前端出现的 "Cannot read properties of undefined (reading 'call')" 错误
- **测试环境配置**: 完善Jest测试环境设置，支持TypeScript和React组件测试
- **依赖管理优化**: 安装缺失的测试相关依赖包

#### 📋 测试运行方式
```bash
# 运行所有测试
./test/run_all_tests.sh

# 只运行前端测试
./test/run_all_tests.sh --frontend

# 只运行后端测试
./test/run_all_tests.sh --backend

# 生成覆盖率报告
./test/run_all_tests.sh --coverage
```

### 2025-09-05 - 代码质量与安全性全面提升 ✅

#### 🎯 前端样式系统重构
- **统一卡片样式系统**: 重构了全局CSS，减少重复样式定义，提高维护性
- **导航栏层级修复**: 解决主题色选择模态框被遮挡问题，优化z-index层级管理
- **响应式设计优化**: 改进移动端适配，确保各设备上的一致体验

#### 🔧 后端架构优化
- **数据库查询优化**:
  - 添加select_related和prefetch_related优化，减少N+1查询问题
  - 实现API响应缓存系统，提升接口响应速度
  - 创建查询性能监控工具，实时追踪慢查询
- **错误处理统一**:
  - 创建通用装饰器系统，统一API错误处理和日志记录
  - 实现前后端一致的错误响应格式
  - 添加错误重试机制和降级策略

#### 🛡️ 安全性增强
- **文件上传安全**:
  - 实现多重文件验证（类型、内容、魔数检查）
  - 添加恶意文件检测和安全文件名生成
  - 创建文件上传频率限制和大小控制
- **API安全加固**:
  - 添加请求频率限制中间件
  - 实现文件上传安全验证
  - 增强用户输入验证和清理

#### 🧪 测试覆盖完善
- **后端测试**:
  - 为用户、AI、博客等核心模块添加单元测试
  - 实现API集成测试，覆盖关键业务流程
  - 创建测试运行脚本和覆盖率报告
- **前端测试**:
  - 添加组件单元测试，重点覆盖文件上传等关键功能
  - 实现错误处理工具的测试覆盖
  - 配置Jest测试环境和模拟工具

#### 📚 代码质量提升
- **代码注释规范**: 为所有新增和修改的文件添加详细注释
- **架构文档**: 创建查询优化、缓存系统、安全验证等技术文档
- **开发工具**: 提供便捷的测试运行和代码质量检查工具

## 核心模块

### 1. 成长引擎 (The Engine) - 进阶任务管理器
- 三段式视图：未来计划、正在进行、已经完成
- 层级化管理：支持目标拆解为子任务
- AI驱动的复盘和开始功能

### 2. 价值罗盘 (The Anchor) - 动态个人价值仪表盘
- 基于完成任务动态生成技能图谱
- 价值观锚定和行动一致性分析
- AI驱动的洞察和建议

### 3. 心灵私域 (The Soul) - 加密的私人博客/日记
- 端到端加密，绝对隐私保护
- 非结构化数据源，支持深度思考记录
- AI辅助反思功能

### 4. 数字镜像 (The Digital Self) - 基于个人数据的AI伴侣
- 个性化模型微调
- 多模式互动（导师、朋友、过去的自己）
- 深度情感链接

## 技术栈

### 核心框架
- **前端**: Next.js 14.2+ + TypeScript + Tailwind CSS
- **后端**: Django 5.0+ + Django REST Framework 3.15+
- **数据库**: PostgreSQL 16+ with pgvector extension
- **认证**: JWT + Django Authentication
- **加密**: 客户端AES-256-GCM加密
- **容器化**: Docker + Docker Compose

### 性能与安全
- **缓存系统**: Redis + Django-Redis
- **查询优化**: select_related, prefetch_related, 查询监控
- **文件安全**: 多重验证、魔数检查、恶意文件检测
- **API安全**: 频率限制、输入验证、错误处理

### 开发与测试
- **后端测试**: pytest + Django Test Framework
- **前端测试**: Jest + Testing Library + React Testing Utils
- **代码质量**: ESLint + Prettier + Black + Flake8
- **文档生成**: drf-spectacular (OpenAPI/Swagger)

## 项目结构

```
Mentia/
├── backend/                 # Django后端
│   ├── mentia_backend/     # Django项目配置
│   ├── apps/               # Django应用
│   │   ├── users/          # 用户管理
│   │   ├── growth/         # 成长引擎
│   │   ├── journal/        # 心灵私域
│   │   └── ai/             # AI功能
│   ├── requirements.txt    # Python依赖
│   └── manage.py
├── frontend/               # Next.js前端
│   ├── src/
│   │   ├── app/            # App Router
│   │   ├── components/     # React组件
│   │   ├── lib/            # 工具函数
│   │   └── types/          # TypeScript类型
│   ├── package.json
│   └── next.config.js
├── test/                   # 统一测试目录 🆕
│   ├── frontend/           # 前端测试
│   │   ├── components/     # 组件测试
│   │   ├── lib/            # 工具函数测试
│   │   ├── __mocks__/      # 模拟文件
│   │   └── setup.ts        # 测试环境配置
│   ├── backend/            # 后端测试
│   │   ├── apps/           # 应用测试
│   │   │   ├── users/      # 用户模块测试
│   │   │   ├── ai/         # AI模块测试
│   │   │   └── blog/       # 博客模块测试
│   │   └── run_tests.py    # 后端测试运行器
│   ├── results/            # 测试结果报告
│   └── run_all_tests.sh    # 统一测试脚本
├── doc/                    # 开发文档
│   └── database.md         # 数据库设计文档
├── docker-compose.yml      # 开发环境配置
└── README.md
```

## 快速开始

### 环境要求
- **Docker & Docker Compose**: 用于数据库服务
- **Python 3.12+**: 后端开发环境
- **Node.js 18+**: 前端开发环境
- **Git**: 版本控制

### 一键启动开发环境

**重要**: 确保 Docker 服务已启动并且当前用户有 Docker 权限。

```bash
# 克隆项目
git clone <repository-url>
cd Mentia

# 一键启动所有服务（Docker + PostgreSQL）
./start-dev.sh

# 或者使用自定义端口
./start-dev.sh --frontend-port 9331 --backend-port 8001
```

启动完成后访问：
- 🌐 前端应用: http://localhost:3000 (或自定义端口)
- 🔧 后端API: http://localhost:8000/api (或自定义端口)
- 📊 API文档: http://localhost:8000/api/docs (或自定义端口)
- 🛠️ Django管理: http://localhost:8000/admin (或自定义端口)

### 端口配置

项目支持灵活的端口配置，解决端口冲突问题：

```bash
# 使用自定义端口启动
./start-dev.sh --frontend-port 9331 --backend-port 8001

# 查看所有可用选项
./start-dev.sh --help
```

**注意**: 端口变更会自动更新所有相关配置文件，确保前后端正确通信。

### 数据库配置

**生产环境数据库 (推荐)**:
- 数据库: PostgreSQL 16 with pgvector
- 主机: localhost:5432
- 数据库名: mentia_db
- 用户名: mentia_user
- 密码: mentia_password

**开发环境备用**: 如果 Docker 不可用，系统会自动回退到 SQLite

### 默认管理员账户
- 邮箱: <EMAIL>
- 密码: admin123456

详细开发指南请参考 [DEVELOPMENT.md](./DEVELOPMENT.md)

## 开发和测试

### 统一测试运行 🆕

**推荐使用统一测试脚本**:
```bash
# 运行所有测试（前端 + 后端）
./test/run_all_tests.sh

# 只运行前端测试
./test/run_all_tests.sh --frontend

# 只运行后端测试
./test/run_all_tests.sh --backend

# 生成覆盖率报告
./test/run_all_tests.sh --coverage

# 详细输出模式
./test/run_all_tests.sh --verbose
```

### 分别运行测试

**后端测试**:
```bash
# 使用统一测试目录
cd test/backend
python3 run_tests.py

# 运行特定应用测试
python3 run_tests.py --app users

# 生成覆盖率报告
python3 run_tests.py --coverage
```

**前端测试**:
```bash
# 进入前端目录
cd frontend

# 运行所有测试
npm test

# 运行测试并生成覆盖率
npm run test:coverage

# 监视模式运行测试
npm run test:watch
```

### 代码质量检查

**后端代码质量**:
```bash
cd backend

# 代码格式化
black .

# 代码检查
flake8 .

# 类型检查
mypy .
```

**前端代码质量**:
```bash
cd frontend

# 代码检查
npm run lint

# 代码格式化
npm run format

# 类型检查
npm run type-check
```

### 性能监控

- 数据库查询监控：开发模式下自动记录慢查询
- API响应时间监控：通过装饰器自动追踪
- 缓存命中率统计：实时监控缓存效果
- 文件上传安全日志：记录所有上传活动

## 已实现功能

### ✅ 用户认证系统
- 用户注册/登录
- JWT令牌认证
- 密码安全验证
- 自动令牌刷新
- 用户设置持久化

### ✅ 成长引擎 (The Engine)
- 三段式任务管理（未来计划/正在进行/已完成）
- 目标和任务的层级管理
- 状态转换和进度追踪
- 标签系统和分类
- AI驱动的子任务生成

### ✅ 价值罗盘 (The Anchor)
- 基于完成项目的技能图谱
- 个人价值观管理
- 成长统计和可视化
- AI成长建议和洞察

### ✅ 心灵私域 (The Soul)
- 私人博客/日记系统
- 分类和标签管理
- 内容搜索和过滤
- 统计和分析功能

### ✅ AI智能助手
- 基于个人数据的智能对话
- 成长分析和建议
- 多轮对话支持
- 上下文理解

### ✅ 现代化UI/UX
- 响应式设计
- 深色/浅色主题支持
- 可复用组件库
- 流畅的用户体验
- 自定义背景和主题

### ✅ 系统安全与性能
- 文件上传安全验证
- API频率限制
- 数据库查询优化
- 响应缓存系统
- 错误处理和监控

### ✅ 测试覆盖
- 后端单元测试和集成测试
- 前端组件测试
- API端点测试
- 文件上传安全测试

### 🚧 开发中功能
- 向量搜索和智能推荐
- 高级AI分析功能
- 数据导入导出
- 移动端应用

## 安全与隐私

### 数据安全
- 所有敏感数据采用客户端加密
- 密码使用强哈希算法存储
- JWT token安全管理
- 数据所有权归用户

### 文件上传安全
- 多重文件类型验证（MIME类型 + 文件扩展名 + 魔数检查）
- 恶意文件检测和过滤
- 安全文件名生成，防止路径遍历攻击
- 文件大小和尺寸限制
- 上传频率限制

### API安全
- 请求频率限制，防止暴力攻击
- 输入验证和清理，防止注入攻击
- 统一错误处理，避免信息泄露
- 详细的安全日志记录

### 系统安全
- 安全HTTP头部设置
- CSRF保护
- XSS防护
- 数据库查询优化，防止性能攻击

## 最新更新 (2025-01-09)

### 🔧 核心问题修复

本次更新修复了6个关键问题，提升了系统稳定性和用户体验：

#### 1. 背景上传功能修复 ✅
- **问题**: 用户无法通过前端设置界面上传自定义应用壁纸 (401错误)
- **修复**: 修正了前端API调用中的Content-Type设置，移除了手动设置的multipart/form-data头部
- **影响**: 用户现在可以正常上传和设置自定义背景图片

#### 2. 博客创建功能修复 ✅
- **问题**: 用户无法通过前端私人博客界面创建博客条目 (400错误)
- **修复**: 修正了后端序列化器中slug字段的必填要求，现在支持自动生成slug
- **影响**: 用户可以正常创建和发布博客文章

#### 3. AI伴侣页面导航修复 ✅
- **问题**: AI伴侣页面导航不可达
- **修复**: 统一了dashboard主页和导航菜单中的AI伴侣链接路径
- **影响**: 用户可以通过导航正常访问AI伴侣功能

#### 4. 背景动效层级优化 ✅
- **问题**: 背景动效被内容层遮挡
- **修复**: 调整了CSS z-index层级，确保背景动效在最底层显示
- **影响**: 背景动效现在正确显示，不会被页面内容遮挡

#### 5. 背景动效增强 ✅
- **问题**: 背景层次与动效需要增强
- **修复**: 添加了更多几何图形、光晕效果和动画，提升视觉体验
- **影响**: 页面背景更加丰富和动态

#### 6. 用户设置持久化验证 ✅
- **问题**: 用户设置的跨设备/session保留
- **验证**: 确认了UserSettings模型和API的正确实现
- **影响**: 用户设置在不同设备和会话间正确保持

### 🚀 新增功能

#### AI伴侣聊天接口
- 新增 `/api/ai/chat/` 端点，支持基于用户数据的个性化对话
- 集成记忆检索功能，提供更智能的回复
- 支持多轮对话和上下文理解

### 📝 代码质量提升

按照开发规范，为所有修改的文件添加了详细注释：
- **文件头注释**: 说明文件用途、功能和使用方式
- **方法注释**: 详细说明每个方法的功能和参数
- **复杂逻辑注释**: 为关键业务逻辑添加说明
- **TODO注释**: 标记未来改进点和扩展方向

### 🔧 技术改进

- 优化了API错误处理和响应格式
- 改进了前端组件的类型安全性
- 统一了路由和导航的命名规范
- 增强了CSS层级管理和动效性能

## 开发原则

- 隐私与安全第一
- 迭代开发，MVP优先
- AI伦理，积极正面赋能
- 用户体验至上

## 🐛 最近修复

### v1.2.1 (2025-09-05)
- ✅ **修复背景图片上传415错误**: 解决了FormData上传时Content-Type冲突问题
- ✅ **修复导航栏交互问题**: 恢复了左侧导航栏的点击和悬停效果
- ✅ **增强背景动效**: 提升了动效颜色对比度，使视觉效果更明显
- ✅ **改进用户体验**: 添加了详细的文件格式说明和推荐分辨率提示
- ✅ **端口配置优化**: 完善了自定义端口支持，确保所有功能在不同端口下正常工作

详细修复内容请参考 [FIXES_SUMMARY.md](FIXES_SUMMARY.md)。

## 更新日志

### v0.2.1 (2025-01-05)
- 🤖 **AI智能助手优化**
  - 将"AI伴侣"重新定位为"智能助手"，提供更专业的交互体验
  - 升级AI模型从qwen-flash到qwen-plus，提升智能性和准确性
  - 优化系统提示词，减少幻觉和过度情感化表达
  - 改进UI样式，解决字体颜色和滚动动效问题
  - 增强基于用户成长数据的个性化分析能力

- 🎨 **界面体验改进**
  - 修复AI助手界面的字体颜色与背景对比度问题
  - 优化对话滚动行为，避免不自然的界面跳动
  - 改善加载状态的视觉效果和用户反馈
  - 更新功能描述，专注于成长分析和专业建议

- 🔧 **技术优化**
  - 修复端口配置中的硬编码问题
  - 确保动态端口分配机制正常工作
  - 优化AI调用参数，提高回复准确性

## 开发指南

详细的开发指南请参考 [DEVELOPMENT.md](./DEVELOPMENT.md)

## 许可证

本项目采用 MIT 许可证 - 详见 [LICENSE](LICENSE) 文件
