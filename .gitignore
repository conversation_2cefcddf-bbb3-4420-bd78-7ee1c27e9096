# 灵境 (Mentia) - Git忽略文件配置

# ===== 通用忽略规则 =====

# 操作系统生成的文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
desktop.ini

# IDE和编辑器文件
.vscode/
.idea/
*.swp
*.swo
*~
.project
.classpath
.settings/
*.sublime-project
*.sublime-workspace

# 临时文件
*.tmp
*.temp
*.log
*.pid
*.seed
*.pid.lock

# ===== Python/Django 后端 =====

# Python字节码
__pycache__/
*.py[cod]
*$py.class
*.so

# 分发/打包
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# 单元测试/覆盖率报告
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# 翻译
*.mo
*.pot

# Django相关
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask相关
instance/
.webassets-cache

# Scrapy相关
.scrapy

# Sphinx文档
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# celery beat调度文件
celerybeat-schedule

# SageMath解析文件
*.sage.py

# 环境变量
.env
.env.local
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder项目设置
.spyderproject
.spyproject

# Rope项目设置
.ropeproject

# mkdocs文档
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre类型检查器
.pyre/

# ===== Node.js/Next.js 前端 =====

# 依赖项
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# 运行时数据
pids
*.pid
*.seed
*.pid.lock

# 覆盖率目录
lib-cov

# nyc测试覆盖率
.nyc_output

# Grunt中间存储
.grunt

# Bower依赖目录
bower_components

# node-waf配置
.lock-wscript

# 编译的二进制插件
build/Release

# 依赖目录
node_modules/
jspm_packages/

# TypeScript v1声明文件
typings/

# TypeScript缓存
*.tsbuildinfo

# 可选的npm缓存目录
.npm

# 可选的eslint缓存
.eslintcache

# Microbundle缓存
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# 可选的REPL历史
.node_repl_history

# yarn v2
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# Next.js构建输出
.next/
.next-build/
out/
*.hot-update.js
*.hot-update.json

# Nuxt.js构建/生成输出
.nuxt
dist

# Gatsby文件
.cache/
public

# Storybook构建输出
.out
.storybook-out

# Temporary folders
tmp/
temp/

# ===== 数据库相关 =====

# SQLite数据库
*.sqlite
*.sqlite3
*.db

# PostgreSQL
*.sql

# MySQL
*.sql

# 数据库备份文件
*.backup
*.bak

# ===== Docker相关 =====

# Docker Compose覆盖文件
docker-compose.override.yml
docker-compose.*.yml

# Docker卷数据
docker-volumes/

# ===== 项目特定忽略 =====

# 后端特定
backend/logs/
backend/media/
backend/staticfiles/
backend/.env
backend/local_settings.py

# 前端特定
frontend/.env.local
frontend/.env.development.local
frontend/.env.test.local
frontend/.env.production.local

# 开发工具生成的文件
*.log
*.pid

# 备份文件
*.backup
*~

# 测试文件
test-results/
coverage/

# 部署相关
deploy/
.deploy/

# 文档构建输出
docs/_build/
docs/build/

# 配置文件（包含敏感信息）
config/local.py
config/production.py
.secrets

# SSL证书
*.pem
*.key
*.crt
*.csr

# 上传文件
uploads/
media/

# 缓存文件
.cache/
cache/

# 监控和日志
logs/
*.log
log/

# 性能分析文件
*.prof

# 本地开发脚本
local-*.sh
dev-*.sh

# 编辑器备份文件
*~
*.bak
*.orig

# 压缩文件
*.zip
*.tar.gz
*.rar

# 系统文件
.fuse_hidden*
.directory
.Trash-*
.nfs*
