# 灵境 (Mentia) 测试指南

## 功能测试清单

### 1. 用户认证系统测试

#### 用户注册
- [ ] 邮箱格式验证
- [ ] 密码强度验证
- [ ] 密码确认匹配
- [ ] 重复邮箱检测
- [ ] 注册成功后自动登录
- [ ] JWT令牌正确生成

#### 用户登录
- [ ] 邮箱密码验证
- [ ] 错误信息提示
- [ ] 登录成功跳转
- [ ] 记住我功能
- [ ] 令牌自动刷新

#### 认证状态
- [ ] 未登录用户重定向
- [ ] 登录状态保持
- [ ] 安全退出功能

### 2. 成长引擎测试

#### 成长项管理
- [ ] 创建新目标/任务
- [ ] 编辑成长项信息
- [ ] 删除成长项
- [ ] 状态转换（未来计划→进行中→已完成）
- [ ] 父子关系管理
- [ ] 标签添加和管理

#### 三段式视图
- [ ] 未来计划列表显示
- [ ] 正在进行列表显示
- [ ] 已完成列表显示
- [ ] 拖拽状态切换
- [ ] 搜索和过滤功能

#### 数据统计
- [ ] 完成率计算
- [ ] 目标任务统计
- [ ] 时间线显示
- [ ] 标签使用统计

### 3. 价值罗盘测试

#### 技能图谱
- [ ] 基于完成项目生成技能
- [ ] 技能使用频率统计
- [ ] 技能图谱可视化
- [ ] 技能发展建议

#### 价值观管理
- [ ] 添加个人价值观
- [ ] 编辑价值观内容
- [ ] 优先级排序
- [ ] 价值观与行动一致性分析

### 4. 用户界面测试

#### 响应式设计
- [ ] 桌面端显示正常
- [ ] 平板端适配良好
- [ ] 移动端体验流畅
- [ ] 侧边栏折叠功能

#### 交互体验
- [ ] 加载状态显示
- [ ] 错误信息提示
- [ ] 成功操作反馈
- [ ] 表单验证提示

## 自动化测试

### 前端测试
```bash
cd frontend

# 运行单元测试
npm run test

# 运行集成测试
npm run test:integration

# 生成测试覆盖率报告
npm run test:coverage
```

### 后端测试
```bash
cd backend

# 激活虚拟环境
source venv/bin/activate

# 运行Django测试
python manage.py test

# 运行pytest测试
pytest

# 生成覆盖率报告
pytest --cov=apps --cov-report=html
```

## 性能测试

### 前端性能
- [ ] 页面加载时间 < 3秒
- [ ] 首屏渲染时间 < 1秒
- [ ] 交互响应时间 < 200ms
- [ ] 资源压缩和缓存

### 后端性能
- [ ] API响应时间 < 500ms
- [ ] 数据库查询优化
- [ ] 并发请求处理
- [ ] 内存使用监控

## 安全测试

### 认证安全
- [ ] JWT令牌安全性
- [ ] 密码哈希强度
- [ ] 会话管理安全
- [ ] CSRF防护

### 数据安全
- [ ] SQL注入防护
- [ ] XSS攻击防护
- [ ] 敏感数据加密
- [ ] API权限控制

## 浏览器兼容性

### 支持的浏览器
- [ ] Chrome 90+
- [ ] Firefox 88+
- [ ] Safari 14+
- [ ] Edge 90+

### 功能兼容性
- [ ] ES6+ 语法支持
- [ ] CSS Grid/Flexbox
- [ ] WebAPI 功能
- [ ] 移动端触摸事件

## 测试数据

### 测试用户账户
```
管理员账户:
邮箱: <EMAIL>
密码: admin123456

测试用户:
邮箱: <EMAIL>
密码: Test123456
```

### 测试数据生成
```bash
# 生成测试数据
python manage.py loaddata fixtures/test_data.json

# 创建示例成长项
python manage.py create_sample_data
```

## 问题报告

### Bug报告模板
```
**问题描述**
简要描述遇到的问题

**复现步骤**
1. 打开页面...
2. 点击按钮...
3. 输入内容...

**预期结果**
描述应该发生什么

**实际结果**
描述实际发生了什么

**环境信息**
- 浏览器: Chrome 120
- 操作系统: macOS 14
- 设备: MacBook Pro

**截图**
如果适用，添加截图说明问题
```

## 测试报告

测试完成后，请更新以下信息：

- 测试执行日期: ___________
- 测试执行人: ___________
- 通过的测试用例: _____ / _____
- 发现的问题数量: _____
- 问题严重程度分布:
  - 严重: _____
  - 中等: _____
  - 轻微: _____

## 持续集成

项目配置了GitHub Actions自动化测试：

- 代码提交时自动运行测试
- Pull Request时执行完整测试套件
- 测试失败时阻止合并
- 生成测试覆盖率报告
