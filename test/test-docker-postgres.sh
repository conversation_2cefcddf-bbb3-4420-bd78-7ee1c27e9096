#!/bin/bash

cd ..

# 测试 Docker + PostgreSQL 连接脚本
set -e

echo "🧪 测试 Docker + PostgreSQL 连接..."

# 停止现有服务
echo "🛑 停止现有服务..."
pkill -f 'python.*runserver' || true
sudo docker-compose down || true

# 启动 Docker 服务
echo "🐳 启动 Docker 服务..."
sudo docker-compose up -d postgres redis

# 等待 PostgreSQL 启动
echo "⏳ 等待 PostgreSQL 启动..."
for i in {1..30}; do
    if sudo docker-compose exec -T postgres pg_isready -U mentia_user -d mentia_db >/dev/null 2>&1; then
        echo "✅ PostgreSQL 已就绪"
        break
    fi
    if [ $i -eq 30 ]; then
        echo "❌ PostgreSQL 启动超时"
        exit 1
    fi
    echo "⏳ 等待 PostgreSQL 启动... ($i/30)"
    sleep 2
done

# 测试数据库连接
echo "🔍 测试数据库连接..."
cd backend
source .venv/bin/activate

# 设置环境变量使用 PostgreSQL
export USE_SQLITE=False

# 运行迁移
echo "🔄 运行数据库迁移..."
python manage.py makemigrations
python manage.py migrate

# 创建超级用户
echo "👤 创建超级用户..."
python manage.py shell -c "
from apps.users.models import User
if not User.objects.filter(is_superuser=True).exists():
    User.objects.create_superuser(email='<EMAIL>', password='admin123456')
    print('✅ 创建了默认超级用户: <EMAIL> / admin123456')
else:
    print('✅ 超级用户已存在')
"

# 启动后端服务
echo "🌐 启动后端服务..."
python manage.py runserver 0.0.0.0:8000 &
DJANGO_PID=$!

# 等待服务启动
sleep 5

# 测试 API
echo "🧪 测试 API 连接..."
curl -f http://localhost:8000/api/schema/ > /dev/null && echo "✅ API Schema 可访问"
curl -f -X POST -H 'Content-Type: application/json' \
     -d '{"email":"<EMAIL>","password":"Test123456","password_confirm":"Test123456"}' \
     http://localhost:8000/api/auth/register/ > /dev/null && echo "✅ 用户注册成功"

echo ""
echo "🎉 Docker + PostgreSQL 测试完成！"
echo ""
echo "📊 数据库信息:"
echo "  - 主机: localhost:5432"
echo "  - 数据库: mentia_db"
echo "  - 用户: mentia_user"
echo "  - 密码: mentia_password"
echo ""
echo "👤 管理员账户:"
echo "  - 邮箱: <EMAIL>"
echo "  - 密码: admin123456"
echo ""
echo "🌐 服务地址:"
echo "  - 后端 API: http://localhost:8000/api"
echo "  - API 文档: http://localhost:8000/api/docs"
echo "  - Django 管理: http://localhost:8000/admin"

# 清理
cleanup() {
    echo "🛑 清理服务..."
    kill $DJANGO_PID 2>/dev/null || true
    sudo docker-compose down
}
trap cleanup EXIT

echo ""
echo "按 Ctrl+C 停止测试"
wait $DJANGO_PID
