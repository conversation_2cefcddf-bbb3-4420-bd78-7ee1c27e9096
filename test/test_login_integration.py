#!/usr/bin/env python3
"""
登录功能集成测试脚本
使用Selenium进行浏览器自动化测试
"""

import time
import requests
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, WebDriverException

def setup_driver():
    """设置Chrome浏览器驱动"""
    chrome_options = Options()
    chrome_options.add_argument('--headless')  # 无头模式
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--disable-gpu')
    chrome_options.add_argument('--window-size=1920,1080')
    
    try:
        driver = webdriver.Chrome(options=chrome_options)
        return driver
    except WebDriverException:
        print("❌ Chrome驱动未找到，尝试使用Firefox...")
        try:
            from selenium.webdriver.firefox.options import Options as FirefoxOptions
            firefox_options = FirefoxOptions()
            firefox_options.add_argument('--headless')
            driver = webdriver.Firefox(options=firefox_options)
            return driver
        except WebDriverException:
            print("❌ 浏览器驱动未找到，请安装Chrome或Firefox驱动")
            return None

def test_backend_api():
    """测试后端API"""
    print("🔍 测试后端API...")
    
    try:
        response = requests.post(
            'http://localhost:8000/api/auth/login/',
            json={
                'email': '<EMAIL>',
                'password': 'admin123456'
            },
            timeout=10
        )
        
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            print("   ✅ 后端API正常")
            return True
        else:
            print(f"   ❌ 后端API异常: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ 后端API请求失败: {e}")
        return False

def test_frontend_login(driver):
    """测试前端登录功能"""
    print("🔍 测试前端登录功能...")
    
    try:
        # 访问登录页面
        print("   访问登录页面...")
        start_time = time.time()
        driver.get('http://localhost:3000/auth/login')
        
        # 等待页面加载
        WebDriverWait(driver, 30).until(
            EC.presence_of_element_located((By.TAG_NAME, "form"))
        )
        load_time = time.time() - start_time
        print(f"   页面加载时间: {load_time:.2f}秒")
        
        # 查找表单元素
        print("   查找表单元素...")
        email_input = WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.NAME, "email"))
        )
        password_input = driver.find_element(By.NAME, "password")
        submit_button = driver.find_element(By.CSS_SELECTOR, "button[type='submit']")
        
        # 填写登录信息
        print("   填写登录信息...")
        email_input.clear()
        email_input.send_keys('<EMAIL>')
        password_input.clear()
        password_input.send_keys('admin123456')
        
        # 提交表单
        print("   提交登录表单...")
        submit_button.click()
        
        # 等待响应
        time.sleep(3)
        
        # 检查是否有错误消息
        try:
            error_elements = driver.find_elements(By.CSS_SELECTOR, '[role="alert"], .error, .text-red-500')
            if error_elements:
                for error in error_elements:
                    if error.text.strip():
                        print(f"   ❌ 发现错误消息: {error.text}")
                        return False
        except:
            pass
        
        # 检查是否跳转到仪表板
        current_url = driver.current_url
        print(f"   当前URL: {current_url}")
        
        if '/dashboard' in current_url or current_url != 'http://localhost:3000/auth/login':
            print("   ✅ 登录成功，页面已跳转")
            return True
        else:
            print("   ⚠️  登录可能失败，页面未跳转")
            
            # 检查页面源码中的错误信息
            page_source = driver.page_source
            if '400' in page_source or 'Bad Request' in page_source:
                print("   ❌ 发现400错误")
                return False
            elif '登录失败' in page_source or 'login failed' in page_source.lower():
                print("   ❌ 发现登录失败消息")
                return False
            else:
                print("   ⚠️  未明确的登录状态")
                return False
                
    except TimeoutException:
        print("   ❌ 页面加载超时")
        return False
    except Exception as e:
        print(f"   ❌ 前端测试失败: {e}")
        return False

def test_api_proxy():
    """测试前端API代理"""
    print("🔍 测试前端API代理...")
    
    try:
        response = requests.post(
            'http://localhost:3000/api/auth/login/',
            json={
                'email': '<EMAIL>',
                'password': 'admin123456'
            },
            timeout=10
        )
        
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            print("   ✅ 前端API代理正常")
            return True
        else:
            print(f"   ❌ 前端API代理异常: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ 前端API代理请求失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始登录功能集成测试...")
    print("=" * 60)
    
    # 测试后端API
    backend_ok = test_backend_api()
    
    # 测试前端API代理
    proxy_ok = test_api_proxy()
    
    # 设置浏览器驱动
    driver = setup_driver()
    if not driver:
        print("❌ 无法设置浏览器驱动，跳过前端测试")
        frontend_ok = False
    else:
        try:
            # 测试前端登录
            frontend_ok = test_frontend_login(driver)
        finally:
            driver.quit()
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("📊 集成测试结果:")
    print(f"   后端API: {'✅ 通过' if backend_ok else '❌ 失败'}")
    print(f"   前端代理: {'✅ 通过' if proxy_ok else '❌ 失败'}")
    print(f"   前端登录: {'✅ 通过' if frontend_ok else '❌ 失败'}")
    
    if all([backend_ok, proxy_ok, frontend_ok]):
        print("\n🎉 所有测试通过！登录功能正常工作")
        return True
    else:
        print("\n⚠️  部分测试失败，需要进一步调试")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
