#!/bin/bash

cd ..

# 端口切换功能测试脚本
# 测试不同端口配置下的前后端通信

set -e

echo "🧪 开始端口切换功能测试..."

# 测试函数
test_api_connection() {
    local backend_port=$1
    local frontend_port=$2
    
    echo "📡 测试后端API连接 (端口: $backend_port)..."
    
    # 等待服务启动
    sleep 5
    
    # 测试后端API
    if curl -s "http://localhost:$backend_port/api/schema/" > /dev/null; then
        echo "✅ 后端API正常 (端口: $backend_port)"
    else
        echo "❌ 后端API连接失败 (端口: $backend_port)"
        return 1
    fi
    
    # 测试前端页面
    if curl -s "http://localhost:$frontend_port/" > /dev/null; then
        echo "✅ 前端页面正常 (端口: $frontend_port)"
    else
        echo "❌ 前端页面连接失败 (端口: $frontend_port)"
        return 1
    fi
    
    return 0
}

# 停止现有服务
echo "🛑 停止现有服务..."
pkill -f "manage.py runserver" || true
pkill -f "next dev" || true
sleep 2

# 测试1: 默认端口
echo ""
echo "🔍 测试1: 默认端口 (前端:3000, 后端:8000)"
./start-dev.sh > /dev/null 2>&1 &
START_PID=$!

if test_api_connection 8000 3000; then
    echo "✅ 默认端口测试通过"
else
    echo "❌ 默认端口测试失败"
fi

# 停止服务
kill $START_PID 2>/dev/null || true
pkill -f "manage.py runserver" || true
pkill -f "next dev" || true
sleep 3

# 测试2: 自定义端口
echo ""
echo "🔍 测试2: 自定义端口 (前端:9331, 后端:8001)"
./start-dev.sh --frontend-port 9331 --backend-port 8001 > /dev/null 2>&1 &
START_PID=$!

if test_api_connection 8001 9331; then
    echo "✅ 自定义端口测试通过"
else
    echo "❌ 自定义端口测试失败"
fi

# 停止服务
kill $START_PID 2>/dev/null || true
pkill -f "manage.py runserver" || true
pkill -f "next dev" || true

echo ""
echo "🎉 端口切换功能测试完成！"
echo ""
echo "💡 提示: 如果测试失败，请检查："
echo "   - Docker服务是否正常运行"
echo "   - 端口是否被其他进程占用"
echo "   - 网络连接是否正常"
