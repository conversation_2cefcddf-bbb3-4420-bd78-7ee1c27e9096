#!/bin/bash

cd ..

# 修复前端权限问题并启动前端服务

echo "🔧 修复前端权限问题..."

# 检查 .next 目录是否存在且属于 root
if [ -d "frontend/.next" ] && [ "$(stat -c %U frontend/.next)" = "root" ]; then
    echo "⚠️  检测到 .next 目录属于 root 用户，需要删除并重新创建"
    
    # 尝试删除 .next 目录
    if sudo rm -rf frontend/.next 2>/dev/null; then
        echo "✅ 成功删除旧的 .next 目录"
    else
        echo "❌ 无法删除 .next 目录，请手动运行: sudo rm -rf frontend/.next"
        exit 1
    fi
fi

echo "🚀 启动前端服务..."
cd frontend

# 确保依赖已安装
if [ ! -d "node_modules" ]; then
    echo "📦 安装前端依赖..."
    npm install
fi

# 启动开发服务器
echo "🌐 启动 Next.js 开发服务器..."
npm run dev
