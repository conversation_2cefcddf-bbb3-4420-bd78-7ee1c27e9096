#!/usr/bin/env python3
"""
简化的登录功能测试脚本
"""

import requests
import json
import time

def test_backend_direct():
    """直接测试后端API"""
    print("🔍 测试后端API...")
    
    try:
        response = requests.post(
            'http://localhost:8000/api/auth/login/',
            json={
                'email': '<EMAIL>',
                'password': 'admin123'
            },
            timeout=10
        )
        
        print(f"   状态码: {response.status_code}")
        print(f"   响应头: {dict(response.headers)}")
        print(f"   响应内容: {response.text}")
        
        if response.status_code == 200:
            print("   ✅ 后端API正常")
            return True
        else:
            print(f"   ❌ 后端API异常: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ 后端API请求失败: {e}")
        return False

def test_frontend_proxy():
    """测试前端API代理"""
    print("🔍 测试前端API代理...")
    
    try:
        response = requests.post(
            'http://localhost:3000/api/auth/login/',
            json={
                'email': '<EMAIL>',
                'password': 'admin123'
            },
            timeout=10
        )
        
        print(f"   状态码: {response.status_code}")
        print(f"   响应头: {dict(response.headers)}")
        print(f"   响应内容: {response.text}")
        
        if response.status_code == 200:
            print("   ✅ 前端API代理正常")
            return True
        else:
            print(f"   ❌ 前端API代理异常: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ 前端API代理请求失败: {e}")
        return False

def test_frontend_page():
    """测试前端登录页面是否可访问"""
    print("🔍 测试前端登录页面...")
    
    try:
        response = requests.get('http://localhost:3000/auth/login', timeout=10)
        
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("   ✅ 前端登录页面可访问")
            return True
        else:
            print(f"   ❌ 前端登录页面异常: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ 前端页面请求失败: {e}")
        return False

def test_user_exists():
    """测试用户是否存在"""
    print("🔍 检查用户数据...")
    
    import os
    import sys
    
    # 添加Django项目路径
    sys.path.append('/home/<USER>/Desktop/program/Mentia/backend')
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'mentia_backend.settings')
    
    try:
        import django
        django.setup()
        
        from apps.users.models import User
        
        user = User.objects.filter(email='<EMAIL>').first()
        if user:
            print(f"   ✅ 用户存在: {user.email}")
            print(f"   用户ID: {user.user_id}")
            print(f"   是否激活: {user.is_active}")
            print(f"   密码已设置: {bool(user.password)}")
            
            # 测试密码验证
            if user.check_password('admin123'):
                print("   ✅ 密码验证正确")
                return True
            else:
                print("   ❌ 密码验证失败")
                return False
        else:
            print("   ❌ 用户不存在")
            return False
            
    except Exception as e:
        print(f"   ❌ 数据库检查失败: {e}")
        return False

def test_performance():
    """测试前端性能"""
    print("🔍 测试前端性能...")
    
    try:
        # 测试首页加载时间
        start_time = time.time()
        response = requests.get('http://localhost:3000/', timeout=30)
        load_time = time.time() - start_time
        
        print(f"   首页加载时间: {load_time:.2f}秒")
        
        if load_time < 5:
            print("   ✅ 首页加载性能良好")
        else:
            print("   ⚠️  首页加载较慢")
        
        # 测试登录页面加载时间
        start_time = time.time()
        response = requests.get('http://localhost:3000/auth/login', timeout=30)
        load_time = time.time() - start_time
        
        print(f"   登录页面加载时间: {load_time:.2f}秒")
        
        if load_time < 3:
            print("   ✅ 登录页面加载性能良好")
            return True
        else:
            print("   ⚠️  登录页面加载较慢")
            return False
            
    except Exception as e:
        print(f"   ❌ 性能测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始登录功能测试...")
    print("=" * 50)
    
    # 测试用户数据
    user_ok = test_user_exists()
    
    # 测试后端API
    backend_ok = test_backend_direct()
    
    # 测试前端代理
    proxy_ok = test_frontend_proxy()
    
    # 测试前端页面
    page_ok = test_frontend_page()
    
    # 测试性能
    perf_ok = test_performance()
    
    print("\n" + "=" * 50)
    print("📊 测试结果:")
    print(f"   用户数据: {'✅ 正常' if user_ok else '❌ 异常'}")
    print(f"   后端API: {'✅ 正常' if backend_ok else '❌ 异常'}")
    print(f"   前端代理: {'✅ 正常' if proxy_ok else '❌ 异常'}")
    print(f"   前端页面: {'✅ 正常' if page_ok else '❌ 异常'}")
    print(f"   前端性能: {'✅ 良好' if perf_ok else '⚠️  需优化'}")
    
    if all([user_ok, backend_ok, proxy_ok, page_ok]):
        print("\n🎉 核心功能测试通过！")
        print("💡 建议：在浏览器中手动测试登录功能以确认完整流程")
        return True
    else:
        print("\n⚠️  部分测试失败，需要进一步调试")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
