#!/usr/bin/env python3
"""
测试修复脚本
验证所有修复是否正常工作
"""

import requests
import json
import sys
import time

# 配置
BACKEND_URL = "http://localhost:8000"
FRONTEND_URL = "http://localhost:3000"

def test_backend_health():
    """测试后端健康状态"""
    try:
        # 使用实际存在的端点测试
        response = requests.get(f"{BACKEND_URL}/api/auth/login/", timeout=5)
        if response.status_code in [200, 405]:  # GET请求可能返回405 Method Not Allowed
            print("✅ 后端服务正常运行")
            return True
        else:
            print(f"❌ 后端服务异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 后端服务连接失败: {e}")
        return False

def test_frontend_health():
    """测试前端健康状态"""
    try:
        response = requests.get(FRONTEND_URL, timeout=15)
        if response.status_code == 200:
            print("✅ 前端服务正常运行")
            return True
        else:
            print(f"❌ 前端服务异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 前端服务连接失败: {e}")
        return False

def get_auth_token():
    """获取认证token"""
    try:
        login_data = {
            "email": "<EMAIL>",
            "password": "admin123"
        }
        response = requests.post(f"{BACKEND_URL}/api/auth/login/", json=login_data, timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"登录响应: {data}")
            token = data.get('access') or data.get('access_token') or data.get('token')
            if not token and 'tokens' in data:
                token = data['tokens'].get('access')
            if token:
                print("✅ 用户认证成功")
                return token
            else:
                print("❌ 认证响应中没有token")
                print(f"响应数据: {data}")
                return None
        else:
            print(f"❌ 用户认证失败: {response.status_code}")
            print(f"响应: {response.text}")
            return None
    except Exception as e:
        print(f"❌ 认证请求失败: {e}")
        return None

def test_profile_api(token):
    """测试个人资料API"""
    try:
        headers = {"Authorization": f"Bearer {token}"}
        response = requests.get(f"{BACKEND_URL}/api/auth/profile/complete/", headers=headers, timeout=10)
        if response.status_code == 200:
            data = response.json()
            print("✅ 个人资料API正常工作")
            print(f"   用户邮箱: {data.get('email', 'N/A')}")
            print(f"   用户ID: {data.get('user_id', 'N/A')}")
            return True
        else:
            print(f"❌ 个人资料API异常: {response.status_code}")
            print(f"响应: {response.text}")
            return False
    except Exception as e:
        print(f"❌ 个人资料API请求失败: {e}")
        return False

def test_memory_search_api(token):
    """测试记忆搜索API"""
    try:
        headers = {"Authorization": f"Bearer {token}"}
        search_data = {
            "query": "学习编程",
            "limit": 3
        }
        response = requests.post(f"{BACKEND_URL}/api/ai/search-memories/", 
                               json=search_data, headers=headers, timeout=15)
        if response.status_code == 200:
            data = response.json()
            memories = data.get('memories', [])
            print(f"✅ 记忆搜索API正常工作")
            print(f"   找到 {len(memories)} 条相关记忆")
            for i, memory in enumerate(memories[:2]):  # 只显示前2条
                print(f"   记忆{i+1}: {memory.get('type', 'N/A')} - {memory.get('title', 'N/A')}")
            return True
        else:
            print(f"❌ 记忆搜索API异常: {response.status_code}")
            print(f"响应: {response.text}")
            return False
    except Exception as e:
        print(f"❌ 记忆搜索API请求失败: {e}")
        return False

def test_ai_chat_api(token):
    """测试AI聊天API"""
    try:
        headers = {"Authorization": f"Bearer {token}"}
        chat_data = {
            "messages": [
                {"role": "user", "content": "你好，我是谁？"}
            ],
            "temperature": 0.7,
            "max_tokens": 100
        }
        response = requests.post(f"{BACKEND_URL}/api/ai/chat/", 
                               json=chat_data, headers=headers, timeout=30)
        if response.status_code == 200:
            data = response.json()
            content = data.get('content', '')
            if content:
                print("✅ AI聊天API正常工作")
                print(f"   AI回复: {content[:100]}...")
                return True
            else:
                print("❌ AI聊天API返回空内容")
                return False
        else:
            print(f"❌ AI聊天API异常: {response.status_code}")
            print(f"响应: {response.text}")
            return False
    except Exception as e:
        print(f"❌ AI聊天API请求失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🔍 开始测试修复效果...")
    print("=" * 50)
    
    # 测试服务健康状态
    backend_ok = test_backend_health()
    frontend_ok = test_frontend_health()

    if not backend_ok:
        print("\n❌ 后端服务未运行，请先启动后端服务")
        sys.exit(1)

    if not frontend_ok:
        print("\n⚠️  前端服务可能未完全启动，但继续测试后端API")
    
    print("\n🔐 测试用户认证...")
    token = get_auth_token()
    if not token:
        print("\n❌ 无法获取认证token，请检查用户账号")
        sys.exit(1)
    
    print("\n📋 测试个人资料API...")
    profile_ok = test_profile_api(token)
    
    print("\n🧠 测试记忆搜索API...")
    memory_ok = test_memory_search_api(token)
    
    print("\n💬 测试AI聊天API...")
    chat_ok = test_ai_chat_api(token)
    
    print("\n" + "=" * 50)
    print("📊 测试结果总结:")
    print(f"   后端服务: {'✅' if backend_ok else '❌'}")
    print(f"   前端服务: {'✅' if frontend_ok else '❌'}")
    print(f"   个人资料API: {'✅' if profile_ok else '❌'}")
    print(f"   记忆搜索API: {'✅' if memory_ok else '❌'}")
    print(f"   AI聊天API: {'✅' if chat_ok else '❌'}")
    
    all_ok = all([backend_ok, frontend_ok, profile_ok, memory_ok, chat_ok])
    
    if all_ok:
        print("\n🎉 所有测试通过！修复成功！")
        return 0
    else:
        print("\n⚠️  部分测试失败，请检查相关服务")
        return 1

if __name__ == "__main__":
    sys.exit(main())
