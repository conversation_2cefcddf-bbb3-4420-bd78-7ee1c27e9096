#!/bin/bash

# =============================================================================
# 灵境 (Mentia) 统一测试运行脚本
# =============================================================================
#
# 脚本功能：
#   统一运行前端和后端的所有测试，提供完整的测试报告
#
# 主要功能：
#   1. 前端测试 - 运行Jest测试套件
#   2. 后端测试 - 运行Django测试套件
#   3. 集成测试 - 运行端到端测试
#   4. 测试报告 - 生成统一的测试报告
#
# 使用方法：
#   ./test/run_all_tests.sh                    # 运行所有测试
#   ./test/run_all_tests.sh --frontend         # 只运行前端测试
#   ./test/run_all_tests.sh --backend          # 只运行后端测试
#   ./test/run_all_tests.sh --coverage         # 生成覆盖率报告
#
# 作者: Mentia开发团队
# 创建时间: 2025-09-06
# 版本: v1.0 - 统一测试目录支持
#
# TODO: 添加并行测试执行
# TODO: 支持测试结果缓存
# TODO: 添加性能测试支持
# =============================================================================

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${CYAN}[信息]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[成功]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

log_error() {
    echo -e "${RED}[错误]${NC} $1"
}

log_frontend() {
    echo -e "${BLUE}[前端测试]${NC} $1"
}

log_backend() {
    echo -e "${PURPLE}[后端测试]${NC} $1"
}

# 解析命令行参数
FRONTEND_ONLY=false
BACKEND_ONLY=false
COVERAGE=false
VERBOSE=false

while [[ $# -gt 0 ]]; do
  case $1 in
    --frontend)
      FRONTEND_ONLY=true
      shift
      ;;
    --backend)
      BACKEND_ONLY=true
      shift
      ;;
    --coverage)
      COVERAGE=true
      shift
      ;;
    --verbose)
      VERBOSE=true
      shift
      ;;
    -h|--help)
      echo "用法: $0 [选项]"
      echo "选项:"
      echo "  --frontend     只运行前端测试"
      echo "  --backend      只运行后端测试"
      echo "  --coverage     生成覆盖率报告"
      echo "  --verbose      详细输出"
      echo "  -h, --help     显示此帮助信息"
      exit 0
      ;;
    *)
      echo "未知选项: $1"
      echo "使用 -h 或 --help 查看帮助"
      exit 1
      ;;
  esac
done

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

log_info "🧪 开始运行灵境 (Mentia) 测试套件..."
log_info "📁 项目根目录: $PROJECT_ROOT"
log_info "📁 测试目录: $SCRIPT_DIR"

# 创建测试结果目录
mkdir -p "$SCRIPT_DIR/results"

# 测试结果变量
FRONTEND_RESULT=0
BACKEND_RESULT=0
INTEGRATION_RESULT=0

# 运行前端测试
run_frontend_tests() {
    log_frontend "🚀 开始运行前端测试..."
    
    cd "$PROJECT_ROOT/frontend"
    
    if [ "$COVERAGE" = true ]; then
        log_frontend "📊 运行前端测试并生成覆盖率报告..."
        if npm run test:coverage; then
            log_success "✅ 前端测试通过（包含覆盖率）"
            FRONTEND_RESULT=0
        else
            log_error "❌ 前端测试失败"
            FRONTEND_RESULT=1
        fi
    else
        log_frontend "🧪 运行前端测试..."
        if npm test -- --watchAll=false; then
            log_success "✅ 前端测试通过"
            FRONTEND_RESULT=0
        else
            log_error "❌ 前端测试失败"
            FRONTEND_RESULT=1
        fi
    fi
    
    cd "$PROJECT_ROOT"
}

# 运行后端测试
run_backend_tests() {
    log_backend "🚀 开始运行后端测试..."
    
    cd "$SCRIPT_DIR/backend"
    
    if [ "$COVERAGE" = true ]; then
        log_backend "📊 运行后端测试并生成覆盖率报告..."
        if python run_tests.py --coverage; then
            log_success "✅ 后端测试通过（包含覆盖率）"
            BACKEND_RESULT=0
        else
            log_error "❌ 后端测试失败"
            BACKEND_RESULT=1
        fi
    else
        log_backend "🧪 运行后端测试..."
        if python run_tests.py; then
            log_success "✅ 后端测试通过"
            BACKEND_RESULT=0
        else
            log_error "❌ 后端测试失败"
            BACKEND_RESULT=1
        fi
    fi
    
    cd "$PROJECT_ROOT"
}

# 运行集成测试
run_integration_tests() {
    log_info "🔗 开始运行集成测试..."
    
    # 运行现有的集成测试脚本
    if [ -f "$SCRIPT_DIR/test_login_integration.py" ]; then
        log_info "🧪 运行登录集成测试..."
        if python "$SCRIPT_DIR/test_login_integration.py"; then
            log_success "✅ 集成测试通过"
            INTEGRATION_RESULT=0
        else
            log_error "❌ 集成测试失败"
            INTEGRATION_RESULT=1
        fi
    else
        log_warning "⚠️  未找到集成测试文件，跳过集成测试"
        INTEGRATION_RESULT=0
    fi
}

# 生成测试报告
generate_report() {
    log_info "📋 生成测试报告..."
    
    REPORT_FILE="$SCRIPT_DIR/results/test_report_$(date +%Y%m%d_%H%M%S).txt"
    
    {
        echo "=============================================="
        echo "灵境 (Mentia) 测试报告"
        echo "=============================================="
        echo "生成时间: $(date)"
        echo "项目根目录: $PROJECT_ROOT"
        echo ""
        echo "测试结果摘要:"
        echo "=============================================="
        
        if [ "$FRONTEND_ONLY" = false ] && [ "$BACKEND_ONLY" = false ]; then
            echo "前端测试: $([ $FRONTEND_RESULT -eq 0 ] && echo "✅ 通过" || echo "❌ 失败")"
            echo "后端测试: $([ $BACKEND_RESULT -eq 0 ] && echo "✅ 通过" || echo "❌ 失败")"
            echo "集成测试: $([ $INTEGRATION_RESULT -eq 0 ] && echo "✅ 通过" || echo "❌ 失败")"
        elif [ "$FRONTEND_ONLY" = true ]; then
            echo "前端测试: $([ $FRONTEND_RESULT -eq 0 ] && echo "✅ 通过" || echo "❌ 失败")"
        elif [ "$BACKEND_ONLY" = true ]; then
            echo "后端测试: $([ $BACKEND_RESULT -eq 0 ] && echo "✅ 通过" || echo "❌ 失败")"
        fi
        
        echo ""
        echo "总体结果: $([ $((FRONTEND_RESULT + BACKEND_RESULT + INTEGRATION_RESULT)) -eq 0 ] && echo "✅ 所有测试通过" || echo "❌ 存在测试失败")"
        echo "=============================================="
    } > "$REPORT_FILE"
    
    log_info "📄 测试报告已保存到: $REPORT_FILE"
    
    # 显示报告内容
    if [ "$VERBOSE" = true ]; then
        cat "$REPORT_FILE"
    fi
}

# 主执行逻辑
main() {
    # 检查依赖
    if [ "$FRONTEND_ONLY" = false ] && [ "$BACKEND_ONLY" = false ]; then
        # 运行所有测试
        run_frontend_tests
        run_backend_tests
        run_integration_tests
    elif [ "$FRONTEND_ONLY" = true ]; then
        # 只运行前端测试
        run_frontend_tests
    elif [ "$BACKEND_ONLY" = true ]; then
        # 只运行后端测试
        run_backend_tests
    fi
    
    # 生成报告
    generate_report
    
    # 返回总体结果
    TOTAL_RESULT=$((FRONTEND_RESULT + BACKEND_RESULT + INTEGRATION_RESULT))
    
    if [ $TOTAL_RESULT -eq 0 ]; then
        log_success "🎉 所有测试都通过了！"
        exit 0
    else
        log_error "💥 存在测试失败，请检查测试报告"
        exit 1
    fi
}

# 运行主函数
main
