'use client'

import { useState } from 'react'
import { PlusIcon, XMarkIcon } from '@heroicons/react/24/outline'

export default function GrowthPageTest() {
  const [showCreateModal, setShowCreateModal] = useState(false)

  const handleButtonClick = () => {
    console.log('🔥 按钮被点击了！')
    console.log('当前状态:', showCreateModal)
    setShowCreateModal(true)
    console.log('设置状态为 true')
  }

  return (
    <div className="space-y-8">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">成长引擎测试</h1>
        <button
          onClick={handleButtonClick}
          className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
        >
          <PlusIcon className="h-5 w-5 mr-2" />
          创建新项目
        </button>
      </div>

      <div className="bg-white p-4 rounded-lg shadow">
        <p>当前模态框状态: {showCreateModal ? '显示' : '隐藏'}</p>
      </div>

      {/* 模态框 */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold">测试模态框</h3>
              <button
                onClick={() => {
                  console.log('关闭按钮被点击')
                  setShowCreateModal(false)
                }}
                className="text-gray-400 hover:text-gray-600"
              >
                <XMarkIcon className="h-6 w-6" />
              </button>
            </div>
            <div className="text-center">
              <p className="mb-4">模态框成功显示！</p>
              <button
                onClick={() => setShowCreateModal(false)}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                关闭
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
