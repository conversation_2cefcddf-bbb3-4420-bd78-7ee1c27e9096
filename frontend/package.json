{"name": "mentia-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "NODE_OPTIONS='--max-old-space-size=4096' NEXT_TELEMETRY_DISABLED=1 next dev --turbo", "dev:fast": "NODE_OPTIONS='--max-old-space-size=4096' NEXT_TELEMETRY_DISABLED=1 next dev --turbo --port ${PORT:-3000}", "dev:legacy": "NODE_OPTIONS='--max-old-space-size=4096' next dev", "dev:precompile": "npm run precompile && next dev --turbo", "build": "next build", "build:analyze": "ANALYZE=true next build", "start": "next start", "lint": "next lint --cache", "lint:fix": "next lint --fix --cache", "type-check": "tsc --noEmit --incremental", "clean": "rm -rf .next .next-build node_modules/.cache .eslintcache", "clean:all": "npm run clean && rm -rf node_modules package-lock.json", "precompile": "node scripts/precompile.js", "predev": "echo 'Starting optimized development server...'", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false"}, "dependencies": {"@headlessui/react": "^2.2.0", "@heroicons/react": "^2.1.5", "@hookform/resolvers": "^3.9.1", "@types/js-cookie": "^3.0.6", "@types/node": "^22.8.7", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "autoprefixer": "^10.4.20", "axios": "^1.7.7", "clsx": "^2.1.1", "critters": "^0.0.25", "date-fns": "^4.1.0", "js-cookie": "^3.0.5", "next": "^14.2.15", "postcss": "^8.4.49", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.53.2", "react-hot-toast": "^2.4.1", "tailwindcss": "^3.4.14", "typescript": "^5.6.3", "zod": "^3.23.8"}, "devDependencies": {"@emotion/jest": "^11.13.0", "@tailwindcss/forms": "^0.5.9", "@tailwindcss/typography": "^0.5.15", "@testing-library/jest-dom": "^6.8.0", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "eslint": "^8.57.1", "eslint-config-next": "^14.2.15", "jest": "^30.1.3", "jest-environment-jsdom": "^30.1.2", "jest-junit": "^16.0.0", "node-notifier": "^10.0.1", "prettier": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.8", "ts-jest": "^29.4.1"}}