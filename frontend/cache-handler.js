/**
 * Next.js增量缓存处理器 - 兼容性修复版本
 *
 * 功能：
 * - 优化开发环境编译性能
 * - 减少重复编译时间
 * - 智能缓存管理
 * - 修复Vercel相关兼容性问题
 */

const { IncrementalCache } = require('next/dist/server/lib/incremental-cache')

class CustomIncrementalCache extends IncrementalCache {
  constructor(options) {
    // 确保options存在并设置默认值
    const safeOptions = {
      ...options,
      dev: options?.dev || process.env.NODE_ENV === 'development'
    }

    try {
      super(safeOptions)
      this.dev = safeOptions.dev
    } catch (error) {
      console.warn('缓存处理器初始化失败，使用默认缓存:', error.message)
      // 如果初始化失败，返回null让Next.js使用默认缓存
      return null
    }
  }

  async get(key, fetchCache) {
    try {
      // 开发环境下使用更激进的缓存策略
      if (this.dev) {
        const cached = await super.get(key, fetchCache)
        if (cached && cached.value) {
          // 延长缓存时间
          cached.revalidateAfter = Date.now() + 60 * 1000 // 60秒
        }
        return cached
      }

      return super.get(key, fetchCache)
    } catch (error) {
      console.warn('缓存获取失败，使用默认行为:', error.message)
      return super.get(key, fetchCache)
    }
  }

  async set(key, data, ctx) {
    try {
      // 开发环境下设置更长的缓存时间
      if (this.dev && data && typeof data === 'object') {
        data.revalidateAfter = Date.now() + 60 * 1000 // 60秒
      }

      return super.set(key, data, ctx)
    } catch (error) {
      console.warn('缓存设置失败，使用默认行为:', error.message)
      return super.set(key, data, ctx)
    }
  }
}

module.exports = CustomIncrementalCache
