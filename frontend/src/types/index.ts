/**
 * 灵境 (Mentia) 前端类型定义
 */

// 用户相关类型
export interface User {
  user_id: string;
  email: string;
  username?: string;
  first_name?: string;
  last_name?: string;
  created_at: string;
  updated_at: string;
}

export interface UserRegistration {
  email: string;
  username?: string;
  password: string;
  password_confirm: string;
}

export interface UserLogin {
  email: string;
  password: string;
}

export interface AuthTokens {
  access: string;
  refresh: string;
}

export interface AuthResponse {
  message: string;
  user: User;
  tokens: AuthTokens;
}

// 成长项相关类型
export type GrowthItemStatus = 'future_plan' | 'in_progress' | 'completed';
export type GrowthItemType = 'goal' | 'task';
export type CreatedBy = 'user' | 'ai';

export interface Tag {
  tag_id: number;
  name: string;
  category: string;
}

export interface GrowthItem {
  item_id: string;
  title: string;
  description?: string;
  item_type: GrowthItemType;
  status: GrowthItemStatus;
  created_by: CreatedBy;
  ai_summary?: string;
  parent?: string;
  created_at: string;
  started_at?: string;
  completed_at?: string;
  tags: Tag[];
  children_count?: number;
  completion_rate?: number;
  children?: GrowthItem[];  // 子任务列表
}

export interface GrowthItemCreate {
  title: string;
  description?: string;
  item_type: GrowthItemType;
  status: GrowthItemStatus;
  parent?: string;
  tag_ids?: number[];
}

export interface GrowthStats {
  total_items: number;
  future_plans: number;
  in_progress: number;
  completed: number;
  total_goals: number;
  completed_goals: number;
  total_tasks: number;
  completed_tasks: number;
  completion_rate: number;
  most_used_tags: Array<{
    name: string;
    category: string;
    usage_count: number;
  }>;
}

// 用户价值观类型
export interface UserValue {
  value_id: string;
  title: string;
  description?: string;
  priority: number;
  created_at: string;
  updated_at: string;
}

// 用户统计类型
export interface UserStats {
  total_goals: number;
  completed_goals: number;
  total_tasks: number;
  completed_tasks: number;
  journal_entries_count: number;
  total_tags: number;
  join_days: number;
}

// API响应类型
export interface ApiResponse<T = any> {
  data?: T;
  message?: string;
  error?: string;
}

export interface PaginatedResponse<T> {
  count: number;
  next?: string;
  previous?: string;
  results: T[];
}

// 表单相关类型
export interface FormError {
  field: string;
  message: string;
}

// 通用UI类型
export interface SelectOption {
  value: string | number;
  label: string;
}

export interface MenuItem {
  id: string;
  label: string;
  href: string;
  icon?: React.ComponentType<any>;
  badge?: string | number;
}

// 主题相关类型
export type Theme = 'light' | 'dark' | 'system';

// 通知类型
export type NotificationType = 'success' | 'error' | 'warning' | 'info';

export interface Notification {
  id: string;
  type: NotificationType;
  title: string;
  message?: string;
  duration?: number;
}

// 博客相关类型
export interface BlogCategory {
  category_id: number;
  name: string;
  slug: string;
  description: string;
  color: string;
  post_count: number;
  created_at: string;
}

export interface BlogTag {
  tag_id: number;
  name: string;
  slug: string;
  color: string;
  post_count: number;
  created_at: string;
}

export interface BlogPost {
  post_id: string;
  title: string;
  slug: string;
  content: string;
  excerpt: string;
  status: 'draft' | 'published' | 'archived';
  featured_image: string;
  meta_description: string;
  meta_keywords: string;
  author: User;
  category: BlogCategory | null;
  tags: BlogTag[];
  view_count: number;
  like_count: number;
  reading_time: number;
  comment_count: number;
  published_at: string | null;
  created_at: string;
  updated_at: string;
}

export interface BlogPostCreate {
  title: string;
  slug: string;
  content: string;
  excerpt?: string;
  status: 'draft' | 'published' | 'archived';
  category?: number | null;
  tag_ids?: number[];
  featured_image?: string;
  meta_description?: string;
  meta_keywords?: string;
}

export interface BlogComment {
  comment_id: string;
  content: string;
  author: User;
  parent: string | null;
  reply_count: number;
  is_approved: boolean;
  created_at: string;
  updated_at: string;
}

export interface BlogStats {
  total_posts: number;
  published_posts: number;
  draft_posts: number;
  total_views: number;
  total_likes: number;
  total_comments: number;
  categories_count: number;
  tags_count: number;
}
