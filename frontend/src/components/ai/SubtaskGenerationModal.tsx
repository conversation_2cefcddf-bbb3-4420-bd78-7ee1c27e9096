'use client'

import { useState } from 'react'
import { createPortal } from 'react-dom'
import { XMarkIcon, SparklesIcon } from '@heroicons/react/24/outline'
import { toast } from 'react-hot-toast'
import { generateSubtasks, type Subtask } from '@/lib/ai-api'

interface SubtaskGenerationModalProps {
  isOpen: boolean
  onClose: () => void
  projectTitle: string
  projectDescription?: string
  parentItemId?: string
  onSubtasksGenerated: (response: any) => void
}

export default function SubtaskGenerationModal({
  isOpen,
  onClose,
  projectTitle,
  projectDescription = '',
  parentItemId,
  onSubtasksGenerated
}: SubtaskGenerationModalProps) {
  const [isGenerating, setIsGenerating] = useState(false)
  const [generatedSubtasks, setGeneratedSubtasks] = useState<Subtask[]>([])
  const [showResults, setShowResults] = useState(false)

  const handleGenerate = async () => {
    if (!projectTitle.trim()) {
      toast.error('项目标题不能为空')
      return
    }

    setIsGenerating(true)
    try {
      const response = await generateSubtasks({
        project_title: projectTitle,
        project_description: projectDescription,
        parent_item_id: parentItemId
      })

      setGeneratedSubtasks(response.subtasks)
      setShowResults(true)
      toast.success(response.message)
    } catch (error) {
      console.error('生成子任务失败:', error)
      toast.error('AI服务暂时不可用，请稍后重试')
    } finally {
      setIsGenerating(false)
    }
  }

  const handleConfirm = () => {
    // 传递完整的响应数据，包括生成的子任务和其他信息
    const response = {
      subtasks: generatedSubtasks,
      parent_updated: !!parentItemId,
      message: "子任务生成成功"
    }
    onSubtasksGenerated(response)
    onClose()
    setShowResults(false)
    setGeneratedSubtasks([])
  }

  const handleClose = () => {
    onClose()
    setShowResults(false)
    setGeneratedSubtasks([])
  }

  if (!isOpen) return null

  const modalContent = (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      {/* 背景遮罩 */}
      <div
        className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
        onClick={handleClose}
      />
      
      {/* 模态框容器 */}
      <div className="flex min-h-screen items-center justify-center p-4">
        <div className="relative w-full max-w-2xl bg-white rounded-lg shadow-xl transform transition-all z-10">
          {/* 头部 */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <div className="flex items-center space-x-3">
              <div className="flex-shrink-0">
                <SparklesIcon className="h-6 w-6 text-purple-600" />
              </div>
              <div>
                <h3 className="text-lg font-medium text-gray-900">
                  AI智能分解任务
                </h3>
                <p className="text-sm text-gray-500">
                  让AI帮你将大目标分解为可执行的小任务
                </p>
              </div>
            </div>
            <button
              onClick={handleClose}
              className="text-gray-400 hover:text-gray-500 transition-colors"
            >
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>

          {/* 内容区域 */}
          <div className="p-6">
            {!showResults ? (
              // 生成阶段
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    项目标题
                  </label>
                  <div className="p-3 bg-gray-50 rounded-md border">
                    <p className="text-gray-900">{projectTitle}</p>
                  </div>
                </div>

                {projectDescription && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      项目描述
                    </label>
                    <div className="p-3 bg-gray-50 rounded-md border">
                      <p className="text-gray-700">{projectDescription}</p>
                    </div>
                  </div>
                )}

                <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
                  <div className="flex">
                    <SparklesIcon className="h-5 w-5 text-blue-400 mt-0.5" />
                    <div className="ml-3">
                      <h4 className="text-sm font-medium text-blue-800">
                        AI将为您做什么
                      </h4>
                      <p className="text-sm text-blue-700 mt-1">
                        基于您的项目信息，AI将生成3-8个具体可执行的子任务，
                        帮助您快速开始项目并保持进度。
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              // 结果展示阶段
              <div className="space-y-4">
                <div className="flex items-center space-x-2 mb-4">
                  <SparklesIcon className="h-5 w-5 text-green-500" />
                  <h4 className="text-lg font-medium text-gray-900">
                    AI为您生成了 {generatedSubtasks.length} 个子任务
                  </h4>
                </div>

                <div className="space-y-3 max-h-96 overflow-y-auto">
                  {generatedSubtasks.map((subtask, index) => (
                    <div
                      key={index}
                      className="border border-gray-200 rounded-lg p-4 hover:border-gray-300 transition-colors"
                    >
                      <div className="flex items-start space-x-3">
                        <div className="flex-shrink-0 w-6 h-6 bg-purple-100 text-purple-600 rounded-full flex items-center justify-center text-sm font-medium">
                          {index + 1}
                        </div>
                        <div className="flex-1">
                          <h5 className="font-medium text-gray-900 mb-1">
                            {subtask.title}
                          </h5>
                          {subtask.description && (
                            <p className="text-sm text-gray-600">
                              {subtask.description}
                            </p>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* 底部按钮 */}
          <div className="flex justify-end space-x-3 p-6 border-t border-gray-200">
            <button
              onClick={handleClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
            >
              取消
            </button>
            {!showResults ? (
              <button
                onClick={handleGenerate}
                disabled={isGenerating}
                className="px-4 py-2 text-sm font-medium text-white bg-purple-600 border border-transparent rounded-md hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-2"
              >
                {isGenerating && (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                )}
                <SparklesIcon className="h-4 w-4" />
                <span>{isGenerating ? '生成中...' : '生成子任务'}</span>
              </button>
            ) : (
              <button
                onClick={handleConfirm}
                className="px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700 transition-colors"
              >
                确认使用这些子任务
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  )

  // 使用Portal渲染到页面根部
  const modalRoot = document.getElementById('modal-root')
  if (!modalRoot) {
    console.warn('Modal root element not found. Make sure to add <div id="modal-root" /> to your app.')
    return null
  }

  return createPortal(modalContent, modalRoot)
}
