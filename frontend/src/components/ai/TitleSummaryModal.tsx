'use client'

import { useState } from 'react'
import { createPortal } from 'react-dom'
import { XMarkIcon, SparklesIcon, DocumentTextIcon } from '@heroicons/react/24/outline'
import { toast } from 'react-hot-toast'
import { generateTitleSummary, type TitleSummaryRequest } from '@/lib/ai-api'

interface TitleSummaryModalProps {
  isOpen: boolean
  onClose: () => void
  content: string
  contentType: 'blog' | 'journal' | 'note'
  onGenerated: (title: string, summary: string) => void
}

export default function TitleSummaryModal({
  isOpen,
  onClose,
  content,
  contentType,
  onGenerated
}: TitleSummaryModalProps) {
  const [isGenerating, setIsGenerating] = useState(false)
  const [generatedTitle, setGeneratedTitle] = useState('')
  const [generatedSummary, setGeneratedSummary] = useState('')
  const [showResults, setShowResults] = useState(false)

  const contentTypeMap = {
    blog: '博客文章',
    journal: '日记',
    note: '笔记'
  }

  const handleGenerate = async () => {
    if (!content.trim()) {
      toast.error('内容不能为空')
      return
    }

    setIsGenerating(true)
    try {
      const response = await generateTitleSummary({
        content,
        content_type: contentType
      })

      setGeneratedTitle(response.title)
      setGeneratedSummary(response.summary)
      setShowResults(true)
      toast.success(response.message)
    } catch (error) {
      console.error('生成标题摘要失败:', error)
      toast.error('AI服务暂时不可用，请稍后重试')
    } finally {
      setIsGenerating(false)
    }
  }

  const handleConfirm = () => {
    onGenerated(generatedTitle, generatedSummary)
    onClose()
    setShowResults(false)
    setGeneratedTitle('')
    setGeneratedSummary('')
  }

  const handleClose = () => {
    onClose()
    setShowResults(false)
    setGeneratedTitle('')
    setGeneratedSummary('')
  }

  if (!isOpen) return null

  const modalContent = (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      {/* 背景遮罩 */}
      <div
        className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
        onClick={handleClose}
      />
      
      {/* 模态框容器 */}
      <div className="flex min-h-screen items-center justify-center p-4">
        <div className="relative w-full max-w-2xl bg-white rounded-lg shadow-xl transform transition-all z-10">
          {/* 头部 */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <div className="flex items-center space-x-3">
              <div className="flex-shrink-0">
                <SparklesIcon className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <h3 className="text-lg font-medium text-gray-900">
                  AI智能生成标题和摘要
                </h3>
                <p className="text-sm text-gray-500">
                  让AI为您的{contentTypeMap[contentType]}生成吸引人的标题和精准摘要
                </p>
              </div>
            </div>
            <button
              onClick={handleClose}
              className="text-gray-400 hover:text-gray-500 transition-colors"
            >
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>

          {/* 内容区域 */}
          <div className="p-6">
            {!showResults ? (
              // 生成阶段
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    内容预览
                  </label>
                  <div className="p-3 bg-gray-50 rounded-md border max-h-40 overflow-y-auto">
                    <p className="text-gray-700 text-sm whitespace-pre-wrap">
                      {content.length > 500 ? content.substring(0, 500) + '...' : content}
                    </p>
                  </div>
                  <p className="text-xs text-gray-500 mt-1">
                    内容长度: {content.length} 字符
                  </p>
                </div>

                <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
                  <div className="flex">
                    <SparklesIcon className="h-5 w-5 text-blue-400 mt-0.5" />
                    <div className="ml-3">
                      <h4 className="text-sm font-medium text-blue-800">
                        AI将为您做什么
                      </h4>
                      <p className="text-sm text-blue-700 mt-1">
                        基于您的{contentTypeMap[contentType]}内容，AI将生成一个简洁有力的标题
                        和100-200字的精准摘要，帮助您更好地整理和回顾内容。
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              // 结果展示阶段
              <div className="space-y-6">
                <div className="flex items-center space-x-2 mb-4">
                  <SparklesIcon className="h-5 w-5 text-green-500" />
                  <h4 className="text-lg font-medium text-gray-900">
                    AI生成结果
                  </h4>
                </div>

                {/* 生成的标题 */}
                <div className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center space-x-2 mb-3">
                    <DocumentTextIcon className="h-5 w-5 text-blue-500" />
                    <h5 className="font-medium text-gray-900">标题</h5>
                  </div>
                  <div className="bg-blue-50 border border-blue-200 rounded-md p-3">
                    <p className="text-gray-900 font-medium">{generatedTitle}</p>
                  </div>
                </div>

                {/* 生成的摘要 */}
                <div className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center space-x-2 mb-3">
                    <DocumentTextIcon className="h-5 w-5 text-green-500" />
                    <h5 className="font-medium text-gray-900">摘要</h5>
                  </div>
                  <div className="bg-green-50 border border-green-200 rounded-md p-3">
                    <p className="text-gray-700 leading-relaxed">{generatedSummary}</p>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* 底部按钮 */}
          <div className="flex justify-end space-x-3 p-6 border-t border-gray-200">
            <button
              onClick={handleClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
            >
              取消
            </button>
            {!showResults ? (
              <button
                onClick={handleGenerate}
                disabled={isGenerating}
                className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-2"
              >
                {isGenerating && (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                )}
                <SparklesIcon className="h-4 w-4" />
                <span>{isGenerating ? '生成中...' : '生成标题和摘要'}</span>
              </button>
            ) : (
              <div className="flex space-x-2">
                <button
                  onClick={handleGenerate}
                  disabled={isGenerating}
                  className="px-4 py-2 text-sm font-medium text-blue-700 bg-blue-50 border border-blue-200 rounded-md hover:bg-blue-100 transition-colors"
                >
                  重新生成
                </button>
                <button
                  onClick={handleConfirm}
                  className="px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700 transition-colors"
                >
                  使用这个结果
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )

  // 使用Portal渲染到页面根部
  const modalRoot = document.getElementById('modal-root')
  if (!modalRoot) {
    console.warn('Modal root element not found. Make sure to add <div id="modal-root" /> to your app.')
    return null
  }

  return createPortal(modalContent, modalRoot)
}
