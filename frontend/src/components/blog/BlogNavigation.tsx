'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { 
  DocumentTextIcon, 
  TagIcon, 
  FolderIcon,
  Cog6ToothIcon 
} from '@heroicons/react/24/outline'

export default function BlogNavigation() {
  const pathname = usePathname()

  const navItems = [
    {
      name: '文章',
      href: '/dashboard/blog',
      icon: DocumentTextIcon,
      description: '管理博客文章'
    },
    {
      name: '分类',
      href: '/dashboard/blog/categories',
      icon: FolderIcon,
      description: '管理文章分类'
    },
    {
      name: '标签',
      href: '/dashboard/blog/tags',
      icon: TagIcon,
      description: '管理文章标签'
    }
  ]

  return (
    <div className="clean-card p-4 mb-6">
      <div className="flex items-center gap-1">
        {navItems.map((item, index) => {
          const isActive = pathname === item.href
          const Icon = item.icon
          
          return (
            <div key={item.href} className="flex items-center">
              {index > 0 && (
                <span className="mx-2 text-gray-300">/</span>
              )}
              <Link
                href={item.href}
                className={`flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                  isActive
                    ? 'bg-primary-100 text-primary-700'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                }`}
                title={item.description}
              >
                <Icon className="h-4 w-4" />
                {item.name}
              </Link>
            </div>
          )
        })}
      </div>
    </div>
  )
}
