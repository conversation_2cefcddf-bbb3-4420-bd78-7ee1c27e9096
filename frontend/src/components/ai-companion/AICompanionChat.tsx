/**
 * AI伴侣聊天组件
 *
 * 功能说明：
 * - 提供与AI伴侣的实时对话界面
 * - 支持消息发送、接收和显示
 * - 集成记忆检索功能，基于用户历史数据提供个性化回复
 * - 自动滚动到最新消息
 * - 支持消息重新生成
 *
 * API交互：
 * - POST /ai/chat/ - 发送聊天消息
 * - POST /ai/search-memories/ - 搜索相关记忆
 *
 * 消息格式：
 * - role: 'system' | 'user' | 'assistant'
 * - content: 消息内容
 * - timestamp: 消息时间戳
 *
 * 特性：
 * - 基于用户成长数据的个性化对话
 * - 情感理解和支持
 * - 记忆检索增强回复质量
 *
 * TODO: 添加消息历史记录持久化
 * TODO: 添加多种AI人格模式
 * TODO: 添加语音输入/输出功能
 */
'use client'

import React, { useState, useRef, useEffect } from 'react'
import {
  PaperAirplaneIcon as Send,
  ChatBubbleLeftRightIcon as Bot,
  UserIcon as User,
  SparklesIcon as Sparkles,
  CpuChipIcon as Brain,
  HeartIcon as Heart
} from '@heroicons/react/24/outline'
import { post } from '@/lib/api'

interface Message {
  id: string
  type: 'user' | 'assistant'
  content: string
  timestamp: Date
  memories?: Array<{
    type: string
    title?: string
    similarity: number
  }>
}

interface AICompanionChatProps {
  className?: string
}

export default function AICompanionChat({ className = '' }: AICompanionChatProps) {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      type: 'assistant',
      content: '你好！我是你的智能助手，基于你的成长数据和项目经验构建。我可以帮助你分析过往经历、提供个性化建议，或者协助你思考问题。有什么我可以帮助你的吗？',
      timestamp: new Date()
    }
  ])
  const [inputValue, setInputValue] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({
      behavior: 'smooth',
      block: 'end',
      inline: 'nearest'
    })
  }

  useEffect(() => {
    // 使用 setTimeout 确保 DOM 更新完成后再滚动
    const timer = setTimeout(() => {
      scrollToBottom()
    }, 100)

    return () => clearTimeout(timer)
  }, [messages])

  const searchMemories = async (query: string) => {
    try {
      const response = await post('/ai/search-memories/', {
        query,
        limit: 3
      })
      return response.memories || []
    } catch (error) {
      console.error('Memory search failed:', error)
      return []
    }
  }

  const generateResponse = async (userMessage: string, memories: any[]) => {
    // 构建上下文
    const memoryContext = memories.map(memory => {
      if (memory.type === 'growth_item') {
        return `项目经验：${memory.title} - ${memory.description || memory.ai_summary || ''}`
      } else if (memory.type === 'journal_entry') {
        return `日记记录：${memory.date}的思考和感悟`
      }
      return ''
    }).filter(Boolean).join('\n')

    const systemPrompt = `你是一个专业的成长分析助手，基于用户的真实成长数据提供客观分析和建议。

用户数据背景：
- 项目经验和技能发展历程
- 学习过程和知识积累轨迹
- 思考方式和问题解决模式

相关记忆片段：
${memoryContext}

回复要求：
1. 仅基于提供的记忆片段进行分析，不要编造或假设不存在的信息
2. 如果记忆片段不足以回答问题，请明确说明需要更多信息
3. 提供具体、可操作的建议，避免空泛的表述
4. 保持客观、专业的语调，避免过度情感化的表达
5. 重点关注用户的成长模式、技能发展和决策支持

请基于以上要求回应用户的问题。`

    try {
      const response = await post('/ai/chat/', {
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userMessage }
        ],
        temperature: 0.3,  // 降低温度以提高准确性和减少幻觉
        max_tokens: 800    // 增加token数量以支持更详细的分析
      })
      return response.content || '抱歉，我现在无法回应。请稍后再试。'
    } catch (error) {
      console.error('AI response failed:', error)
      return '抱歉，我现在无法回应。请稍后再试。'
    }
  }

  const handleSendMessage = async () => {
    if (!inputValue.trim() || isLoading) return

    const userMessage = inputValue.trim()
    setInputValue('')
    setIsLoading(true)

    // 添加用户消息
    const newUserMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: userMessage,
      timestamp: new Date()
    }
    setMessages(prev => [...prev, newUserMessage])

    try {
      // 搜索相关记忆
      const memories = await searchMemories(userMessage)
      
      // 生成AI回应
      const aiResponse = await generateResponse(userMessage, memories)
      
      // 添加AI回应
      const newAIMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'assistant',
        content: aiResponse,
        timestamp: new Date(),
        memories: memories.slice(0, 2) // 只显示前2个相关记忆
      }
      setMessages(prev => [...prev, newAIMessage])
    } catch (error) {
      console.error('Chat error:', error)
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'assistant',
        content: '抱歉，我遇到了一些问题。请稍后再试。',
        timestamp: new Date()
      }
      setMessages(prev => [...prev, errorMessage])
    } finally {
      setIsLoading(false)
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  return (
    <div className={`flex flex-col h-full ${className}`}>
      {/* 头部 */}
      <div className="flex items-center gap-3 p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="relative">
          <div className="w-10 h-10 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-full flex items-center justify-center">
            <Brain className="w-5 h-5 text-white" />
          </div>
          <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white dark:border-gray-800"></div>
        </div>
        <div>
          <h3 className="font-semibold text-gray-900 dark:text-white">智能助手</h3>
          <p className="text-sm text-gray-500 dark:text-gray-400">基于你的成长数据提供个性化建议</p>
        </div>
        <div className="ml-auto">
          <Sparkles className="w-5 h-5 text-primary-500 animate-pulse" />
        </div>
      </div>

      {/* 消息列表 */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4 scroll-smooth">
        {messages.map((message) => (
          <div
            key={message.id}
            className={`flex gap-3 ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
          >
            {message.type === 'assistant' && (
              <div className="w-8 h-8 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-full flex items-center justify-center flex-shrink-0">
                <Bot className="w-4 h-4 text-white" />
              </div>
            )}
            
            <div className={`max-w-[70%] ${message.type === 'user' ? 'order-1' : ''}`}>
              <div className={`p-3 rounded-lg shadow-sm ${
                message.type === 'user'
                  ? 'bg-primary-500 text-white ml-auto'
                  : 'bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100'
              }`}>
                <p className="text-sm leading-relaxed">{message.content}</p>
                
                {/* 显示相关记忆 */}
                {message.memories && message.memories.length > 0 && (
                  <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-600">
                    <p className="text-xs text-gray-500 dark:text-gray-400 mb-2 flex items-center gap-1">
                      <Heart className="w-3 h-3" />
                      相关记忆
                    </p>
                    {message.memories.map((memory, index) => (
                      <div key={index} className="text-xs text-gray-600 dark:text-gray-300 mb-1">
                        • {memory.title || `${memory.type === 'growth_item' ? '项目' : '日记'}`}
                        <span className="text-gray-400 ml-1">
                          ({Math.round(memory.similarity * 100)}%匹配)
                        </span>
                      </div>
                    ))}
                  </div>
                )}
              </div>
              
              <p className="text-xs text-gray-400 mt-1 px-1">
                {message.timestamp.toLocaleTimeString()}
              </p>
            </div>

            {message.type === 'user' && (
              <div className="w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center flex-shrink-0">
                <User className="w-4 h-4 text-gray-600 dark:text-gray-300" />
              </div>
            )}
          </div>
        ))}
        
        {isLoading && (
          <div className="flex gap-3 justify-start">
            <div className="w-8 h-8 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-full flex items-center justify-center">
              <Bot className="w-4 h-4 text-white" />
            </div>
            <div className="p-3 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 rounded-lg shadow-sm">
              <div className="flex items-center gap-2">
                <div className="flex gap-1">
                  <div className="w-2 h-2 bg-primary-500 rounded-full animate-bounce"></div>
                  <div className="w-2 h-2 bg-primary-500 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                  <div className="w-2 h-2 bg-primary-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                </div>
                <span className="text-sm text-gray-500 dark:text-gray-400">正在思考...</span>
              </div>
            </div>
          </div>
        )}
        
        {/* 滚动锚点 - 确保在消息列表的最底部 */}
        <div ref={messagesEndRef} className="h-0" />
      </div>

      {/* 输入区域 */}
      <div className="p-4 border-t border-gray-200 dark:border-gray-700">
        <div className="flex gap-2">
          <input
            ref={inputRef}
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder="向智能助手提问或寻求建议..."
            className="flex-1 input"
            disabled={isLoading}
          />
          <button
            onClick={handleSendMessage}
            disabled={!inputValue.trim() || isLoading}
            className="btn btn-primary px-4"
          >
            <Send className="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>
  )
}
