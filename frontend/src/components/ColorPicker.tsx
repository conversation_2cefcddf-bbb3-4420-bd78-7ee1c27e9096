'use client'

import { useState, useRef, useEffect } from 'react'
import { useTheme } from '@/contexts/ThemeContext'
import { SwatchIcon } from '@heroicons/react/24/outline'

// 预设颜色选项
const presetColors = [
  { name: '青绿', color: '#14b8a6' }, // teal
  { name: '蓝色', color: '#3b82f6' }, // blue
  { name: '紫色', color: '#8b5cf6' }, // violet
  { name: '粉色', color: '#ec4899' }, // pink
  { name: '红色', color: '#ef4444' }, // red
  { name: '橙色', color: '#f97316' }, // orange
  { name: '黄色', color: '#eab308' }, // yellow
  { name: '绿色', color: '#22c55e' }, // green
  { name: '靛蓝', color: '#6366f1' }, // indigo
  { name: '玫瑰', color: '#f43f5e' }, // rose
]

export default function ColorPicker() {
  const { baseColor, setBaseColor } = useTheme()
  const [isOpen, setIsOpen] = useState(false)
  const [customColor, setCustomColor] = useState(baseColor)
  const dropdownRef = useRef<HTMLDivElement>(null)

  // 点击外部关闭下拉菜单
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  const handlePresetColorSelect = (color: string) => {
    setBaseColor(color)
    setCustomColor(color)
    setIsOpen(false)
  }

  const handleCustomColorChange = (color: string) => {
    setCustomColor(color)
    setBaseColor(color)
  }

  return (
    <div className="relative" ref={dropdownRef}>
      {/* 触发按钮 */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-2 px-3 py-2 text-sm font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
        title="选择主题颜色"
      >
        <div 
          className="w-4 h-4 rounded-full border-2 border-white shadow-sm"
          style={{ backgroundColor: baseColor }}
        />
        <SwatchIcon className="h-4 w-4" />
        <span className="hidden sm:inline">主题</span>
      </button>

      {/* 下拉菜单 - 使用更高的z-index确保在导航栏之上 */}
      {isOpen && (
        <div className="absolute right-0 mt-2 w-80 bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-600 p-4 z-[60]">
          <div className="space-y-4">
            {/* 标题 */}
            <div>
              <h3 className="text-sm font-semibold text-gray-900 mb-1">选择主题颜色</h3>
              <p className="text-xs text-gray-500">选择一个颜色来个性化您的界面</p>
            </div>

            {/* 预设颜色 */}
            <div>
              <h4 className="text-xs font-medium text-gray-700 mb-2">预设颜色</h4>
              <div className="grid grid-cols-5 gap-2">
                {presetColors.map((preset) => (
                  <button
                    key={preset.color}
                    onClick={() => handlePresetColorSelect(preset.color)}
                    className={`
                      w-12 h-12 rounded-lg border-2 transition-all duration-200 hover:scale-110
                      ${baseColor === preset.color 
                        ? 'border-gray-400 shadow-md' 
                        : 'border-gray-200 hover:border-gray-300'
                      }
                    `}
                    style={{ backgroundColor: preset.color }}
                    title={preset.name}
                  >
                    {baseColor === preset.color && (
                      <div className="w-full h-full flex items-center justify-center">
                        <div className="w-2 h-2 bg-white rounded-full shadow-sm" />
                      </div>
                    )}
                  </button>
                ))}
              </div>
            </div>

            {/* 自定义颜色 */}
            <div>
              <h4 className="text-xs font-medium text-gray-700 mb-2">自定义颜色</h4>
              <div className="flex items-center space-x-3">
                <div className="relative">
                  <input
                    type="color"
                    value={customColor}
                    onChange={(e) => handleCustomColorChange(e.target.value)}
                    className="w-12 h-12 rounded-lg border-2 border-gray-200 cursor-pointer"
                    title="选择自定义颜色"
                  />
                </div>
                <div className="flex-1">
                  <input
                    type="text"
                    value={customColor}
                    onChange={(e) => {
                      const value = e.target.value
                      if (/^#[0-9A-Fa-f]{6}$/.test(value)) {
                        handleCustomColorChange(value)
                      }
                      setCustomColor(value)
                    }}
                    className="w-full px-3 py-2 text-sm border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    placeholder="#000000"
                  />
                </div>
              </div>
            </div>

            {/* 颜色预览 */}
            <div>
              <h4 className="text-xs font-medium text-gray-700 mb-2">颜色预览</h4>
              <div className="flex space-x-1 rounded-lg overflow-hidden border border-gray-200">
                {['50', '100', '200', '300', '400', '500', '600', '700', '800', '900'].map((shade) => (
                  <div
                    key={shade}
                    className="flex-1 h-8"
                    style={{ 
                      backgroundColor: `var(--color-primary-${shade})`,
                    }}
                    title={`${shade} 色调`}
                  />
                ))}
              </div>
            </div>

            {/* 应用说明 */}
            <div className="text-xs text-gray-500 bg-gray-50 rounded-lg p-3">
              <p className="mb-1">💡 <strong>智能配色：</strong></p>
              <p>系统会根据您选择的颜色自动生成完整的配色方案，包括浅色、深色变体和对比色，确保界面的和谐统一。</p>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
