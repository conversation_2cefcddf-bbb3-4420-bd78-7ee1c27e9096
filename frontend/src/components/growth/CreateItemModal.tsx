'use client'

import { useState } from 'react'
import { createPortal } from 'react-dom'
import { XMarkIcon } from '@heroicons/react/24/outline'
import { GrowthItemCreate, Tag } from '@/types'

interface CreateItemModalProps {
  isOpen: boolean
  onClose: () => void
  onSubmit: (form: GrowthItemCreate) => Promise<void>
  tags: Tag[]
  isCreating: boolean
}

export default function CreateItemModal({ 
  isOpen, 
  onClose, 
  onSubmit, 
  tags, 
  isCreating 
}: CreateItemModalProps) {
  const [form, setForm] = useState<GrowthItemCreate>({
    title: '',
    description: '',
    item_type: 'goal',
    status: 'future_plan',
    parent: undefined,
    tag_ids: []
  })

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!form.title.trim()) return
    
    await onSubmit(form)
    
    // 重置表单
    setForm({
      title: '',
      description: '',
      item_type: 'goal',
      status: 'future_plan',
      parent: undefined,
      tag_ids: []
    })
  }

  const handleTagToggle = (tagId: number) => {
    setForm(prev => ({
      ...prev,
      tag_ids: prev.tag_ids?.includes(tagId)
        ? prev.tag_ids.filter(id => id !== tagId)
        : [...(prev.tag_ids || []), tagId]
    }))
  }

  if (!isOpen) return null

  const modalContent = (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      {/* 背景遮罩 */}
      <div
        className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
        onClick={onClose}
      />

      {/* 模态框容器 */}
      <div className="flex min-h-screen items-center justify-center p-4">
        <div className="relative w-full max-w-md bg-white rounded-lg shadow-xl transform transition-all z-10">
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-lg font-semibold text-gray-900">添加新项目</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
            disabled={isCreating}
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              项目标题 *
            </label>
            <input
              type="text"
              value={form.title}
              onChange={(e) => setForm(prev => ({ ...prev, title: e.target.value }))}
              className="input w-full"
              placeholder="输入项目标题..."
              required
              disabled={isCreating}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              项目描述
            </label>
            <textarea
              value={form.description}
              onChange={(e) => setForm(prev => ({ ...prev, description: e.target.value }))}
              className="input w-full h-20 resize-none"
              placeholder="描述项目内容..."
              disabled={isCreating}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              项目类型
            </label>
            <select
              value={form.item_type}
              onChange={(e) => setForm(prev => ({ ...prev, item_type: e.target.value as 'goal' | 'task' }))}
              className="input w-full"
              disabled={isCreating}
            >
              <option value="goal">目标</option>
              <option value="task">任务</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              初始状态
            </label>
            <select
              value={form.status}
              onChange={(e) => setForm(prev => ({ ...prev, status: e.target.value as any }))}
              className="input w-full"
              disabled={isCreating}
            >
              <option value="future_plan">未来计划</option>
              <option value="in_progress">进行中</option>
            </select>
          </div>

          {tags.length > 0 && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                选择标签
              </label>
              <div className="flex flex-wrap gap-2 max-h-32 overflow-y-auto">
                {tags.map((tag) => (
                  <button
                    key={tag.tag_id}
                    type="button"
                    onClick={() => handleTagToggle(tag.tag_id)}
                    className={`px-3 py-1 rounded-full text-sm transition-colors ${
                      form.tag_ids?.includes(tag.tag_id)
                        ? 'bg-primary-100 text-primary-700 border border-primary-300'
                        : 'bg-gray-100 text-gray-700 border border-gray-300 hover:bg-gray-200'
                    }`}
                    disabled={isCreating}
                  >
                    {tag.name}
                  </button>
                ))}
              </div>
            </div>
          )}

          <div className="flex gap-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="btn btn-secondary flex-1"
              disabled={isCreating}
            >
              取消
            </button>
            <button
              type="submit"
              className="btn btn-primary flex-1"
              disabled={isCreating || !form.title.trim()}
            >
              {isCreating ? '创建中...' : '创建'}
            </button>
          </div>
        </form>
        </div>
      </div>
    </div>
  )

  // 使用Portal渲染到页面根部
  const modalRoot = document.getElementById('modal-root')
  if (!modalRoot) {
    console.warn('Modal root element not found. Make sure to add <div id="modal-root" /> to your app.')
    return null
  }

  return createPortal(modalContent, modalRoot)
}
