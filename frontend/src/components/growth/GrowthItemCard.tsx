'use client'

import { CheckCircleIcon, ClockIcon, CalendarIcon, PencilIcon, TrashIcon, SparklesIcon } from '@heroicons/react/24/outline'
import { GrowthItem } from '@/types'
import { formatRelativeTime, getGrowthItemStatusText, getStatusColorClass } from '@/lib/utils'

interface GrowthItemCardProps {
  item: GrowthItem
  onStatusChange: (itemId: string, newStatus: string) => void
  onEdit: (item: GrowthItem) => void
  onDelete: (item: GrowthItem) => void
  onAIGenerate?: (item: GrowthItem) => void
}

export default function GrowthItemCard({ item, onStatusChange, onEdit, onDelete, onAIGenerate }: GrowthItemCardProps) {
  const statusOptions = [
    { value: 'future_plan', label: '未来计划' },
    { value: 'in_progress', label: '进行中' },
    { value: 'completed', label: '已完成' }
  ]

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircleIcon className="h-4 w-4 text-green-500" />
      case 'in_progress':
        return <ClockIcon className="h-4 w-4 text-blue-500" />
      default:
        return <CalendarIcon className="h-4 w-4 text-gray-400" />
    }
  }

  return (
    <div className={`card hover:shadow-md transition-shadow ${getStatusColorClass(item.status)}`}>
      <div className="card-body">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-2">
              {getStatusIcon(item.status)}
              <h3 className="font-medium text-gray-900">{item.title}</h3>
              <span className={`badge ${
                item.item_type === 'goal' ? 'badge-primary' : 'badge-secondary'
              }`}>
                {item.item_type === 'goal' ? '目标' : '任务'}
              </span>
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="flex items-center gap-1 ml-2">
            {/* AI生成子任务按钮 - 只在未来计划状态显示 */}
            {item.status === 'future_plan' && onAIGenerate && (
              <button
                onClick={() => onAIGenerate(item)}
                className="p-1.5 text-gray-400 hover:text-purple-600 hover:bg-purple-50 rounded-md transition-colors"
                title="AI生成子任务"
              >
                <SparklesIcon className="h-4 w-4" />
              </button>
            )}
            <button
              onClick={() => onEdit(item)}
              className="p-1.5 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-md transition-colors"
              title="编辑"
            >
              <PencilIcon className="h-4 w-4" />
            </button>
            <button
              onClick={() => onDelete(item)}
              className="p-1.5 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-md transition-colors"
              title="删除"
            >
              <TrashIcon className="h-4 w-4" />
            </button>
          </div>
        </div>

        <div className="mt-2">
          {item.description && (
            <p className="text-sm text-gray-600 mb-3">{item.description}</p>
          )}

          {item.tags && item.tags.length > 0 && (
            <div className="flex flex-wrap gap-1 mb-3">
              {item.tags.map((tag) => (
                <span key={tag.tag_id} className="badge-outline text-xs">
                  {tag.name}
                </span>
              ))}
            </div>
          )}
        </div>
        
        <div className="flex items-center justify-between text-xs text-gray-500 pt-3 border-t">
          <span>{formatRelativeTime(item.created_at)}</span>
          <select
            value={item.status}
            onChange={(e) => onStatusChange(item.item_id, e.target.value)}
            className="text-xs border-0 bg-transparent focus:ring-0 cursor-pointer"
          >
            {statusOptions.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>
      </div>
    </div>
  )
}
