'use client'

import { createPortal } from 'react-dom'
import { ExclamationTriangleIcon, XMarkIcon } from '@heroicons/react/24/outline'
import { GrowthItem } from '@/types'

interface DeleteConfirmModalProps {
  isOpen: boolean
  onClose: () => void
  onConfirm: (itemId: string) => Promise<void>
  item: GrowthItem | null
  isDeleting: boolean
}

export default function DeleteConfirmModal({ 
  isOpen, 
  onClose, 
  onConfirm, 
  item,
  isDeleting 
}: DeleteConfirmModalProps) {
  const handleConfirm = async () => {
    if (!item) return
    await onConfirm(item.item_id)
  }

  if (!isOpen || !item) return null

  const modalContent = (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      {/* 背景遮罩 */}
      <div
        className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
        onClick={onClose}
      />

      {/* 模态框容器 */}
      <div className="flex min-h-screen items-center justify-center p-4">
        <div className="relative w-full max-w-md bg-white rounded-lg shadow-xl transform transition-all z-10">
        <div className="flex items-center justify-between p-6 border-b">
          <div className="flex items-center gap-3">
            <ExclamationTriangleIcon className="h-6 w-6 text-red-500" />
            <h2 className="text-lg font-semibold text-gray-900">确认删除</h2>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
            disabled={isDeleting}
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        <div className="p-6">
          <p className="text-gray-600 mb-4">
            您确定要删除项目 <span className="font-medium text-gray-900">"{item.title}"</span> 吗？
          </p>
          <p className="text-sm text-red-600 mb-6">
            此操作无法撤销，项目的所有相关数据都将被永久删除。
          </p>

          <div className="flex gap-3">
            <button
              type="button"
              onClick={onClose}
              className="btn btn-secondary flex-1"
              disabled={isDeleting}
            >
              取消
            </button>
            <button
              type="button"
              onClick={handleConfirm}
              className="btn bg-red-600 hover:bg-red-700 text-white flex-1"
              disabled={isDeleting}
            >
              {isDeleting ? '删除中...' : '确认删除'}
            </button>
          </div>
        </div>
        </div>
      </div>
    </div>
  )

  // 使用Portal渲染到页面根部
  const modalRoot = document.getElementById('modal-root')
  if (!modalRoot) {
    console.warn('Modal root element not found. Make sure to add <div id="modal-root" /> to your app.')
    return null
  }

  return createPortal(modalContent, modalRoot)
}
