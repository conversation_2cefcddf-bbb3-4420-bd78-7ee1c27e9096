'use client'

import { useState, useEffect } from 'react'
import { createPortal } from 'react-dom'
import { XMarkIcon } from '@heroicons/react/24/outline'
import { GrowthItem, Tag, GrowthItemStatus } from '@/types'

interface EditItemModalProps {
  isOpen: boolean
  onClose: () => void
  onSubmit: (itemId: string, form: Partial<GrowthItem>) => Promise<void>
  item: GrowthItem | null
  tags: Tag[]
  isUpdating: boolean
}

export default function EditItemModal({ 
  isOpen, 
  onClose, 
  onSubmit, 
  item,
  tags, 
  isUpdating 
}: EditItemModalProps) {
  const [form, setForm] = useState({
    title: '',
    description: '',
    item_type: 'goal' as 'goal' | 'task',
    status: 'future_plan' as GrowthItemStatus,
    tag_ids: [] as number[]
  })

  // 当item变化时更新表单
  useEffect(() => {
    if (item) {
      setForm({
        title: item.title,
        description: item.description || '',
        item_type: item.item_type,
        status: item.status,
        tag_ids: item.tags?.map(tag => tag.tag_id) || []
      })
    }
  }, [item])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!form.title.trim() || !item) return
    
    await onSubmit(item.item_id, form)
  }

  const handleTagToggle = (tagId: number) => {
    setForm(prev => ({
      ...prev,
      tag_ids: prev.tag_ids.includes(tagId)
        ? prev.tag_ids.filter(id => id !== tagId)
        : [...prev.tag_ids, tagId]
    }))
  }

  if (!isOpen || !item) return null

  const modalContent = (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      {/* 背景遮罩 */}
      <div
        className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
        onClick={onClose}
      />

      {/* 模态框容器 */}
      <div className="flex min-h-screen items-center justify-center p-4">
        <div className="relative w-full max-w-md bg-white rounded-lg shadow-xl transform transition-all z-10">
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-lg font-semibold text-gray-900">编辑项目</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
            disabled={isUpdating}
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              项目标题 *
            </label>
            <input
              type="text"
              value={form.title}
              onChange={(e) => setForm(prev => ({ ...prev, title: e.target.value }))}
              className="input w-full"
              placeholder="输入项目标题..."
              required
              disabled={isUpdating}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              项目描述
            </label>
            <textarea
              value={form.description}
              onChange={(e) => setForm(prev => ({ ...prev, description: e.target.value }))}
              className="input w-full h-20 resize-none"
              placeholder="描述项目内容..."
              disabled={isUpdating}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              项目类型
            </label>
            <select
              value={form.item_type}
              onChange={(e) => setForm(prev => ({ ...prev, item_type: e.target.value as 'goal' | 'task' }))}
              className="input w-full"
              disabled={isUpdating}
            >
              <option value="goal">目标</option>
              <option value="task">任务</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              状态
            </label>
            <select
              value={form.status}
              onChange={(e) => setForm(prev => ({ ...prev, status: e.target.value as GrowthItemStatus }))}
              className="input w-full"
              disabled={isUpdating}
            >
              <option value="future_plan">未来计划</option>
              <option value="in_progress">进行中</option>
              <option value="completed">已完成</option>
            </select>
          </div>

          {tags.length > 0 && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                选择标签
              </label>
              <div className="flex flex-wrap gap-2 max-h-32 overflow-y-auto">
                {tags.map((tag) => (
                  <button
                    key={tag.tag_id}
                    type="button"
                    onClick={() => handleTagToggle(tag.tag_id)}
                    className={`px-3 py-1 rounded-full text-sm transition-colors ${
                      form.tag_ids.includes(tag.tag_id)
                        ? 'bg-primary-100 text-primary-700 border border-primary-300'
                        : 'bg-gray-100 text-gray-700 border border-gray-300 hover:bg-gray-200'
                    }`}
                    disabled={isUpdating}
                  >
                    {tag.name}
                  </button>
                ))}
              </div>
            </div>
          )}

          <div className="flex gap-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="btn btn-secondary flex-1"
              disabled={isUpdating}
            >
              取消
            </button>
            <button
              type="submit"
              className="btn btn-primary flex-1"
              disabled={isUpdating || !form.title.trim()}
            >
              {isUpdating ? '更新中...' : '更新'}
            </button>
          </div>
        </form>
        </div>
      </div>
    </div>
  )

  // 使用Portal渲染到页面根部
  const modalRoot = document.getElementById('modal-root')
  if (!modalRoot) {
    console.warn('Modal root element not found. Make sure to add <div id="modal-root" /> to your app.')
    return null
  }

  return createPortal(modalContent, modalRoot)
}
