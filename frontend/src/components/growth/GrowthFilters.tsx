'use client'

import { FunnelIcon, MagnifyingGlassIcon } from '@heroicons/react/24/outline'

interface GrowthFiltersProps {
  searchQuery: string
  onSearchChange: (query: string) => void
  statusFilter: string
  onStatusFilterChange: (status: string) => void
  typeFilter: string
  onTypeFilterChange: (type: string) => void
}

export default function GrowthFilters({
  searchQuery,
  onSearchChange,
  statusFilter,
  onStatusFilterChange,
  typeFilter,
  onTypeFilterChange
}: GrowthFiltersProps) {
  return (
    <div className="flex flex-col sm:flex-row gap-4">
      {/* 搜索框 */}
      <div className="relative flex-1">
        <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
        <input
          type="text"
          placeholder="搜索项目..."
          value={searchQuery}
          onChange={(e) => onSearchChange(e.target.value)}
          className="input pl-10 w-full"
        />
      </div>

      {/* 过滤器 */}
      <div className="flex gap-2">
        <div className="relative">
          <FunnelIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <select
            value={statusFilter}
            onChange={(e) => onStatusFilterChange(e.target.value)}
            className="input pl-10 pr-8 appearance-none bg-white"
          >
            <option value="all">所有状态</option>
            <option value="future_plan">未来计划</option>
            <option value="in_progress">进行中</option>
            <option value="completed">已完成</option>
          </select>
        </div>

        <select
          value={typeFilter}
          onChange={(e) => onTypeFilterChange(e.target.value)}
          className="input"
        >
          <option value="all">所有类型</option>
          <option value="goal">目标</option>
          <option value="task">任务</option>
        </select>
      </div>
    </div>
  )
}
