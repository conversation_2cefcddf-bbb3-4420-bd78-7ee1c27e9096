'use client'

import { useState } from 'react'
import {
  CheckCircleIcon,
  ClockIcon,
  CalendarIcon,
  PencilIcon,
  TrashIcon,
  SparklesIcon,
  ChevronDownIcon,
  ChevronRightIcon,
  PlusIcon
} from '@heroicons/react/24/outline'
import { GrowthItem } from '@/types'
import { formatRelativeTime, getGrowthItemStatusText, getStatusColorClass } from '@/lib/utils'

interface GrowthItemCardWithSubtasksProps {
  item: GrowthItem
  onStatusChange: (itemId: string, newStatus: string) => void
  onEdit: (item: GrowthItem) => void
  onDelete: (item: GrowthItem) => void
  onAIGenerate?: (item: GrowthItem) => void
  onAddSubtask?: (item: GrowthItem) => void
}

export default function GrowthItemCardWithSubtasks({ 
  item, 
  onStatusChange, 
  onEdit, 
  onDelete, 
  onAIGenerate,
  onAddSubtask 
}: GrowthItemCardWithSubtasksProps) {
  const [isExpanded, setIsExpanded] = useState(true)
  
  const statusOptions = [
    { value: 'future_plan', label: '未来计划' },
    { value: 'in_progress', label: '进行中' },
    { value: 'completed', label: '已完成' }
  ]

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircleIcon className="h-4 w-4 text-green-500" />
      case 'in_progress':
        return <ClockIcon className="h-4 w-4 text-blue-500" />
      default:
        return <CalendarIcon className="h-4 w-4 text-gray-400" />
    }
  }

  const getSubtaskStatusDot = (status: string) => {
    switch (status) {
      case 'completed':
        return <div className="w-2 h-2 rounded-full bg-green-500" />
      case 'in_progress':
        return <div className="w-2 h-2 rounded-full bg-yellow-500" />
      default:
        return <div className="w-2 h-2 rounded-full bg-gray-400" />
    }
  }

  const hasSubtasks = item.children && item.children.length > 0

  return (
    <div className={`card hover:shadow-md transition-shadow ${getStatusColorClass(item.status)}`}>
      <div className="card-body">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-2">
              {/* 展开/收起按钮 */}
              {hasSubtasks && (
                <button
                  onClick={() => setIsExpanded(!isExpanded)}
                  className="p-0.5 text-gray-400 hover:text-gray-600 transition-colors"
                >
                  {isExpanded ? (
                    <ChevronDownIcon className="h-4 w-4" />
                  ) : (
                    <ChevronRightIcon className="h-4 w-4" />
                  )}
                </button>
              )}
              
              {getStatusIcon(item.status)}
              <h3 className="font-medium text-gray-900">{item.title}</h3>
              <span className={`badge ${
                item.item_type === 'goal' ? 'badge-primary' : 'badge-secondary'
              }`}>
                {item.item_type === 'goal' ? '目标' : '任务'}
              </span>
              
              {/* 子任务数量指示器 */}
              {hasSubtasks && (
                <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                  {item.children?.length || 0} 个子任务
                </span>
              )}
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="flex items-center gap-1 ml-2">
            {/* AI生成子任务按钮 - 只在未来计划状态显示 */}
            {item.status === 'future_plan' && onAIGenerate && (
              <button
                onClick={() => onAIGenerate(item)}
                className="p-1.5 text-gray-400 hover:text-purple-600 hover:bg-purple-50 rounded-md transition-colors"
                title="AI生成子任务"
              >
                <SparklesIcon className="h-4 w-4" />
              </button>
            )}
            
            {/* 手动添加子任务按钮 */}
            {onAddSubtask && (
              <button
                onClick={() => onAddSubtask(item)}
                className="p-1.5 text-gray-400 hover:text-green-600 hover:bg-green-50 rounded-md transition-colors"
                title="添加子任务"
              >
                <PlusIcon className="h-4 w-4" />
              </button>
            )}
            
            <button
              onClick={() => onEdit(item)}
              className="p-1.5 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-md transition-colors"
              title="编辑"
            >
              <PencilIcon className="h-4 w-4" />
            </button>
            <button
              onClick={() => onDelete(item)}
              className="p-1.5 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-md transition-colors"
              title="删除"
            >
              <TrashIcon className="h-4 w-4" />
            </button>
          </div>
        </div>

        <div className="mt-2">
          {item.description && (
            <p className="text-sm text-gray-600 mb-3">{item.description}</p>
          )}

          {item.tags && item.tags.length > 0 && (
            <div className="flex flex-wrap gap-1 mb-3">
              {item.tags.map((tag) => (
                <span key={tag.tag_id} className="badge-outline text-xs">
                  {tag.name}
                </span>
              ))}
            </div>
          )}

          {/* 子任务列表 */}
          {hasSubtasks && isExpanded && (
            <div className="mt-4 pl-4 border-l-2 border-gray-100">
              <div className="space-y-2">
                {item.children?.map((subtask) => (
                  <div
                    key={subtask.item_id}
                    className="flex items-center gap-3 p-2 bg-gray-50 rounded-md hover:bg-gray-100 transition-colors"
                  >
                    {/* 状态点 */}
                    <div className="flex-shrink-0">
                      {getSubtaskStatusDot(subtask.status)}
                    </div>
                    
                    {/* 子任务内容 */}
                    <div className="flex-1 min-w-0">
                      <h4 className="text-sm font-medium text-gray-900 truncate">
                        {subtask.title}
                      </h4>
                      {subtask.description && (
                        <p className="text-xs text-gray-600 mt-1 line-clamp-2">
                          {subtask.description}
                        </p>
                      )}
                    </div>
                    
                    {/* 子任务状态选择器 */}
                    <div className="flex-shrink-0">
                      <select
                        value={subtask.status}
                        onChange={(e) => onStatusChange(subtask.item_id, e.target.value)}
                        className="text-xs border-0 bg-transparent focus:ring-0 cursor-pointer pr-6"
                      >
                        {statusOptions.map(option => (
                          <option key={option.value} value={option.value}>
                            {option.label}
                          </option>
                        ))}
                      </select>
                    </div>
                    
                    {/* 子任务操作按钮 */}
                    <div className="flex items-center gap-1">
                      <button
                        onClick={() => onEdit(subtask)}
                        className="p-1 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded transition-colors"
                        title="编辑子任务"
                      >
                        <PencilIcon className="h-3 w-3" />
                      </button>
                      <button
                        onClick={() => onDelete(subtask)}
                        className="p-1 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded transition-colors"
                        title="删除子任务"
                      >
                        <TrashIcon className="h-3 w-3" />
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
        
        <div className="flex items-center justify-between text-xs text-gray-500 pt-3 border-t">
          <span>{formatRelativeTime(item.created_at)}</span>
          <select
            value={item.status}
            onChange={(e) => onStatusChange(item.item_id, e.target.value)}
            className="text-xs border-0 bg-transparent focus:ring-0 cursor-pointer"
          >
            {statusOptions.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>
      </div>
    </div>
  )
}
