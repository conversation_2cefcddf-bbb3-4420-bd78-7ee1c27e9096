'use client'

import { useState, useEffect } from 'react'
import { get, patch } from '@/lib/api'
import toast from 'react-hot-toast'

interface UserSettings {
  settings_id: string
  theme: 'light' | 'dark' | 'auto'
  primary_color: string
  email_notifications: boolean
  push_notifications: boolean
  weekly_summary: boolean
  profile_visibility: 'public' | 'private'
  auto_save_interval: number
  default_view: 'dashboard' | 'growth' | 'compass' | 'blog'
  custom_background: string | null
  background_opacity: number
  background_blur: number
  created_at: string
  updated_at: string
}

export default function SettingsDebug() {
  const [settings, setSettings] = useState<UserSettings | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [testResults, setTestResults] = useState<string[]>([])

  const addTestResult = (result: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${result}`])
  }

  const fetchSettings = async () => {
    try {
      setIsLoading(true)
      addTestResult('开始获取设置...')
      const settingsData = await get<UserSettings>('/auth/settings/')
      setSettings(settingsData)
      addTestResult(`✅ 成功获取设置: ${JSON.stringify(settingsData, null, 2)}`)
    } catch (error: any) {
      addTestResult(`❌ 获取设置失败: ${error.message}`)
      console.error('Failed to fetch settings:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const testUpdateSetting = async (key: keyof UserSettings, value: any) => {
    try {
      setIsLoading(true)
      addTestResult(`开始更新设置 ${key} = ${value}...`)
      
      const updatedSettings = await patch<UserSettings>('/auth/settings/', {
        [key]: value
      })
      
      setSettings(updatedSettings)
      addTestResult(`✅ 成功更新设置: ${JSON.stringify(updatedSettings, null, 2)}`)
      toast.success(`设置 ${key} 已更新`)
    } catch (error: any) {
      addTestResult(`❌ 更新设置失败: ${error.message}`)
      console.error('Failed to update setting:', error)
      toast.error(`更新设置 ${key} 失败`)
    } finally {
      setIsLoading(false)
    }
  }

  const runAllTests = async () => {
    setTestResults([])
    
    // 测试1: 获取设置
    await fetchSettings()
    
    // 等待一秒
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 测试2: 更新布尔值
    await testUpdateSetting('email_notifications', !settings?.email_notifications)
    
    // 等待一秒
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 测试3: 更新数字值
    await testUpdateSetting('auto_save_interval', Math.floor(Math.random() * 100) + 10)
    
    // 等待一秒
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 测试4: 更新字符串值
    const themes = ['light', 'dark', 'auto']
    const randomTheme = themes[Math.floor(Math.random() * themes.length)]
    await testUpdateSetting('theme', randomTheme)
    
    // 等待一秒
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 测试5: 更新浮点数值
    await testUpdateSetting('background_opacity', Math.random())
    
    // 最终验证
    await fetchSettings()
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
        <h2 className="text-2xl font-bold mb-6 text-gray-900 dark:text-white">
          设置保存调试工具
        </h2>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 控制面板 */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              控制面板
            </h3>
            
            <div className="space-y-2">
              <button
                onClick={fetchSettings}
                disabled={isLoading}
                className="w-full px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50"
              >
                {isLoading ? '加载中...' : '获取当前设置'}
              </button>
              
              <button
                onClick={runAllTests}
                disabled={isLoading}
                className="w-full px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 disabled:opacity-50"
              >
                {isLoading ? '测试中...' : '运行所有测试'}
              </button>
              
              <button
                onClick={() => setTestResults([])}
                className="w-full px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600"
              >
                清空日志
              </button>
            </div>
            
            {/* 当前设置显示 */}
            {settings && (
              <div className="mt-6">
                <h4 className="text-md font-semibold text-gray-900 dark:text-white mb-2">
                  当前设置
                </h4>
                <div className="bg-gray-100 dark:bg-gray-700 p-4 rounded-lg">
                  <pre className="text-sm text-gray-800 dark:text-gray-200 overflow-auto">
                    {JSON.stringify(settings, null, 2)}
                  </pre>
                </div>
              </div>
            )}
          </div>
          
          {/* 测试日志 */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              测试日志
            </h3>
            
            <div className="bg-black text-green-400 p-4 rounded-lg h-96 overflow-y-auto font-mono text-sm">
              {testResults.length === 0 ? (
                <div className="text-gray-500">等待测试...</div>
              ) : (
                testResults.map((result, index) => (
                  <div key={index} className="mb-1">
                    {result}
                  </div>
                ))
              )}
            </div>
          </div>
        </div>
        
        {/* 快速测试按钮 */}
        <div className="mt-6 grid grid-cols-2 md:grid-cols-4 gap-2">
          <button
            onClick={() => testUpdateSetting('theme', 'dark')}
            disabled={isLoading}
            className="px-3 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 disabled:opacity-50 text-sm"
          >
            测试主题
          </button>
          
          <button
            onClick={() => testUpdateSetting('email_notifications', !settings?.email_notifications)}
            disabled={isLoading}
            className="px-3 py-2 bg-orange-500 text-white rounded hover:bg-orange-600 disabled:opacity-50 text-sm"
          >
            测试通知
          </button>
          
          <button
            onClick={() => testUpdateSetting('auto_save_interval', 60)}
            disabled={isLoading}
            className="px-3 py-2 bg-red-500 text-white rounded hover:bg-red-600 disabled:opacity-50 text-sm"
          >
            测试间隔
          </button>
          
          <button
            onClick={() => testUpdateSetting('background_opacity', 0.5)}
            disabled={isLoading}
            className="px-3 py-2 bg-teal-500 text-white rounded hover:bg-teal-600 disabled:opacity-50 text-sm"
          >
            测试透明度
          </button>
        </div>
      </div>
    </div>
  )
}
