/**
 * Toast通知组件
 * 基于react-hot-toast的自定义样式
 */
import React from 'react'
import { Toaster, toast } from 'react-hot-toast'
import {
  CheckCircleIcon,
  ExclamationCircleIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  XMarkIcon,
} from '@heroicons/react/24/outline'

// Toast配置
export const ToastProvider: React.FC = () => (
  <Toaster
    position="top-right"
    toastOptions={{
      duration: 4000,
      style: {
        background: '#fff',
        color: '#374151',
        boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
        border: '1px solid #e5e7eb',
        borderRadius: '0.5rem',
        padding: '16px',
        maxWidth: '400px',
      },
      success: {
        iconTheme: {
          primary: '#10b981',
          secondary: '#fff',
        },
      },
      error: {
        iconTheme: {
          primary: '#ef4444',
          secondary: '#fff',
        },
      },
    }}
  />
)

// 自定义Toast函数
export const showToast = {
  success: (message: string, options?: any) => {
    toast.success(message, {
      icon: <CheckCircleIcon className="h-5 w-5 text-success-500" />,
      ...options,
    })
  },
  
  error: (message: string, options?: any) => {
    toast.error(message, {
      icon: <ExclamationCircleIcon className="h-5 w-5 text-error-500" />,
      ...options,
    })
  },
  
  warning: (message: string, options?: any) => {
    toast(message, {
      icon: <ExclamationTriangleIcon className="h-5 w-5 text-warning-500" />,
      style: {
        borderLeft: '4px solid #f59e0b',
      },
      ...options,
    })
  },
  
  info: (message: string, options?: any) => {
    toast(message, {
      icon: <InformationCircleIcon className="h-5 w-5 text-blue-500" />,
      style: {
        borderLeft: '4px solid #3b82f6',
      },
      ...options,
    })
  },
  
  loading: (message: string, options?: any) => {
    return toast.loading(message, {
      style: {
        borderLeft: '4px solid #6b7280',
      },
      ...options,
    })
  },
  
  dismiss: (toastId?: string) => {
    toast.dismiss(toastId)
  },
  
  promise: <T,>(
    promise: Promise<T>,
    messages: {
      loading: string
      success: string | ((data: T) => string)
      error: string | ((error: any) => string)
    },
    options?: any
  ) => {
    return toast.promise(promise, messages, {
      success: {
        icon: <CheckCircleIcon className="h-5 w-5 text-success-500" />,
      },
      error: {
        icon: <ExclamationCircleIcon className="h-5 w-5 text-error-500" />,
      },
      ...options,
    })
  },
}

// 自定义Toast组件
export interface CustomToastProps {
  type: 'success' | 'error' | 'warning' | 'info'
  title: string
  message?: string
  onClose?: () => void
}

export const CustomToast: React.FC<CustomToastProps> = ({
  type,
  title,
  message,
  onClose,
}) => {
  const icons = {
    success: <CheckCircleIcon className="h-5 w-5 text-success-500" />,
    error: <ExclamationCircleIcon className="h-5 w-5 text-error-500" />,
    warning: <ExclamationTriangleIcon className="h-5 w-5 text-warning-500" />,
    info: <InformationCircleIcon className="h-5 w-5 text-blue-500" />,
  }

  const borderColors = {
    success: 'border-l-success-500',
    error: 'border-l-error-500',
    warning: 'border-l-warning-500',
    info: 'border-l-blue-500',
  }

  return (
    <div className={`flex items-start p-4 bg-white border-l-4 rounded-lg shadow-lg ${borderColors[type]}`}>
      <div className="flex-shrink-0">
        {icons[type]}
      </div>
      <div className="ml-3 flex-1">
        <p className="text-sm font-medium text-gray-900">{title}</p>
        {message && (
          <p className="mt-1 text-sm text-gray-600">{message}</p>
        )}
      </div>
      {onClose && (
        <div className="ml-4 flex-shrink-0">
          <button
            onClick={onClose}
            className="inline-flex text-gray-400 hover:text-gray-600 focus:outline-none"
          >
            <XMarkIcon className="h-4 w-4" />
          </button>
        </div>
      )}
    </div>
  )
}

export default showToast
