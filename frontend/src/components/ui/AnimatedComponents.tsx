'use client'

import React, { ReactNode } from 'react'
import { cn } from '@/lib/utils'
import { 
  useIntersectionObserver, 
  useStaggeredAnimation, 
  useCountUp,
  useTypewriter,
  useHoverAnimation 
} from '@/hooks/useAnimations'

interface AnimatedContainerProps {
  children: ReactNode
  className?: string
  animation?: 'fade-in' | 'slide-in-left' | 'slide-in-right' | 'scale-in' | 'float'
  delay?: number
}

export function AnimatedContainer({
  children,
  className,
  animation = 'fade-in',
  delay = 0
}: AnimatedContainerProps) {
  const { ref, hasIntersected } = useIntersectionObserver()

  return (
    <div
      ref={ref}
      className={cn(
        'transition-all duration-700 ease-out',
        hasIntersected ? animation : 'opacity-0 translate-y-8',
        className
      )}
      style={{ transitionDelay: `${delay}ms` }}
    >
      {children}
    </div>
  )
}

interface StaggeredListProps {
  children: ReactNode[]
  className?: string
  itemClassName?: string
  delay?: number
}

export function StaggeredList({
  children,
  className,
  itemClassName,
  delay = 100
}: StaggeredListProps) {
  const { ref, visibleItems } = useStaggeredAnimation(children.length, delay)

  return (
    <div ref={ref} className={className}>
      {children.map((child, index) => (
        <div
          key={index}
          className={cn(
            'transition-all duration-500 ease-out',
            visibleItems.has(index) 
              ? 'opacity-100 translate-y-0' 
              : 'opacity-0 translate-y-4',
            itemClassName
          )}
        >
          {child}
        </div>
      ))}
    </div>
  )
}

interface CountUpProps {
  end: number
  start?: number
  duration?: number
  decimals?: number
  className?: string
  suffix?: string
  prefix?: string
}

export function CountUp({
  end,
  start = 0,
  duration = 2000,
  decimals = 0,
  className,
  suffix = '',
  prefix = ''
}: CountUpProps) {
  const { ref, count } = useCountUp(end, duration, start, decimals)

  return (
    <span ref={ref} className={className}>
      {prefix}{count}{suffix}
    </span>
  )
}

interface TypewriterProps {
  text: string
  speed?: number
  startDelay?: number
  className?: string
  cursor?: boolean
}

export function Typewriter({
  text,
  speed = 50,
  startDelay = 0,
  className,
  cursor = true
}: TypewriterProps) {
  const { displayText, isComplete } = useTypewriter(text, speed, startDelay)

  return (
    <span className={className}>
      {displayText}
      {cursor && !isComplete && (
        <span className="animate-pulse">|</span>
      )}
    </span>
  )
}

interface FloatingCardProps {
  children: ReactNode
  className?: string
  intensity?: number
}

export function FloatingCard({
  children,
  className,
  intensity = 1
}: FloatingCardProps) {
  const { ref, isHovered } = useHoverAnimation()

  return (
    <div
      ref={ref}
      className={cn(
        'transition-all duration-300 ease-out',
        'hover:shadow-lg',
        className
      )}
      style={{
        transform: isHovered 
          ? `translateY(-${4 * intensity}px) scale(${1 + 0.02 * intensity})` 
          : 'translateY(0px) scale(1)'
      }}
    >
      {children}
    </div>
  )
}

interface PulseProps {
  children: ReactNode
  className?: string
  color?: string
  size?: 'sm' | 'md' | 'lg'
}

export function Pulse({
  children,
  className,
  color = 'primary',
  size = 'md'
}: PulseProps) {
  const sizeClasses = {
    sm: 'w-2 h-2',
    md: 'w-3 h-3',
    lg: 'w-4 h-4'
  }

  return (
    <div className={cn('relative inline-flex items-center', className)}>
      <div className={cn(
        'absolute rounded-full animate-ping',
        sizeClasses[size],
        `bg-${color}-400`
      )} />
      <div className={cn(
        'relative rounded-full',
        sizeClasses[size],
        `bg-${color}-500`
      )} />
      {children && <span className="ml-2">{children}</span>}
    </div>
  )
}

interface ShimmerProps {
  className?: string
  children?: ReactNode
}

export function Shimmer({ className, children }: ShimmerProps) {
  return (
    <div className={cn('relative overflow-hidden', className)}>
      <div className="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/20 to-transparent" />
      {children}
    </div>
  )
}

interface GlowProps {
  children: ReactNode
  className?: string
  color?: string
  intensity?: number
}

export function Glow({
  children,
  className,
  color = 'primary',
  intensity = 1
}: GlowProps) {
  return (
    <div
      className={cn('relative', className)}
      style={{
        filter: `drop-shadow(0 0 ${8 * intensity}px var(--color-${color}-400))`
      }}
    >
      {children}
    </div>
  )
}

interface MorphingButtonProps {
  children: ReactNode
  className?: string
  morphTo?: ReactNode
  trigger?: boolean
}

export function MorphingButton({
  children,
  className,
  morphTo,
  trigger = false
}: MorphingButtonProps) {
  return (
    <div className={cn('relative overflow-hidden', className)}>
      <div
        className={cn(
          'transition-all duration-300 ease-in-out',
          trigger ? 'opacity-0 scale-95' : 'opacity-100 scale-100'
        )}
      >
        {children}
      </div>
      {morphTo && (
        <div
          className={cn(
            'absolute inset-0 flex items-center justify-center',
            'transition-all duration-300 ease-in-out',
            trigger ? 'opacity-100 scale-100' : 'opacity-0 scale-95'
          )}
        >
          {morphTo}
        </div>
      )}
    </div>
  )
}

interface ParticleFieldProps {
  className?: string
  particleCount?: number
  color?: string
}

export function ParticleField({
  className,
  particleCount = 50,
  color = 'primary'
}: ParticleFieldProps) {
  const particles = Array.from({ length: particleCount }, (_, i) => (
    <div
      key={i}
      className={cn(
        'absolute w-1 h-1 rounded-full opacity-30',
        `bg-${color}-400`,
        'animate-float'
      )}
      style={{
        left: `${Math.random() * 100}%`,
        top: `${Math.random() * 100}%`,
        animationDelay: `${Math.random() * 3}s`,
        animationDuration: `${3 + Math.random() * 2}s`
      }}
    />
  ))

  return (
    <div className={cn('absolute inset-0 overflow-hidden pointer-events-none', className)}>
      {particles}
    </div>
  )
}

interface ProgressRingProps {
  progress: number
  size?: number
  strokeWidth?: number
  className?: string
  color?: string
}

export function ProgressRing({
  progress,
  size = 120,
  strokeWidth = 8,
  className,
  color = 'primary'
}: ProgressRingProps) {
  const radius = (size - strokeWidth) / 2
  const circumference = radius * 2 * Math.PI
  const strokeDashoffset = circumference - (progress / 100) * circumference

  return (
    <div className={cn('relative', className)}>
      <svg
        width={size}
        height={size}
        className="transform -rotate-90"
      >
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke="currentColor"
          strokeWidth={strokeWidth}
          fill="none"
          className="text-neutral-200"
        />
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke="currentColor"
          strokeWidth={strokeWidth}
          fill="none"
          strokeDasharray={circumference}
          strokeDashoffset={strokeDashoffset}
          strokeLinecap="round"
          className={cn(`text-${color}-500`, 'transition-all duration-1000 ease-out')}
        />
      </svg>
      <div className="absolute inset-0 flex items-center justify-center">
        <CountUp end={progress} suffix="%" className="text-2xl font-bold" />
      </div>
    </div>
  )
}
