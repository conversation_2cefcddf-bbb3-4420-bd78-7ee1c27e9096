'use client'

import React, { useEffect, useState } from 'react'
import { usePathname } from 'next/navigation'
import { cn } from '@/lib/utils'

interface PageTransitionProps {
  children: React.ReactNode
  className?: string
}

export default function PageTransition({ children, className }: PageTransitionProps) {
  const pathname = usePathname()
  const [isLoading, setIsLoading] = useState(false)
  const [displayChildren, setDisplayChildren] = useState(children)

  useEffect(() => {
    setIsLoading(true)
    
    // 延迟更新内容，创建平滑过渡
    const timer = setTimeout(() => {
      setDisplayChildren(children)
      setIsLoading(false)
    }, 150)

    return () => clearTimeout(timer)
  }, [pathname, children])

  return (
    <div className={cn('relative', className)}>
      {/* 加载遮罩 */}
      {isLoading && (
        <div className="absolute inset-0 z-10 flex items-center justify-center bg-gradient-to-br from-primary-50/80 to-secondary-50/80 backdrop-blur-sm">
          <div className="flex items-center space-x-3">
            <div className="loading-spinner w-6 h-6" />
            <span className="text-sm font-medium text-primary-600">切换中...</span>
          </div>
        </div>
      )}
      
      {/* 页面内容 */}
      <div
        className={cn(
          'transition-all duration-600 ease-out',
          isLoading 
            ? 'opacity-0 scale-98 translate-y-2' 
            : 'opacity-100 scale-100 translate-y-0 page-enter'
        )}
      >
        {displayChildren}
      </div>
    </div>
  )
}

// 页面包装器组件，提供统一的页面结构和动效
interface PageWrapperProps {
  children: React.ReactNode
  title?: string
  subtitle?: string
  className?: string
  showBackButton?: boolean
  onBack?: () => void
}

export function PageWrapper({ 
  children, 
  title, 
  subtitle, 
  className,
  showBackButton = false,
  onBack
}: PageWrapperProps) {
  return (
    <PageTransition>
      <div className={cn('space-y-6', className)}>
        {/* 页面头部 */}
        {(title || subtitle || showBackButton) && (
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              {showBackButton && (
                <button
                  onClick={onBack}
                  className="flex items-center text-sm text-primary-600 hover:text-primary-700 transition-colors mb-2 flow-hover"
                >
                  <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                  </svg>
                  返回
                </button>
              )}
              {title && (
                <h1 className="text-3xl font-bold gradient-text">
                  {title}
                </h1>
              )}
              {subtitle && (
                <p className="text-neutral-600">
                  {subtitle}
                </p>
              )}
            </div>
          </div>
        )}
        
        {/* 页面内容 */}
        <div className="space-y-6">
          {children}
        </div>
      </div>
    </PageTransition>
  )
}

/**
 * 重复组件已删除 - 使用CSS类替代
 *
 * 替代方案：
 * - CardContainer -> 使用 className="card"
 * - ButtonContainer -> 使用 className="btn btn-primary"
 * - InputContainer -> 使用 className="input" 或 <Input> 组件
 *
 * 示例：
 * <div className="card">内容</div>
 * <button className="btn btn-primary">按钮</button>
 * <input className="input" placeholder="输入" />
 */

// 列表容器组件，提供交错动画
interface ListContainerProps {
  children: React.ReactNode[]
  className?: string
  stagger?: boolean
  delay?: number
}

export function ListContainer({ 
  children, 
  className,
  stagger = true,
  delay = 100
}: ListContainerProps) {
  if (!stagger) {
    return <div className={className}>{children}</div>
  }

  return (
    <div className={className}>
      {children.map((child, index) => (
        <div
          key={index}
          className="animate-fadeIn"
          style={{ animationDelay: `${index * delay}ms` }}
        >
          {child}
        </div>
      ))}
    </div>
  )
}

// 工具栏组件，提供统一的工具栏样式
interface ToolbarProps {
  children: React.ReactNode
  className?: string
  sticky?: boolean
}

export function Toolbar({ children, className, sticky = false }: ToolbarProps) {
  return (
    <div
      className={cn(
        'flex items-center justify-between p-4 bg-surface border-b border-border-light',
        sticky && 'sticky top-0 z-20 backdrop-blur-lg',
        className
      )}
      style={{
        backgroundColor: sticky ? 'var(--color-surface)' : undefined,
        borderColor: 'var(--color-border-light)'
      }}
    >
      {children}
    </div>
  )
}
