/**
 * 自定义背景组件
 *
 * 功能说明：
 * - 根据用户设置显示自定义背景图片
 * - 支持背景透明度和模糊度调节
 * - 集成增强背景动效
 * - 监听设置变更事件，实时更新背景
 *
 * 背景层级结构：
 * - z-index: -10 背景图片层（固定定位）
 * - z-index: -9 背景效果层（透明度+模糊）
 * - z-index: -5 渐变光晕效果层
 * - z-index: -4 波纹效果层
 * - z-index: -3 增强背景动效层
 * - z-index: -2 粒子效果层
 * - z-index: 0 内容层
 *
 * 设置字段：
 * - custom_background: 背景图片URL
 * - background_opacity: 背景透明度 (0-1)
 * - background_blur: 背景模糊度 (px)
 *
 * TODO: 添加背景图片预加载功能
 * TODO: 支持多张背景图片轮播
 */
'use client'

import { useEffect, useState } from 'react'
import { get } from '@/lib/api'
import EnhancedBackground from './EnhancedBackground'

interface UserSettings {
  custom_background: string | null
  background_opacity: number
  background_blur: number
}

interface CustomBackgroundProps {
  children: React.ReactNode
}

export default function CustomBackground({ children }: CustomBackgroundProps) {
  const [settings, setSettings] = useState<UserSettings | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    fetchSettings()

    // 监听设置变化事件
    const handleSettingsChange = () => {
      fetchSettings()
    }

    window.addEventListener('backgroundSettingsChanged', handleSettingsChange)

    return () => {
      window.removeEventListener('backgroundSettingsChanged', handleSettingsChange)
    }
  }, [])

  const fetchSettings = async () => {
    try {
      const settingsData = await get<UserSettings>('/auth/settings/')
      setSettings(settingsData)
    } catch (error) {
      console.error('Failed to fetch background settings:', error)
    } finally {
      setIsLoading(false)
    }
  }

  // 如果正在加载，显示加载状态
  if (isLoading) {
    return <>{children}</>
  }

  // 如果没有自定义背景，使用增强背景效果
  if (!settings?.custom_background) {
    return (
      <EnhancedBackground intensity="medium" showParticles={true}>
        {children}
      </EnhancedBackground>
    )
  }

  return (
    <EnhancedBackground intensity="high" showParticles={true}>
      <div className="relative min-h-screen">
        {/* 背景图片层 */}
        <div
          className="fixed inset-0 z-[-10]"
          style={{
            backgroundImage: `url(${settings.custom_background.startsWith('http')
              ? settings.custom_background
              : `${process.env.NEXT_PUBLIC_API_URL?.replace('/api', '') || 'http://localhost:8000'}${settings.custom_background}`})`,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            backgroundRepeat: 'no-repeat',
            backgroundAttachment: 'fixed'
          }}
        />

        {/* 背景效果层 */}
        <div
          className="fixed inset-0 z-[-9]"
          style={{
            backgroundColor: `rgba(255, 255, 255, ${1 - settings.background_opacity})`,
            backdropFilter: `blur(${settings.background_blur}px)`,
            WebkitBackdropFilter: `blur(${settings.background_blur}px)`
          }}
        />

        {/* 内容层 */}
        <div className="relative z-0">
          {children}
        </div>
      </div>
    </EnhancedBackground>
  )
}

// Hook for accessing background settings
export function useBackgroundSettings() {
  const [settings, setSettings] = useState<UserSettings | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    fetchSettings()
  }, [])

  const fetchSettings = async () => {
    try {
      const settingsData = await get<UserSettings>('/auth/settings/')
      setSettings(settingsData)
    } catch (error) {
      console.error('Failed to fetch background settings:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const refreshSettings = () => {
    fetchSettings()
  }

  return {
    settings,
    isLoading,
    refreshSettings
  }
}
