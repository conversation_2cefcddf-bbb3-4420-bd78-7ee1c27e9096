'use client'

import React, { useState, useEffect } from 'react'
import { useTheme } from '@/contexts/ThemeContext'

interface LoadingStage {
  id: string
  title: string
  description: string
  icon: React.ReactNode
  duration: number // 毫秒
}

interface StageLoaderProps {
  stages: LoadingStage[]
  onComplete?: () => void
  className?: string
}

const defaultStages: LoadingStage[] = [
  {
    id: 'init',
    title: '初始化应用',
    description: '正在启动灵境系统...',
    icon: (
      <div className="w-6 h-6 rounded-full bg-gradient-to-r from-primary-400 to-primary-600 animate-pulse" />
    ),
    duration: 800
  },
  {
    id: 'auth',
    title: '验证身份',
    description: '正在验证用户权限...',
    icon: (
      <div className="w-6 h-6 rounded-full bg-gradient-to-r from-secondary-400 to-secondary-600 animate-pulse" />
    ),
    duration: 800
  },
  {
    id: 'data',
    title: '加载数据',
    description: '正在获取个人成长数据...',
    icon: (
      <div className="w-6 h-6 rounded-full bg-gradient-to-r from-success-400 to-success-600 animate-pulse" />
    ),
    duration: 800
  },
  {
    id: 'ui',
    title: '构建界面',
    description: '正在渲染用户界面...',
    icon: (
      <div className="w-6 h-6 rounded-full bg-gradient-to-r from-warning-400 to-warning-600 animate-pulse" />
    ),
    duration: 800
  },
  {
    id: 'complete',
    title: '准备就绪',
    description: '欢迎回到灵境！',
    icon: (
      <div className="w-6 h-6 rounded-full bg-gradient-to-r from-primary-500 to-secondary-500 animate-bounce" />
    ),
    duration: 600
  }
]

export default function StageLoader({ 
  stages = defaultStages, 
  onComplete,
  className = '' 
}: StageLoaderProps) {
  const [currentStageIndex, setCurrentStageIndex] = useState(0)
  const [completedStages, setCompletedStages] = useState<Set<string>>(new Set())
  const [progress, setProgress] = useState(0)
  const { colors } = useTheme()

  useEffect(() => {
    if (currentStageIndex >= stages.length) {
      onComplete?.()
      return
    }

    const currentStage = stages[currentStageIndex]
    const timer = setTimeout(() => {
      setCompletedStages(prev => new Set(Array.from(prev).concat(currentStage.id)))
      setCurrentStageIndex(prev => prev + 1)
      setProgress((currentStageIndex + 1) / stages.length * 100)
    }, currentStage.duration)

    return () => clearTimeout(timer)
  }, [currentStageIndex, stages, onComplete])

  const currentStage = stages[currentStageIndex] || stages[stages.length - 1]

  return (
    <div className={`min-h-screen flex items-center justify-center bg-gradient-to-br from-primary-50 to-secondary-50 ${className}`}>
      <div className="max-w-md w-full mx-auto p-8">
        {/* 主要加载区域 */}
        <div className="text-center mb-8">
          <div className="mb-6">
            <div className="w-20 h-20 mx-auto mb-4 relative">
              {/* 外圈进度环 */}
              <svg className="w-20 h-20 transform -rotate-90" viewBox="0 0 80 80">
                <circle
                  cx="40"
                  cy="40"
                  r="36"
                  stroke="currentColor"
                  strokeWidth="4"
                  fill="none"
                  className="text-neutral-200"
                />
                <circle
                  cx="40"
                  cy="40"
                  r="36"
                  stroke="currentColor"
                  strokeWidth="4"
                  fill="none"
                  strokeDasharray={`${2 * Math.PI * 36}`}
                  strokeDashoffset={`${2 * Math.PI * 36 * (1 - progress / 100)}`}
                  className="text-primary-500 transition-all duration-300 ease-out"
                  strokeLinecap="round"
                />
              </svg>
              
              {/* 中心图标 */}
              <div className="absolute inset-0 flex items-center justify-center">
                {currentStage.icon}
              </div>
            </div>
            
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              {currentStage.title}
            </h2>
            <p className="text-gray-600">
              {currentStage.description}
            </p>
          </div>

          {/* 进度条 */}
          <div className="w-full bg-neutral-200 rounded-full h-2 mb-6">
            <div 
              className="bg-gradient-to-r from-primary-500 to-secondary-500 h-2 rounded-full transition-all duration-300 ease-out"
              style={{ width: `${progress}%` }}
            />
          </div>

          {/* 进度百分比 */}
          <div className="text-sm text-gray-500 mb-8">
            {Math.round(progress)}% 完成
          </div>
        </div>

        {/* 阶段列表 */}
        <div className="space-y-3">
          {stages.map((stage, index) => {
            const isCompleted = completedStages.has(stage.id)
            const isCurrent = index === currentStageIndex
            const isPending = index > currentStageIndex

            return (
              <div
                key={stage.id}
                className={`flex items-center p-3 rounded-lg transition-all duration-300 ${
                  isCompleted
                    ? 'bg-success-50 border border-success-200'
                    : isCurrent
                    ? 'bg-primary-50 border border-primary-200 shadow-sm'
                    : 'bg-neutral-50 border border-neutral-200'
                }`}
              >
                <div className="flex-shrink-0 mr-3">
                  {isCompleted ? (
                    <div className="w-5 h-5 rounded-full bg-success-500 flex items-center justify-center">
                      <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </div>
                  ) : isCurrent ? (
                    <div className="w-5 h-5 rounded-full bg-primary-500 animate-pulse" />
                  ) : (
                    <div className="w-5 h-5 rounded-full bg-neutral-300" />
                  )}
                </div>
                
                <div className="flex-1 min-w-0">
                  <p className={`text-sm font-medium ${
                    isCompleted
                      ? 'text-success-700'
                      : isCurrent
                      ? 'text-primary-700'
                      : 'text-neutral-500'
                  }`}>
                    {stage.title}
                  </p>
                  <p className={`text-xs ${
                    isCompleted
                      ? 'text-success-600'
                      : isCurrent
                      ? 'text-primary-600'
                      : 'text-neutral-400'
                  }`}>
                    {stage.description}
                  </p>
                </div>
              </div>
            )
          })}
        </div>

        {/* 品牌标识 */}
        <div className="text-center mt-8">
          <p className="text-xs text-gray-400">
            灵境 Mentia - 个人成长操作系统
          </p>
        </div>
      </div>
    </div>
  )
}

// 预设的加载阶段配置
export const loadingStages = {
  dashboard: [
    {
      id: 'init',
      title: '初始化仪表板',
      description: '正在准备概览数据...',
      icon: <div className="w-6 h-6 rounded-full bg-gradient-to-r from-primary-400 to-primary-600 animate-pulse" />,
      duration: 600
    },
    {
      id: 'stats',
      title: '加载统计数据',
      description: '正在获取成长统计...',
      icon: <div className="w-6 h-6 rounded-full bg-gradient-to-r from-secondary-400 to-secondary-600 animate-pulse" />,
      duration: 800
    },
    {
      id: 'timeline',
      title: '构建时间线',
      description: '正在整理最近活动...',
      icon: <div className="w-6 h-6 rounded-full bg-gradient-to-r from-success-400 to-success-600 animate-pulse" />,
      duration: 700
    },
    {
      id: 'complete',
      title: '准备就绪',
      description: '欢迎回到您的成长空间！',
      icon: <div className="w-6 h-6 rounded-full bg-gradient-to-r from-primary-500 to-secondary-500 animate-bounce" />,
      duration: 500
    }
  ],
  
  growth: [
    {
      id: 'init',
      title: '初始化成长引擎',
      description: '正在启动成长管理系统...',
      icon: <div className="w-6 h-6 rounded-full bg-gradient-to-r from-primary-400 to-primary-600 animate-pulse" />,
      duration: 600
    },
    {
      id: 'items',
      title: '加载成长项目',
      description: '正在获取您的目标和任务...',
      icon: <div className="w-6 h-6 rounded-full bg-gradient-to-r from-secondary-400 to-secondary-600 animate-pulse" />,
      duration: 900
    },
    {
      id: 'tags',
      title: '整理标签系统',
      description: '正在分类整理标签...',
      icon: <div className="w-6 h-6 rounded-full bg-gradient-to-r from-warning-400 to-warning-600 animate-pulse" />,
      duration: 600
    },
    {
      id: 'complete',
      title: '引擎就绪',
      description: '开始您的成长之旅！',
      icon: <div className="w-6 h-6 rounded-full bg-gradient-to-r from-primary-500 to-secondary-500 animate-bounce" />,
      duration: 500
    }
  ]
}
