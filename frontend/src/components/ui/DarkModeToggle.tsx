'use client'

import React from 'react'
import { SunIcon, MoonIcon } from '@heroicons/react/24/outline'
import { useTheme } from '@/contexts/ThemeContext'
import { cn } from '@/lib/utils'

interface DarkModeToggleProps {
  className?: string
  size?: 'sm' | 'md' | 'lg'
  showLabel?: boolean
}

export default function DarkModeToggle({ 
  className, 
  size = 'md', 
  showLabel = false 
}: DarkModeToggleProps) {
  const { isDarkMode, toggleDarkMode } = useTheme()

  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-10 h-10',
    lg: 'w-12 h-12'
  }

  const iconSizes = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6'
  }

  return (
    <div className={cn('flex items-center gap-2', className)}>
      {showLabel && (
        <span className="text-sm font-medium text-neutral-700 dark:text-neutral-300">
          {isDarkMode ? '暗色模式' : '亮色模式'}
        </span>
      )}
      
      <button
        onClick={toggleDarkMode}
        className={cn(
          'relative inline-flex items-center justify-center rounded-full',
          'glass-button overflow-hidden group',
          'focus:outline-none focus:ring-2 focus:ring-primary-400 focus:ring-offset-2',
          'transition-all duration-500 ease-out',
          'transform hover:scale-105 active:scale-95',
          sizeClasses[size]
        )}
        aria-label={isDarkMode ? '切换到亮色模式' : '切换到暗色模式'}
      >
        {/* 磨砂玻璃背景 */}
        <div
          className={cn(
            'absolute inset-0 rounded-full transition-all duration-500',
            'backdrop-filter backdrop-blur-md',
            isDarkMode
              ? 'bg-gradient-to-br from-slate-800/40 to-slate-900/60 border border-slate-600/30'
              : 'bg-gradient-to-br from-white/40 to-white/60 border border-white/30'
          )}
        />

        {/* 内部光效 */}
        <div
          className={cn(
            'absolute inset-0 rounded-full transition-all duration-500',
            'bg-gradient-to-br opacity-60',
            isDarkMode
              ? 'from-blue-400/20 via-purple-400/20 to-indigo-400/20'
              : 'from-yellow-300/30 via-orange-300/30 to-amber-300/30'
          )}
        />

        {/* 图标容器 */}
        <div className="relative z-10 flex items-center justify-center">
          {isDarkMode ? (
            <MoonIcon
              className={cn(
                iconSizes[size],
                'text-slate-200 group-hover:text-white transition-colors duration-300',
                'drop-shadow-sm'
              )}
            />
          ) : (
            <SunIcon
              className={cn(
                iconSizes[size],
                'text-amber-600 group-hover:text-amber-500 transition-colors duration-300',
                'drop-shadow-sm'
              )}
            />
          )}
        </div>

        {/* Hover光晕效果 */}
        <div
          className={cn(
            'absolute inset-0 rounded-full transition-all duration-300',
            'opacity-0 group-hover:opacity-100',
            'bg-gradient-to-br',
            isDarkMode
              ? 'from-blue-400/10 via-purple-400/10 to-indigo-400/10'
              : 'from-yellow-300/20 via-orange-300/20 to-amber-300/20'
          )}
        />

        {/* 边缘高光 */}
        <div
          className={cn(
            'absolute inset-0 rounded-full transition-all duration-300',
            'opacity-0 group-hover:opacity-100',
            isDarkMode
              ? 'shadow-[inset_0_1px_0_rgba(255,255,255,0.1)]'
              : 'shadow-[inset_0_1px_0_rgba(255,255,255,0.4)]'
          )}
        />
      </button>
    </div>
  )
}

// 简化版本的切换开关
export function DarkModeSwitch({ className }: { className?: string }) {
  const { isDarkMode, toggleDarkMode } = useTheme()

  return (
    <button
      onClick={toggleDarkMode}
      className={cn(
        'relative inline-flex h-6 w-11 items-center rounded-full transition-colors',
        'focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2',
        isDarkMode ? 'bg-primary-600' : 'bg-neutral-200',
        className
      )}
      role="switch"
      aria-checked={isDarkMode}
      aria-label="切换暗色模式"
    >
      <span
        className={cn(
          'inline-block h-4 w-4 transform rounded-full bg-white transition-transform',
          'shadow-lg ring-0',
          isDarkMode ? 'translate-x-6' : 'translate-x-1'
        )}
      >
        {/* 小图标 */}
        <span className="flex h-full w-full items-center justify-center">
          {isDarkMode ? (
            <MoonIcon className="h-2.5 w-2.5 text-primary-600" />
          ) : (
            <SunIcon className="h-2.5 w-2.5 text-yellow-500" />
          )}
        </span>
      </span>
    </button>
  )
}

// 文字版本的切换器
export function DarkModeTextToggle({ className }: { className?: string }) {
  const { isDarkMode, toggleDarkMode } = useTheme()

  return (
    <button
      onClick={toggleDarkMode}
      className={cn(
        'flex items-center gap-2 px-3 py-2 rounded-lg',
        'text-sm font-medium transition-colors',
        'hover:bg-neutral-100 dark:hover:bg-neutral-800',
        'text-neutral-700 dark:text-neutral-300',
        'flow-hover',
        className
      )}
    >
      {isDarkMode ? (
        <>
          <SunIcon className="w-4 h-4" />
          <span>亮色模式</span>
        </>
      ) : (
        <>
          <MoonIcon className="w-4 h-4" />
          <span>暗色模式</span>
        </>
      )}
    </button>
  )
}

// 菜单项版本
export function DarkModeMenuItem({ className }: { className?: string }) {
  const { isDarkMode, toggleDarkMode } = useTheme()

  return (
    <div
      className={cn(
        'flex items-center justify-between px-4 py-2',
        'text-sm text-neutral-700 dark:text-neutral-300',
        className
      )}
    >
      <div className="flex items-center gap-3">
        {isDarkMode ? (
          <MoonIcon className="w-4 h-4" />
        ) : (
          <SunIcon className="w-4 h-4" />
        )}
        <span>暗色模式</span>
      </div>
      
      <DarkModeSwitch />
    </div>
  )
}
