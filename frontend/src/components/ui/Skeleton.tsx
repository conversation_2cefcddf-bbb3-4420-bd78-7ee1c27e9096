'use client'

import React from 'react'
import { cn } from '@/lib/utils'

interface SkeletonProps {
  className?: string
  variant?: 'text' | 'circular' | 'rectangular' | 'rounded'
  width?: string | number
  height?: string | number
  animation?: 'pulse' | 'wave' | 'none'
}

export function Skeleton({
  className,
  variant = 'rectangular',
  width,
  height,
  animation = 'pulse',
  ...props
}: SkeletonProps & React.HTMLAttributes<HTMLDivElement>) {
  const baseClasses = 'bg-neutral-200 animate-pulse'
  
  const variantClasses = {
    text: 'h-4 rounded',
    circular: 'rounded-full',
    rectangular: 'rounded',
    rounded: 'rounded-lg'
  }

  const animationClasses = {
    pulse: 'animate-pulse',
    wave: 'animate-wave',
    none: ''
  }

  const style: React.CSSProperties = {}
  if (width) style.width = typeof width === 'number' ? `${width}px` : width
  if (height) style.height = typeof height === 'number' ? `${height}px` : height

  return (
    <div
      className={cn(
        baseClasses,
        variantClasses[variant],
        animationClasses[animation],
        className
      )}
      style={style}
      {...props}
    />
  )
}

// 预设的骨架屏组件
export function CardSkeleton({ className }: { className?: string }) {
  return (
    <div className={cn('card p-6 space-y-4', className)}>
      <div className="flex items-center space-x-4">
        <Skeleton variant="circular" width={40} height={40} />
        <div className="space-y-2 flex-1">
          <Skeleton variant="text" className="h-4 w-3/4" />
          <Skeleton variant="text" className="h-3 w-1/2" />
        </div>
      </div>
      <div className="space-y-2">
        <Skeleton variant="text" className="h-3 w-full" />
        <Skeleton variant="text" className="h-3 w-5/6" />
        <Skeleton variant="text" className="h-3 w-4/6" />
      </div>
    </div>
  )
}

export function DashboardSkeleton() {
  return (
    <div className="space-y-8">
      {/* 欢迎区域骨架 */}
      <div className="card p-6">
        <div className="space-y-4">
          <Skeleton variant="text" className="h-8 w-1/3" />
          <Skeleton variant="text" className="h-4 w-2/3" />
          <div className="flex space-x-4 mt-6">
            <Skeleton variant="rounded" className="h-10 w-32" />
            <Skeleton variant="rounded" className="h-10 w-28" />
          </div>
        </div>
      </div>

      {/* 统计卡片骨架 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {Array.from({ length: 4 }).map((_, i) => (
          <div key={i} className="card p-6">
            <div className="flex items-center justify-between">
              <div className="space-y-2">
                <Skeleton variant="text" className="h-4 w-16" />
                <Skeleton variant="text" className="h-8 w-12" />
              </div>
              <Skeleton variant="circular" width={48} height={48} />
            </div>
          </div>
        ))}
      </div>

      {/* 内容区域骨架 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div className="space-y-6">
          <div className="card p-6">
            <Skeleton variant="text" className="h-6 w-1/4 mb-4" />
            <div className="space-y-4">
              {Array.from({ length: 3 }).map((_, i) => (
                <div key={i} className="flex items-center space-x-3">
                  <Skeleton variant="circular" width={32} height={32} />
                  <div className="flex-1 space-y-2">
                    <Skeleton variant="text" className="h-4 w-3/4" />
                    <Skeleton variant="text" className="h-3 w-1/2" />
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        <div className="space-y-6">
          <div className="card p-6">
            <Skeleton variant="text" className="h-6 w-1/3 mb-4" />
            <div className="grid grid-cols-2 gap-4">
              {Array.from({ length: 4 }).map((_, i) => (
                <div key={i} className="flex items-center p-4 border border-neutral-200 rounded-lg">
                  <Skeleton variant="circular" width={32} height={32} className="mr-4" />
                  <div className="space-y-2">
                    <Skeleton variant="text" className="h-4 w-20" />
                    <Skeleton variant="text" className="h-3 w-16" />
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export function GrowthPageSkeleton() {
  return (
    <div className="space-y-6">
      {/* 头部操作区域 */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="space-y-2">
          <Skeleton variant="text" className="h-8 w-48" />
          <Skeleton variant="text" className="h-4 w-64" />
        </div>
        <div className="flex space-x-3">
          <Skeleton variant="rounded" className="h-10 w-24" />
          <Skeleton variant="rounded" className="h-10 w-32" />
        </div>
      </div>

      {/* 过滤器区域 */}
      <div className="flex flex-wrap gap-4">
        {Array.from({ length: 4 }).map((_, i) => (
          <Skeleton key={i} variant="rounded" className="h-8 w-20" />
        ))}
      </div>

      {/* 成长项目列表 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {Array.from({ length: 6 }).map((_, i) => (
          <div key={i} className="card p-6">
            <div className="space-y-4">
              <div className="flex items-start justify-between">
                <div className="flex-1 space-y-2">
                  <Skeleton variant="text" className="h-5 w-3/4" />
                  <Skeleton variant="rounded" className="h-5 w-16" />
                </div>
                <Skeleton variant="circular" width={24} height={24} />
              </div>
              
              <div className="space-y-2">
                <Skeleton variant="text" className="h-3 w-full" />
                <Skeleton variant="text" className="h-3 w-5/6" />
              </div>

              <div className="flex items-center justify-between">
                <div className="flex space-x-2">
                  <Skeleton variant="rounded" className="h-5 w-12" />
                  <Skeleton variant="rounded" className="h-5 w-16" />
                </div>
                <Skeleton variant="text" className="h-3 w-20" />
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

export function BlogPageSkeleton() {
  return (
    <div className="space-y-8">
      {/* 头部区域 */}
      <div className="flex justify-between items-center">
        <div className="space-y-2">
          <Skeleton variant="text" className="h-8 w-32" />
          <Skeleton variant="text" className="h-4 w-48" />
        </div>
        <Skeleton variant="rounded" className="h-10 w-24" />
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {Array.from({ length: 4 }).map((_, i) => (
          <div key={i} className="card p-4 text-center">
            <Skeleton variant="text" className="h-8 w-12 mx-auto mb-2" />
            <Skeleton variant="text" className="h-4 w-16 mx-auto" />
          </div>
        ))}
      </div>

      {/* 搜索和过滤器 */}
      <div className="flex flex-col sm:flex-row gap-4">
        <Skeleton variant="rounded" className="h-10 flex-1" />
        <Skeleton variant="rounded" className="h-10 w-32" />
        <Skeleton variant="rounded" className="h-10 w-24" />
      </div>

      {/* 文章列表 */}
      <div className="space-y-4">
        {Array.from({ length: 5 }).map((_, i) => (
          <div key={i} className="card p-6">
            <div className="flex items-start space-x-4">
              <div className="flex-1 space-y-3">
                <div className="flex items-center space-x-2">
                  <Skeleton variant="rounded" className="h-5 w-12" />
                  <Skeleton variant="text" className="h-4 w-24" />
                </div>
                <Skeleton variant="text" className="h-6 w-3/4" />
                <div className="space-y-2">
                  <Skeleton variant="text" className="h-4 w-full" />
                  <Skeleton variant="text" className="h-4 w-5/6" />
                </div>
                <div className="flex items-center space-x-4">
                  <Skeleton variant="text" className="h-3 w-20" />
                  <Skeleton variant="text" className="h-3 w-16" />
                  <Skeleton variant="text" className="h-3 w-12" />
                </div>
              </div>
              <Skeleton variant="rounded" width={120} height={80} />
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

export function CompassPageSkeleton() {
  return (
    <div className="space-y-8">
      {/* 头部 */}
      <div className="text-center space-y-4">
        <Skeleton variant="text" className="h-8 w-48 mx-auto" />
        <Skeleton variant="text" className="h-4 w-64 mx-auto" />
      </div>

      {/* 主要指标 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {Array.from({ length: 3 }).map((_, i) => (
          <div key={i} className="card p-6 text-center">
            <Skeleton variant="circular" width={80} height={80} className="mx-auto mb-4" />
            <Skeleton variant="text" className="h-6 w-24 mx-auto mb-2" />
            <Skeleton variant="text" className="h-8 w-16 mx-auto mb-2" />
            <Skeleton variant="text" className="h-4 w-32 mx-auto" />
          </div>
        ))}
      </div>

      {/* 技能图谱和价值观 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div className="card p-6">
          <Skeleton variant="text" className="h-6 w-32 mb-6" />
          <div className="space-y-4">
            {Array.from({ length: 6 }).map((_, i) => (
              <div key={i} className="flex items-center justify-between">
                <Skeleton variant="text" className="h-4 w-24" />
                <div className="flex-1 mx-4">
                  <Skeleton variant="rounded" className="h-2 w-full" />
                </div>
                <Skeleton variant="text" className="h-4 w-12" />
              </div>
            ))}
          </div>
        </div>

        <div className="card p-6">
          <Skeleton variant="text" className="h-6 w-32 mb-6" />
          <div className="space-y-4">
            {Array.from({ length: 4 }).map((_, i) => (
              <div key={i} className="p-4 border border-neutral-200 rounded-lg">
                <Skeleton variant="text" className="h-5 w-32 mb-2" />
                <Skeleton variant="text" className="h-4 w-full" />
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}
