/**
 * 背景图片上传组件
 *
 * 功能说明：
 * - 支持用户上传自定义背景图片（JPEG、PNG、WebP、GIF格式）
 * - 提供实时预览功能
 * - 支持背景透明度和模糊度调节
 * - 集成文件验证（类型、大小限制）
 * - 与后端 /api/auth/background/upload/ 接口交互
 *
 * 使用方式：
 * <BackgroundUpload
 *   currentBackground={backgroundUrl}
 *   onBackgroundChange={(url) => setBackground(url)}
 *   onOpacityChange={(opacity) => setOpacity(opacity)}
 * />
 *
 * TODO: 添加图片裁剪功能
 * TODO: 支持从URL导入背景图片
 */
'use client'

import { useState, useRef } from 'react'
import {
  PhotoIcon,
  TrashIcon,
  CloudArrowUpIcon,
  EyeIcon,
  AdjustmentsHorizontalIcon
} from '@heroicons/react/24/outline'
import toast from 'react-hot-toast'
import { post, del } from '@/lib/api'
import { handleError, ErrorPresets } from '@/lib/errorHandler'

interface BackgroundUploadProps {
  currentBackground?: string
  backgroundOpacity?: number
  backgroundBlur?: number
  onBackgroundChange: (backgroundUrl: string | null) => void
  onOpacityChange: (opacity: number) => void
  onBlurChange: (blur: number) => void
}

export default function BackgroundUpload({
  currentBackground,
  backgroundOpacity = 0.3,
  backgroundBlur = 0,
  onBackgroundChange,
  onOpacityChange,
  onBlurChange
}: BackgroundUploadProps) {
  const [isUploading, setIsUploading] = useState(false)
  const [previewUrl, setPreviewUrl] = useState<string | null>(currentBackground || null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  /**
   * 处理文件选择事件
   * 增强版本：更严格的客户端验证和错误处理
   */
  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    try {
      // 增强的文件验证
      const validationResult = validateFile(file)
      if (!validationResult.isValid) {
        toast.error(validationResult.error)
        return
      }

      // 创建预览
      const reader = new FileReader()
      reader.onload = (e) => {
        setPreviewUrl(e.target?.result as string)
      }
      reader.onerror = () => {
        toast.error('文件读取失败，请重试')
      }
      reader.readAsDataURL(file)

      // 上传文件
      await uploadBackground(file)
    } catch (error) {
      handleError(error, ErrorPresets.form('background-upload'))
    }
  }

  /**
   * 增强的文件验证函数
   * 包含更多安全检查
   */
  const validateFile = (file: File): { isValid: boolean; error: string } => {
    // 验证文件类型
    const allowedTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/gif']
    if (!allowedTypes.includes(file.type)) {
      return {
        isValid: false,
        error: '不支持的文件类型，请上传 JPEG、PNG、WebP 或 GIF 格式的图片'
      }
    }

    // 验证文件大小 (最大 10MB)
    if (file.size > 10 * 1024 * 1024) {
      return {
        isValid: false,
        error: '文件大小不能超过 10MB'
      }
    }

    // 验证文件名
    if (!file.name || file.name.length > 255) {
      return {
        isValid: false,
        error: '文件名无效或过长'
      }
    }

    // 检查文件名中的危险字符
    const dangerousChars = /[<>:"/\\|?*]/
    if (dangerousChars.test(file.name)) {
      return {
        isValid: false,
        error: '文件名包含非法字符'
      }
    }

    // 验证最小文件大小（防止空文件）
    if (file.size < 100) {
      return {
        isValid: false,
        error: '文件太小，可能已损坏'
      }
    }

    return { isValid: true, error: '' }
  }

  /**
   * 上传背景图片到服务器
   * 增强版本：更好的错误处理和用户反馈
   */
  const uploadBackground = async (file: File) => {
    setIsUploading(true)

    try {
      const formData = new FormData()
      formData.append('background', file)

      const response = await post('/auth/background/upload/', formData)

      onBackgroundChange(response.background_url)
      toast.success('背景图片上传成功')

      // 记录成功上传的文件信息
      console.log('Background uploaded:', {
        filename: file.name,
        size: file.size,
        type: file.type,
        url: response.background_url
      })
    } catch (error: any) {
      // 使用统一错误处理
      handleError(error, {
        context: 'background-upload',
        showToast: true,
        fallbackMessage: '背景图片上传失败，请检查文件格式和网络连接'
      })

      // 恢复预览状态
      setPreviewUrl(currentBackground || null)
    } finally {
      setIsUploading(false)
    }
  }

  /**
   * 删除背景图片
   * 增强版本：更好的错误处理和确认机制
   */
  const handleRemoveBackground = async () => {
    try {
      await del('/auth/background/upload/')

      setPreviewUrl(null)
      onBackgroundChange(null)
      toast.success('背景图片删除成功')
    } catch (error: any) {
      // 使用统一错误处理
      handleError(error, {
        context: 'background-delete',
        showToast: true,
        fallbackMessage: '背景图片删除失败，请稍后重试'
      })
    }
  }

  return (
    <div className="space-y-6">
      {/* 背景预览 */}
      <div className="relative">
        <label className="block text-sm font-medium text-gray-700 mb-3">
          <PhotoIcon className="w-4 h-4 inline mr-1" />
          背景图片
        </label>
        
        <div 
          className="relative w-full h-48 rounded-lg border-2 border-dashed border-gray-300 hover:border-gray-400 transition-colors overflow-hidden"
          style={{
            backgroundImage: previewUrl ? `url(${previewUrl})` : undefined,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            backgroundRepeat: 'no-repeat'
          }}
        >
          {/* 背景效果预览 */}
          {previewUrl && (
            <div 
              className="absolute inset-0"
              style={{
                backgroundColor: `rgba(255, 255, 255, ${1 - backgroundOpacity})`,
                backdropFilter: `blur(${backgroundBlur}px)`,
                WebkitBackdropFilter: `blur(${backgroundBlur}px)`
              }}
            />
          )}
          
          {/* 上传区域 */}
          <div className="absolute inset-0 flex items-center justify-center">
            {!previewUrl ? (
              <div className="text-center">
                <CloudArrowUpIcon className="mx-auto h-12 w-12 text-gray-400" />
                <div className="mt-4">
                  <button
                    type="button"
                    onClick={() => fileInputRef.current?.click()}
                    className="btn btn-primary"
                    disabled={isUploading}
                  >
                    {isUploading ? '上传中...' : '选择背景图片'}
                  </button>
                </div>
                <p className="mt-2 text-sm text-gray-500">
                  支持 JPEG、PNG、WebP、GIF 格式，最大 10MB
                </p>
                <p className="mt-1 text-xs text-gray-400">
                  推荐尺寸：1920x1080 或更高分辨率，确保最佳显示效果
                </p>
              </div>
            ) : (
              <div className="flex gap-2">
                <button
                  type="button"
                  onClick={() => fileInputRef.current?.click()}
                  className="btn btn-secondary"
                  disabled={isUploading}
                >
                  <PhotoIcon className="w-4 h-4 mr-1" />
                  更换
                </button>
                <button
                  type="button"
                  onClick={handleRemoveBackground}
                  className="btn btn-danger"
                >
                  <TrashIcon className="w-4 h-4 mr-1" />
                  删除
                </button>
              </div>
            )}
          </div>
        </div>

        <input
          ref={fileInputRef}
          type="file"
          accept="image/jpeg,image/png,image/webp,image/gif"
          onChange={handleFileSelect}
          className="hidden"
        />
      </div>

      {/* 背景调节选项 */}
      {previewUrl && (
        <div className="space-y-4">
          <h4 className="text-sm font-medium text-gray-700 flex items-center">
            <AdjustmentsHorizontalIcon className="w-4 h-4 mr-1" />
            背景效果调节
          </h4>
          
          {/* 透明度调节 */}
          <div>
            <label className="block text-sm text-gray-600 mb-2">
              <EyeIcon className="w-4 h-4 inline mr-1" />
              透明度: {Math.round(backgroundOpacity * 100)}%
            </label>
            <input
              type="range"
              min="0"
              max="1"
              step="0.1"
              value={backgroundOpacity}
              onChange={(e) => onOpacityChange(parseFloat(e.target.value))}
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
            />
          </div>

          {/* 模糊度调节 */}
          <div>
            <label className="block text-sm text-gray-600 mb-2">
              模糊度: {backgroundBlur}px
            </label>
            <input
              type="range"
              min="0"
              max="20"
              step="1"
              value={backgroundBlur}
              onChange={(e) => onBlurChange(parseInt(e.target.value))}
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
            />
          </div>
        </div>
      )}
    </div>
  )
}
