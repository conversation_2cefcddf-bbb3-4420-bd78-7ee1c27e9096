/**
 * UI组件统一导出
 * 提供一致的导入接口，提高组件复用性
 */

// 基础组件
export { default as Button } from './Button'
export type { ButtonProps } from './Button'

export { default as Input } from './Input'
export type { InputProps } from './Input'

// 反馈组件

export { ToastProvider, CustomToast, showToast } from './Toast'
export type { CustomToastProps } from './Toast'

export { default as DarkModeToggle, DarkModeSwitch, DarkModeTextToggle, DarkModeMenuItem } from './DarkModeToggle'

// 加载组件
export { default as StageLoader, loadingStages } from './StageLoader'

export {
  Skeleton,
  CardSkeleton,
  DashboardSkeleton,
  GrowthPageSkeleton,
  BlogPageSkeleton,
  CompassPageSkeleton
} from './Skeleton'

// 动画组件
export {
  AnimatedContainer,
  StaggeredList,
  CountUp,
  Typewriter,
  FloatingCard,
  Pulse,
  Shimmer,
  Glow,
  MorphingButton,
  ParticleField,
  ProgressRing
} from './AnimatedComponents'
