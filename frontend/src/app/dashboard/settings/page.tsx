/**
 * 用户设置页面
 *
 * 功能说明：
 * - 提供用户个性化设置界面
 * - 支持主题、通知、隐私、功能、背景等设置
 * - 实时保存设置到后端（防抖优化）
 * - 跨设备/会话持久化设置
 *
 * 设置分类：
 * - 主题设置：主题模式、主题色
 * - 通知设置：邮件、推送、周报通知
 * - 隐私设置：资料可见性
 * - 功能设置：自动保存间隔、默认首页
 * - 背景设置：自定义背景、透明度、模糊度
 *
 * API交互：
 * - GET /auth/settings/ - 获取用户设置
 * - PATCH /auth/settings/ - 更新用户设置
 * - POST /auth/background/upload/ - 上传背景图片
 *
 * 事件机制：
 * - backgroundSettingsChanged - 背景设置变更事件
 *
 * TODO: 添加设置导入/导出功能
 * TODO: 添加设置重置功能
 */
'use client'

import { useState, useEffect, useCallback } from 'react'
import { 
  PaintBrushIcon,
  BellIcon,
  ShieldCheckIcon,
  Cog6ToothIcon,
  EyeIcon,
  ClockIcon,
  HomeIcon
} from '@heroicons/react/24/outline'
import { get, patch } from '@/lib/api'
import toast from 'react-hot-toast'
import { DarkModeSwitch } from '@/components/ui/DarkModeToggle'
import BackgroundUpload from '@/components/ui/BackgroundUpload'
import { useTheme } from '@/contexts/ThemeContext'

interface UserSettings {
  settings_id: string
  theme: 'light' | 'dark' | 'auto'
  primary_color: string
  email_notifications: boolean
  push_notifications: boolean
  weekly_summary: boolean
  profile_visibility: 'public' | 'private'
  auto_save_interval: number
  default_view: 'dashboard' | 'growth' | 'compass' | 'blog'
  custom_background: string | null
  background_opacity: number
  background_blur: number
  created_at: string
  updated_at: string
}

const THEME_OPTIONS = [
  { value: 'light', label: '亮色模式' },
  { value: 'dark', label: '暗色模式' },
  { value: 'auto', label: '跟随系统' }
]

const COLOR_OPTIONS = [
  { value: 'teal', label: '青绿色', color: 'bg-teal-500' },
  { value: 'blue', label: '蓝色', color: 'bg-blue-500' },
  { value: 'purple', label: '紫色', color: 'bg-purple-500' },
  { value: 'pink', label: '粉色', color: 'bg-pink-500' },
  { value: 'green', label: '绿色', color: 'bg-green-500' },
  { value: 'orange', label: '橙色', color: 'bg-orange-500' }
]

const DEFAULT_VIEW_OPTIONS = [
  { value: 'dashboard', label: '概览', icon: HomeIcon },
  { value: 'growth', label: '成长引擎', icon: Cog6ToothIcon },
  { value: 'compass', label: '价值罗盘', icon: PaintBrushIcon },
  { value: 'blog', label: '私人博客', icon: BellIcon }
]

// 防抖函数
function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null
  return (...args: Parameters<T>) => {
    if (timeout) clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}

export default function SettingsPage() {
  const [settings, setSettings] = useState<UserSettings | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const { setBaseColor } = useTheme()

  // 防抖更新函数
  const debouncedUpdate = useCallback(
    debounce(async (key: keyof UserSettings, value: any) => {
      if (!settings) return

      try {
        setIsSaving(true)
        const updatedSettings = await patch<UserSettings>('/auth/settings/', {
          [key]: value
        })
        setSettings(updatedSettings)

        // 如果是背景相关设置，触发背景更新事件
        if (key.includes('background')) {
          window.dispatchEvent(new CustomEvent('backgroundSettingsChanged'))
        }
      } catch (error) {
        console.error('Failed to update setting:', error)
        toast.error('更新设置失败')
      } finally {
        setIsSaving(false)
      }
    }, 500),
    [settings]
  )

  useEffect(() => {
    fetchSettings()
  }, [])

  const fetchSettings = async () => {
    try {
      setIsLoading(true)
      const settingsData = await get<UserSettings>('/auth/settings/')
      setSettings(settingsData)
    } catch (error) {
      console.error('Failed to fetch settings:', error)
      toast.error('获取设置失败')
    } finally {
      setIsLoading(false)
    }
  }

  const updateSetting = async (key: keyof UserSettings, value: any) => {
    if (!settings) return

    try {
      setIsSaving(true)
      const updatedSettings = await patch<UserSettings>('/auth/settings/', {
        [key]: value
      })
      setSettings(updatedSettings)
      toast.success('设置已更新')

      // 如果是背景相关设置，触发背景更新事件
      if (key.includes('background')) {
        window.dispatchEvent(new CustomEvent('backgroundSettingsChanged'))
      }
    } catch (error) {
      console.error('Failed to update setting:', error)
      toast.error('更新设置失败')
    } finally {
      setIsSaving(false)
    }
  }

  const updateThemeColor = async (colorName: string) => {
    if (!settings) return

    try {
      setIsSaving(true)

      // 将颜色名称映射到hex值
      const colorMap: Record<string, string> = {
        'teal': '#14b8a6',
        'blue': '#3b82f6',
        'violet': '#8b5cf6',
        'pink': '#ec4899',
        'red': '#ef4444',
        'orange': '#f97316',
        'yellow': '#eab308',
        'green': '#22c55e',
        'indigo': '#6366f1',
        'rose': '#f43f5e'
      }

      const hexColor = colorMap[colorName] || colorName

      // 使用ThemeContext更新主题色（这会自动同步到后端）
      await setBaseColor(hexColor)

      // 更新本地设置状态
      setSettings(prev => prev ? { ...prev, primary_color: colorName } : null)

      toast.success('主题色已更新')
    } catch (error) {
      console.error('Failed to update theme color:', error)
      toast.error('更新主题色失败')
    } finally {
      setIsSaving(false)
    }
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-4">
            <div className="h-32 bg-gray-200 rounded"></div>
            <div className="h-32 bg-gray-200 rounded"></div>
            <div className="h-32 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div>
        <h1 className="text-2xl font-bold text-theme-primary">设置</h1>
        <p className="mt-1 text-theme-secondary">
          个性化您的灵境体验
        </p>
      </div>

      {/* 外观设置 */}
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-medium text-theme-primary flex items-center">
            <PaintBrushIcon className="w-5 h-5 mr-2" />
            外观设置
          </h3>
        </div>
        <div className="card-body space-y-6">
          {/* 主题模式 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">
              主题模式
            </label>
            <div className="flex items-center justify-between">
              <div className="flex gap-4">
                {THEME_OPTIONS.map((option) => (
                  <label key={option.value} className="flex items-center">
                    <input
                      type="radio"
                      name="theme"
                      value={option.value}
                      checked={settings?.theme === option.value}
                      onChange={(e) => updateSetting('theme', e.target.value)}
                      className="mr-2"
                      disabled={isSaving}
                    />
                    {option.label}
                  </label>
                ))}
              </div>
              <DarkModeSwitch />
            </div>
          </div>

          {/* 主题色 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">
              主题色
            </label>
            <div className="flex gap-3">
              {COLOR_OPTIONS.map((option) => (
                <button
                  key={option.value}
                  onClick={() => updateThemeColor(option.value)}
                  className={`w-8 h-8 rounded-full ${option.color} ${
                    settings?.primary_color === option.value
                      ? 'ring-2 ring-offset-2 ring-gray-400'
                      : ''
                  }`}
                  title={option.label}
                  disabled={isSaving}
                />
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* 背景设置 */}
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-medium text-theme-primary flex items-center">
            <PaintBrushIcon className="w-5 h-5 mr-2" />
            背景设置
          </h3>
        </div>
        <div className="card-body">
          <BackgroundUpload
            currentBackground={settings?.custom_background || undefined}
            backgroundOpacity={settings?.background_opacity || 0.3}
            backgroundBlur={settings?.background_blur || 0}
            onBackgroundChange={(backgroundUrl) => {
              updateSetting('custom_background', backgroundUrl)
            }}
            onOpacityChange={(opacity) => {
              // 立即更新本地状态以获得即时反馈
              setSettings(prev => prev ? { ...prev, background_opacity: opacity } : null)
              // 使用防抖更新后端
              debouncedUpdate('background_opacity', opacity)
            }}
            onBlurChange={(blur) => {
              // 立即更新本地状态以获得即时反馈
              setSettings(prev => prev ? { ...prev, background_blur: blur } : null)
              // 使用防抖更新后端
              debouncedUpdate('background_blur', blur)
            }}
          />
        </div>
      </div>

      {/* 通知设置 */}
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-medium text-theme-primary flex items-center">
            <BellIcon className="w-5 h-5 mr-2" />
            通知设置
          </h3>
        </div>
        <div className="card-body space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="text-sm font-medium text-gray-900">邮件通知</h4>
              <p className="text-sm text-gray-500">接收重要更新和提醒邮件</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={settings?.email_notifications || false}
                onChange={(e) => updateSetting('email_notifications', e.target.checked)}
                className="sr-only peer"
                disabled={isSaving}
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
            </label>
          </div>

          <div className="flex items-center justify-between">
            <div>
              <h4 className="text-sm font-medium text-gray-900">推送通知</h4>
              <p className="text-sm text-gray-500">接收浏览器推送通知</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={settings?.push_notifications || false}
                onChange={(e) => updateSetting('push_notifications', e.target.checked)}
                className="sr-only peer"
                disabled={isSaving}
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
            </label>
          </div>

          <div className="flex items-center justify-between">
            <div>
              <h4 className="text-sm font-medium text-gray-900">周报总结</h4>
              <p className="text-sm text-gray-500">每周接收成长总结报告</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={settings?.weekly_summary || false}
                onChange={(e) => updateSetting('weekly_summary', e.target.checked)}
                className="sr-only peer"
                disabled={isSaving}
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
            </label>
          </div>
        </div>
      </div>

      {/* 隐私设置 */}
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-medium text-theme-primary flex items-center">
            <ShieldCheckIcon className="w-5 h-5 mr-2" />
            隐私设置
          </h3>
        </div>
        <div className="card-body">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="text-sm font-medium text-gray-900">资料可见性</h4>
              <p className="text-sm text-gray-500">控制其他用户是否可以查看您的资料</p>
            </div>
            <select
              value={settings?.profile_visibility || 'private'}
              onChange={(e) => updateSetting('profile_visibility', e.target.value)}
              className="input w-32"
              disabled={isSaving}
            >
              <option value="private">私密</option>
              <option value="public">公开</option>
            </select>
          </div>
        </div>
      </div>

      {/* 功能设置 */}
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-medium text-theme-primary flex items-center">
            <Cog6ToothIcon className="w-5 h-5 mr-2" />
            功能设置
          </h3>
        </div>
        <div className="card-body space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="text-sm font-medium text-gray-900 flex items-center">
                <ClockIcon className="w-4 h-4 mr-1" />
                自动保存间隔
              </h4>
              <p className="text-sm text-gray-500">编辑内容时的自动保存频率（秒）</p>
            </div>
            <input
              type="number"
              min="10"
              max="300"
              value={settings?.auto_save_interval || 30}
              onChange={(e) => updateSetting('auto_save_interval', parseInt(e.target.value))}
              className="input w-20"
              disabled={isSaving}
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <h4 className="text-sm font-medium text-gray-900 flex items-center">
                <HomeIcon className="w-4 h-4 mr-1" />
                默认首页
              </h4>
              <p className="text-sm text-gray-500">登录后默认显示的页面</p>
            </div>
            <select
              value={settings?.default_view || 'dashboard'}
              onChange={(e) => updateSetting('default_view', e.target.value)}
              className="input w-32"
              disabled={isSaving}
            >
              {DEFAULT_VIEW_OPTIONS.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>
    </div>
  )
}
