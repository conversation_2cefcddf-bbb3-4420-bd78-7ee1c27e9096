'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import {
  HomeIcon,
  ChartBarIcon,
  BookOpenIcon,
  PencilSquareIcon,
  ChatBubbleLeftRightIcon,
  UserIcon,
  Cog6ToothIcon,
  ArrowRightOnRectangleIcon,
  Bars3Icon,
  XMarkIcon,
} from '@heroicons/react/24/outline'
import { tokenManager, post, get } from '@/lib/api'
import { User } from '@/types'
import toast from 'react-hot-toast'
import ColorPicker from '@/components/ColorPicker'
import DarkModeToggle from '@/components/ui/DarkModeToggle'
import CustomBackground from '@/components/ui/CustomBackground'

const navigation = [
  { name: '仪表盘', href: '/dashboard', icon: HomeIcon },
  { name: '成长引擎', href: '/dashboard/growth', icon: ChartBarIcon },
  { name: '价值罗盘', href: '/dashboard/compass', icon: BookOpenIcon },
  { name: '私人博客', href: '/dashboard/blog', icon: PencilSquareIcon },
  { name: 'AI伴侣', href: '/dashboard/ai-companion', icon: ChatBubbleLeftRightIcon },
  { name: '个人资料', href: '/dashboard/profile', icon: UserIcon },
  { name: '设置', href: '/dashboard/settings', icon: Cog6ToothIcon },
]

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const router = useRouter()
  const pathname = usePathname()
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  // 统一使用aurora布局风格
  const layoutClass = 'aurora-layout'
  const pageClass = 'aurora-page'

  useEffect(() => {
    // 检查认证状态
    if (!tokenManager.isAuthenticated()) {
      router.push('/auth/login')
      return
    }

    // 获取用户信息
    fetchUserProfile()
  }, [router])

  // 添加路径变化监听，确保导航状态正确
  useEffect(() => {
    // 路径变化时关闭移动端侧边栏
    setSidebarOpen(false)
  }, [pathname])

  const fetchUserProfile = async () => {
    try {
      const userData = await get<User>('/auth/profile/')
      setUser(userData)
    } catch (error) {
      console.error('Failed to fetch user profile:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleLogout = async () => {
    try {
      const refreshToken = tokenManager.getRefreshToken()
      if (refreshToken) {
        await post('/auth/logout/', { refresh_token: refreshToken })
      }
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      tokenManager.clearTokens()
      toast.success('已安全退出')
      router.push('/auth/login')
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="loading-spinner w-8 h-8"></div>
      </div>
    )
  }

  return (
    <CustomBackground>
      <div className={`min-h-screen ${pageClass} ${layoutClass}`}>
        {/* 移动端侧边栏 */}
        <div className={`fixed inset-0 z-50 lg:hidden ${sidebarOpen ? 'block' : 'hidden'}`}>
        <div className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm" onClick={() => setSidebarOpen(false)} />
        <div className="fixed inset-y-0 left-0 flex w-64 flex-col bg-glass shadow-xl">
          <div className="flex h-16 items-center justify-between px-4">
            <div className="flex items-center">
              <div className="h-8 w-8 flex items-center justify-center rounded-full bg-primary-100">
                <span className="text-lg font-bold text-primary-600">灵</span>
              </div>
              <span className="ml-2 text-xl font-bold text-gray-900">灵境</span>
            </div>
            <button
              onClick={() => setSidebarOpen(false)}
              className="text-gray-400 hover:text-gray-600"
            >
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>
          <nav className="flex-1 px-4 py-4">
            <ul className="space-y-2">
              {navigation.map((item) => {
                const isActive = pathname === item.href
                return (
                  <li key={item.name}>
                    <Link
                      href={item.href}
                      className={`nav-link ${isActive ? 'nav-link-active' : ''}`}
                      onClick={() => setSidebarOpen(false)}
                    >
                      <item.icon className="h-5 w-5 mr-3" />
                      {item.name}
                    </Link>
                  </li>
                )
              })}
            </ul>
          </nav>
          <div className="border-t border-gray-200 p-4">
            <button
              onClick={handleLogout}
              className="nav-link w-full text-left text-red-600 hover:bg-red-50"
            >
              <ArrowRightOnRectangleIcon className="h-5 w-5 mr-3" />
              退出登录
            </button>
          </div>
        </div>
      </div>

      {/* 桌面端侧边栏 - 使用较低的z-index确保不与顶部导航栏冲突 */}
      <div className="hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col z-40">
        <div className="flex flex-col flex-grow bg-glass border-r border-white/20">
          <div className="flex h-16 items-center px-4">
            <div className="h-8 w-8 flex items-center justify-center rounded-full bg-primary-100">
              <span className="text-lg font-bold text-primary-600">灵</span>
            </div>
            <span className="ml-2 text-xl font-bold text-gray-900">灵境</span>
          </div>
          <nav className="flex-1 px-4 py-4">
            <ul className="space-y-2">
              {navigation.map((item) => {
                const isActive = pathname === item.href
                return (
                  <li key={item.name}>
                    <Link
                      href={item.href}
                      className={`nav-link ${isActive ? 'nav-link-active' : ''}`}
                    >
                      <item.icon className="h-5 w-5 mr-3" />
                      {item.name}
                    </Link>
                  </li>
                )
              })}
            </ul>
          </nav>
          <div className="border-t border-gray-200 p-4">
            <div className="flex items-center mb-4">
              <div className="h-8 w-8 bg-primary-100 rounded-full flex items-center justify-center">
                <span className="text-sm font-medium text-primary-600">
                  {user?.username?.[0] || user?.email?.[0] || 'U'}
                </span>
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-900">
                  {user?.username || '用户'}
                </p>
                <p className="text-xs text-gray-500">{user?.email}</p>
              </div>
            </div>
            <button
              onClick={handleLogout}
              className="nav-link w-full text-left text-red-600 hover:bg-red-50"
            >
              <ArrowRightOnRectangleIcon className="h-5 w-5 mr-3" />
              退出登录
            </button>
          </div>
        </div>
      </div>

      {/* 主内容区域 */}
      <div className="lg:pl-64 main-aurora-bg">
        {/* 顶部导航栏 */}
        <div className="sticky top-0 z-50 flex h-16 shrink-0 items-center gap-x-4 border-b border-white/20 navbar-glass px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8 lg:border-l-0">
          <button
            type="button"
            className="-m-2.5 p-2.5 text-gray-700 lg:hidden"
            onClick={() => setSidebarOpen(true)}
          >
            <Bars3Icon className="h-6 w-6" />
          </button>

          <div className="flex flex-1 gap-x-4 self-stretch lg:gap-x-6">
            <div className="flex flex-1"></div>
            <div className="flex items-center gap-x-4 lg:gap-x-6">
              {/* 暗色模式切换 */}
              <DarkModeToggle size="sm" />

              <div className="hidden lg:block lg:h-6 lg:w-px lg:bg-gray-200 dark:bg-gray-600" />

              {/* 颜色选择器 */}
              <ColorPicker />

              <div className="hidden lg:block lg:h-6 lg:w-px lg:bg-gray-200 dark:bg-gray-600" />
              <div className="flex items-center">
                <span className="text-sm text-gray-700 dark:text-gray-300">
                  欢迎回来，{user?.username || '用户'}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* 页面内容 */}
        <main className="py-8 main-content-enhanced">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <div className="page-container p-6">
              {children}
            </div>
          </div>
        </main>
      </div>
    </div>
    </CustomBackground>
  )
}
