/**
 * AI伴侣页面
 *
 * 功能说明：
 * - 提供与AI伴侣的对话界面
 * - 基于用户的成长数据和记忆进行个性化对话
 * - 集成记忆检索和情感理解功能
 * - 展示AI伴侣的特性和使用建议
 *
 * 页面布局：
 * - 左侧：AI伴侣介绍、功能特性、使用建议
 * - 右侧：对话界面（AICompanionChat组件）
 *
 * API交互：
 * - /ai/search-memories/ - 搜索相关记忆
 * - /ai/chat/ - AI对话接口
 *
 * TODO: 添加对话历史记录功能
 * TODO: 添加多种AI人格模式选择
 */
'use client'

import React from 'react'
import {
  CpuChipIcon as Brain,
  SparklesIcon as Sparkles,
  ChatBubbleLeftRightIcon as MessageCircle,
  CircleStackIcon as Database,
  BoltIcon as Zap
} from '@heroicons/react/24/outline'
import AICompanionChat from '@/components/ai-companion/AICompanionChat'

export default function AICompanionPage() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">智能助手</h1>
          <p className="text-gray-600">基于你的成长数据，提供个性化分析和专业建议</p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 min-h-[calc(100vh-12rem)]">
          
          {/* 左侧信息面板 */}
          <div className="lg:col-span-1 space-y-6">
            {/* AI伴侣介绍 */}
            <div className="card p-6 bg-gradient-to-br from-primary-500 to-secondary-500 text-white">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center">
                  <Brain className="w-6 h-6" />
                </div>
                <div>
                  <h2 className="text-xl font-bold">智能助手</h2>
                  <p className="text-white/80 text-sm">个性化成长顾问</p>
                </div>
                <Sparkles className="w-5 h-5 ml-auto animate-pulse" />
              </div>
              
              <p className="text-white/90 text-sm leading-relaxed mb-4">
                基于你的成长经历、项目经验和学习轨迹构建的智能助手，能够分析你的发展模式，提供专业的成长建议和决策支持。
              </p>
              
              <div className="flex items-center gap-2 text-white/80 text-xs">
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                在线并准备对话
              </div>
            </div>

            {/* 功能特性 */}
            <div className="card p-6">
              <h3 className="font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
                <Zap className="w-5 h-5 text-primary-500" />
                智能特性
              </h3>
              
              <div className="space-y-4">
                <div className="flex items-start gap-3">
                  <div className="w-8 h-8 bg-primary-100 dark:bg-primary-900/30 rounded-lg flex items-center justify-center flex-shrink-0">
                    <Database className="w-4 h-4 text-primary-600 dark:text-primary-400" />
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white text-sm">智能检索</h4>
                    <p className="text-gray-600 dark:text-gray-400 text-xs">
                      基于向量数据库检索相关的成长经历和项目经验
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start gap-3">
                  <div className="w-8 h-8 bg-secondary-100 dark:bg-secondary-900/30 rounded-lg flex items-center justify-center flex-shrink-0">
                    <Brain className="w-4 h-4 text-secondary-600 dark:text-secondary-400" />
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white text-sm">模式分析</h4>
                    <p className="text-gray-600 dark:text-gray-400 text-xs">
                      识别你的成长模式，提供专业的发展建议
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start gap-3">
                  <div className="w-8 h-8 bg-accent-100 dark:bg-accent-900/30 rounded-lg flex items-center justify-center flex-shrink-0">
                    <MessageCircle className="w-4 h-4 text-accent-600 dark:text-accent-400" />
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white text-sm">个性化建议</h4>
                    <p className="text-gray-600 dark:text-gray-400 text-xs">
                      基于你的经历和发展轨迹提供定制化建议
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* 使用提示 */}
            <div className="card p-6 bg-gradient-to-br from-amber-50 to-orange-50 dark:from-amber-900/20 dark:to-orange-900/20 border-amber-200 dark:border-amber-800">
              <h3 className="font-semibold text-amber-900 dark:text-amber-100 mb-3 flex items-center gap-2">
                <Sparkles className="w-5 h-5" />
                使用建议
              </h3>
              
              <div className="space-y-2 text-amber-800 dark:text-amber-200 text-sm">
                <p>• 询问关于过往项目的分析和建议</p>
                <p>• 讨论技能发展和学习规划</p>
                <p>• 寻求职业发展方向的指导</p>
                <p>• 获取基于经验的决策支持</p>
              </div>
            </div>
          </div>

        {/* 右侧对话区域 */}
        <div className="lg:col-span-2">
          <div className="card h-full flex flex-col overflow-hidden">
            <AICompanionChat className="h-full" />
          </div>
        </div>
      </div>
    </div>
  )
}
