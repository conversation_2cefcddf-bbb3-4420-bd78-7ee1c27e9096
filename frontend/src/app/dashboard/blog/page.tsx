'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { PlusIcon, MagnifyingGlassIcon, FunnelIcon } from '@heroicons/react/24/outline'
import { BlogPost, BlogCategory, BlogTag, BlogStats } from '@/types'
import { get, del } from '@/lib/api'
import toast from 'react-hot-toast'
import BlogNavigation from '@/components/blog/BlogNavigation'
import { BlogPageSkeleton } from '@/components/ui'

export default function BlogPage() {
  const router = useRouter()
  const [posts, setPosts] = useState<BlogPost[]>([])
  const [categories, setCategories] = useState<BlogCategory[]>([])
  const [tags, setTags] = useState<BlogTag[]>([])
  const [stats, setStats] = useState<BlogStats | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [categoryFilter, setCategoryFilter] = useState<string>('all')

  useEffect(() => {
    fetchBlogData()
  }, [])

  const fetchBlogData = async () => {
    try {
      setIsLoading(true)
      const [postsData, categoriesData, tagsData, statsData] = await Promise.all([
        get('/blog/posts/'),
        get('/blog/categories/'),
        get('/blog/tags/'),
        get('/blog/stats/')
      ])

      // 确保数据是数组格式
      setPosts(Array.isArray(postsData) ? postsData : (postsData?.results || []))
      setCategories(Array.isArray(categoriesData) ? categoriesData : (categoriesData?.results || []))
      setTags(Array.isArray(tagsData) ? tagsData : (tagsData?.results || []))
      setStats(statsData || {
        total_posts: 0,
        published_posts: 0,
        draft_posts: 0,
        total_views: 0,
        total_likes: 0,
        total_comments: 0,
        categories_count: 0,
        tags_count: 0
      })
    } catch (error) {
      console.error('获取博客数据失败:', error)
      toast.error('获取博客数据失败')
      // 设置默认空数据
      setPosts([])
      setCategories([])
      setTags([])
      setStats({
        total_posts: 0,
        published_posts: 0,
        draft_posts: 0,
        total_views: 0,
        total_likes: 0,
        total_comments: 0,
        categories_count: 0,
        tags_count: 0
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleDeletePost = async (postId: string) => {
    if (!confirm('确定要删除这篇文章吗？此操作不可恢复。')) {
      return
    }

    try {
      await del(`/blog/posts/${postId}/delete/`)
      toast.success('文章删除成功')
      // 重新获取文章列表
      fetchBlogData()
    } catch (error) {
      console.error('删除文章失败:', error)
      toast.error('删除文章失败')
    }
  }

  const filteredPosts = posts.filter(post => {
    const matchesSearch = post.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         post.excerpt.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesStatus = statusFilter === 'all' || post.status === statusFilter
    const matchesCategory = categoryFilter === 'all' || 
                           (post.category && post.category.category_id.toString() === categoryFilter)
    
    return matchesSearch && matchesStatus && matchesCategory
  })

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      draft: { label: '草稿', className: 'badge-secondary' },
      published: { label: '已发布', className: 'badge-success' },
      archived: { label: '已归档', className: 'badge-warning' }
    }
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.draft
    return <span className={`badge ${config.className}`}>{config.label}</span>
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  if (isLoading) {
    return <BlogPageSkeleton />
  }

  return (
    <div className="space-y-6">
      {/* 博客导航 */}
      <BlogNavigation />

      {/* 页面标题和操作 */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-theme-primary">私人博客</h1>
          <p className="mt-1 text-theme-secondary">记录思考，分享见解</p>
        </div>
        <div className="mt-4 sm:mt-0">
          <button
            onClick={() => window.location.href = '/dashboard/blog/create'}
            className="btn btn-primary"
          >
            <PlusIcon className="h-5 w-5 mr-2" />
            写文章
          </button>
        </div>
      </div>

      {/* 统计概览 */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="clean-card p-4">
            <div className="text-2xl font-bold text-gray-900">{stats.total_posts}</div>
            <div className="text-sm text-gray-500">总文章数</div>
          </div>
          <div className="clean-card p-4">
            <div className="text-2xl font-bold text-green-600">{stats.published_posts}</div>
            <div className="text-sm text-gray-500">已发布</div>
          </div>
          <div className="clean-card p-4">
            <div className="text-2xl font-bold text-yellow-600">{stats.draft_posts}</div>
            <div className="text-sm text-gray-500">草稿</div>
          </div>
          <div className="clean-card p-4">
            <div className="text-2xl font-bold text-blue-600">{stats.total_views}</div>
            <div className="text-sm text-gray-500">总浏览量</div>
          </div>
        </div>
      )}

      {/* 搜索和过滤 */}
      <div className="clean-card p-4">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <input
                type="text"
                placeholder="搜索文章..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="input pl-10 w-full"
              />
            </div>
          </div>
          <div className="flex gap-2">
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="input"
            >
              <option value="all">所有状态</option>
              <option value="published">已发布</option>
              <option value="draft">草稿</option>
              <option value="archived">已归档</option>
            </select>
            <select
              value={categoryFilter}
              onChange={(e) => setCategoryFilter(e.target.value)}
              className="input"
            >
              <option value="all">所有分类</option>
              {categories.map(category => (
                <option key={category.category_id} value={category.category_id.toString()}>
                  {category.name}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* 文章列表 */}
      <div className="space-y-4">
        {filteredPosts.length === 0 ? (
          <div className="clean-card p-8 text-center">
            <div className="text-gray-500">
              {searchQuery || statusFilter !== 'all' || categoryFilter !== 'all' 
                ? '没有找到匹配的文章' 
                : '还没有文章，开始写第一篇吧！'
              }
            </div>
          </div>
        ) : (
          filteredPosts.map(post => (
            <div key={post.post_id} className="clean-card p-6 hover:shadow-md transition-shadow">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-2">
                    <h3
                      className="text-lg font-semibold text-gray-900 hover:text-primary-600 cursor-pointer"
                      onClick={() => router.push(`/dashboard/blog/${post.post_id}`)}
                    >
                      {post.title}
                    </h3>
                    {getStatusBadge(post.status)}
                  </div>
                  
                  <p className="text-gray-600 mb-3 line-clamp-2">{post.excerpt}</p>
                  
                  <div className="flex items-center gap-4 text-sm text-gray-500">
                    <span>{formatDate(post.created_at)}</span>
                    {post.category && (
                      <span className="px-2 py-1 bg-gray-100 rounded text-xs">
                        {post.category.name}
                      </span>
                    )}
                    <span>{post.reading_time} 分钟阅读</span>
                    <span>{post.view_count} 次浏览</span>
                  </div>
                  
                  {post.tags.length > 0 && (
                    <div className="flex flex-wrap gap-1 mt-2">
                      {post.tags.map(tag => (
                        <span
                          key={tag.tag_id}
                          className="px-2 py-1 text-xs rounded"
                          style={{ backgroundColor: tag.color + '20', color: tag.color }}
                        >
                          #{tag.name}
                        </span>
                      ))}
                    </div>
                  )}
                </div>
                
                <div className="flex items-center gap-2 ml-4">
                  <button
                    onClick={() => window.location.href = `/dashboard/blog/${post.post_id}/edit`}
                    className="btn btn-ghost btn-sm"
                  >
                    编辑
                  </button>
                  <button
                    onClick={() => handleDeletePost(post.post_id)}
                    className="btn btn-ghost btn-sm text-red-600"
                  >
                    删除
                  </button>
                </div>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  )
}
