'use client'

import { useState, useEffect, useRef } from 'react'
import { useRouter, useParams } from 'next/navigation'
import { 
  ArrowLeftIcon, 
  PencilIcon, 
  TrashIcon, 
  EyeIcon,
  HeartIcon,
  ChatBubbleLeftIcon 
} from '@heroicons/react/24/outline'
import { BlogPost } from '@/types'
import { get, post as apiPost, del } from '@/lib/api'
import toast from 'react-hot-toast'

export default function BlogPostPage() {
  const router = useRouter()
  const params = useParams()
  const [post, setPost] = useState<BlogPost | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isDeleting, setIsDeleting] = useState(false)
  const hasViewedRef = useRef(false)

  useEffect(() => {
    if (params.id && !hasViewedRef.current) {
      hasViewedRef.current = true
      fetchPost()
    }
  }, [params.id])

  const fetchPost = async () => {
    try {
      setIsLoading(true)
      const postData = await get(`/blog/posts/${params.id}/`)
      setPost(postData)
    } catch (error) {
      console.error('获取文章失败:', error)
      toast.error('获取文章失败')
      router.push('/dashboard/blog')
    } finally {
      setIsLoading(false)
    }
  }

  const handleLike = async () => {
    if (!post) return

    try {
      await apiPost(`/blog/posts/${post.post_id}/like/`)
      setPost(prev => prev ? { ...prev, like_count: prev.like_count + 1 } : null)
      toast.success('点赞成功')
    } catch (error) {
      console.error('点赞失败:', error)
      toast.error('点赞失败')
    }
  }

  const handleDelete = async () => {
    if (!post) return
    
    if (!confirm('确定要删除这篇文章吗？此操作不可恢复。')) {
      return
    }

    try {
      setIsDeleting(true)
      await del(`/blog/posts/${post.post_id}/delete/`)
      toast.success('文章删除成功')
      router.push('/dashboard/blog')
    } catch (error) {
      console.error('删除文章失败:', error)
      toast.error('删除文章失败')
    } finally {
      setIsDeleting(false)
    }
  }

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      draft: { label: '草稿', className: 'badge-secondary' },
      published: { label: '已发布', className: 'badge-success' },
      archived: { label: '已归档', className: 'badge-warning' }
    }
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.draft
    return <span className={`badge ${config.className}`}>{config.label}</span>
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="loading-spinner w-8 h-8"></div>
      </div>
    )
  }

  if (!post) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500">文章不存在</p>
        <button
          onClick={() => router.push('/dashboard/blog')}
          className="btn btn-primary mt-4"
        >
          返回博客
        </button>
      </div>
    )
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* 页面标题和操作 */}
      <div className="flex items-center justify-between">
        <button
          onClick={() => router.push('/dashboard/blog')}
          className="btn btn-ghost"
        >
          <ArrowLeftIcon className="h-5 w-5 mr-2" />
          返回博客列表
        </button>
        
        <div className="flex items-center gap-2">
          <button
            onClick={() => router.push(`/dashboard/blog/${post.post_id}/edit`)}
            className="btn btn-outline"
          >
            <PencilIcon className="h-5 w-5 mr-2" />
            编辑
          </button>
          <button
            onClick={handleDelete}
            disabled={isDeleting}
            className="btn btn-outline text-red-600 hover:bg-red-50"
          >
            <TrashIcon className="h-5 w-5 mr-2" />
            删除
          </button>
        </div>
      </div>

      {/* 文章内容 */}
      <article className="clean-card p-8">
        {/* 文章头部 */}
        <header className="mb-8">
          <div className="flex items-center gap-3 mb-4">
            <h1 className="text-3xl font-bold text-gray-900">{post.title}</h1>
            {getStatusBadge(post.status)}
          </div>
          
          <div className="flex items-center gap-6 text-sm text-gray-500 mb-4">
            <span>创建于 {formatDate(post.created_at)}</span>
            {post.published_at && (
              <span>发布于 {formatDate(post.published_at)}</span>
            )}
            <span>{post.reading_time} 分钟阅读</span>
          </div>

          {post.category && (
            <div className="mb-4">
              <span 
                className="px-3 py-1 text-sm rounded-full"
                style={{ 
                  backgroundColor: post.category.color + '20', 
                  color: post.category.color 
                }}
              >
                {post.category.name}
              </span>
            </div>
          )}

          {post.tags.length > 0 && (
            <div className="flex flex-wrap gap-2 mb-6">
              {post.tags.map(tag => (
                <span
                  key={tag.tag_id}
                  className="px-2 py-1 text-xs rounded"
                  style={{ 
                    backgroundColor: tag.color + '20', 
                    color: tag.color 
                  }}
                >
                  #{tag.name}
                </span>
              ))}
            </div>
          )}

          {post.featured_image && (
            <div className="mb-6">
              <img
                src={post.featured_image}
                alt={post.title}
                className="w-full h-64 object-cover rounded-lg"
              />
            </div>
          )}
        </header>

        {/* 文章正文 */}
        <div className="prose max-w-none">
          <div className="whitespace-pre-wrap text-gray-800 leading-relaxed">
            {post.content}
          </div>
        </div>

        {/* 文章底部 */}
        <footer className="mt-8 pt-6 border-t border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-6 text-sm text-gray-500">
              <div className="flex items-center gap-1">
                <EyeIcon className="h-4 w-4" />
                <span>{post.view_count} 次浏览</span>
              </div>
              <div className="flex items-center gap-1">
                <ChatBubbleLeftIcon className="h-4 w-4" />
                <span>{post.comment_count} 条评论</span>
              </div>
            </div>
            
            <button
              onClick={handleLike}
              className="flex items-center gap-1 text-sm text-gray-500 hover:text-red-500 transition-colors"
            >
              <HeartIcon className="h-4 w-4" />
              <span>{post.like_count}</span>
            </button>
          </div>
        </footer>
      </article>

      {/* SEO信息（仅在开发模式下显示） */}
      {process.env.NODE_ENV === 'development' && (post.meta_description || post.meta_keywords) && (
        <div className="clean-card p-4">
          <h3 className="font-medium text-gray-900 mb-2">SEO信息</h3>
          <div className="space-y-2 text-sm text-gray-600">
            {post.meta_description && (
              <div>
                <strong>描述:</strong> {post.meta_description}
              </div>
            )}
            {post.meta_keywords && (
              <div>
                <strong>关键词:</strong> {post.meta_keywords}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  )
}
