'use client'

import { useState, useEffect } from 'react'
import { createPortal } from 'react-dom'
import { PlusIcon, PencilIcon, TrashIcon } from '@heroicons/react/24/outline'
import { BlogTag } from '@/types'
import { get, post, put, del } from '@/lib/api'
import toast from 'react-hot-toast'
import BlogNavigation from '@/components/blog/BlogNavigation'

export default function TagsPage() {
  const [tags, setTags] = useState<BlogTag[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [editingTag, setEditingTag] = useState<BlogTag | null>(null)
  const [form, setForm] = useState({
    name: '',
    slug: '',
    color: '#10b981'
  })

  useEffect(() => {
    fetchTags()
  }, [])

  const fetchTags = async () => {
    try {
      setIsLoading(true)
      const response = await get('/blog/tags/')
      setTags(Array.isArray(response) ? response : (response.results || []))
    } catch (error) {
      console.error('获取标签失败:', error)
      toast.error('获取标签失败')
      setTags([])
    } finally {
      setIsLoading(false)
    }
  }

  const resetForm = () => {
    setForm({
      name: '',
      slug: '',
      color: '#10b981'
    })
    setEditingTag(null)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!form.name.trim()) {
      toast.error('请输入标签名称')
      return
    }

    try {
      if (editingTag) {
        await put(`/blog/tags/${editingTag.tag_id}/`, form)
        toast.success('标签更新成功')
      } else {
        await post('/blog/tags/', form)
        toast.success('标签创建成功')
      }
      
      setShowCreateModal(false)
      resetForm()
      fetchTags()
    } catch (error) {
      console.error('保存标签失败:', error)
      toast.error('保存标签失败')
    }
  }

  const handleEdit = (tag: BlogTag) => {
    setForm({
      name: tag.name,
      slug: tag.slug,
      color: tag.color
    })
    setEditingTag(tag)
    setShowCreateModal(true)
  }

  const handleDelete = async (tagId: number) => {
    if (!confirm('确定要删除这个标签吗？')) {
      return
    }

    try {
      await del(`/blog/tags/${tagId}/`)
      toast.success('标签删除成功')
      fetchTags()
    } catch (error) {
      console.error('删除标签失败:', error)
      toast.error('删除标签失败')
    }
  }

  const generateSlug = (name: string) => {
    return name.toLowerCase().replace(/[^\w\s-]/g, '').replace(/\s+/g, '-')
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="loading-spinner w-8 h-8"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* 博客导航 */}
      <BlogNavigation />

      {/* 页面标题和操作 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">标签管理</h1>
          <p className="text-gray-600">管理博客文章标签</p>
        </div>
        <button
          onClick={() => {
            resetForm()
            setShowCreateModal(true)
          }}
          className="btn btn-primary"
        >
          <PlusIcon className="h-5 w-5 mr-2" />
          添加标签
        </button>
      </div>

      {/* 标签列表 */}
      <div className="clean-card">
        {tags.length === 0 ? (
          <div className="p-8 text-center text-gray-500">
            还没有标签，创建第一个标签吧！
          </div>
        ) : (
          <div className="p-4">
            <div className="flex flex-wrap gap-3">
              {tags.map(tag => (
                <div
                  key={tag.tag_id}
                  className="flex items-center gap-2 px-3 py-2 rounded-lg border border-gray-200 hover:shadow-sm transition-shadow"
                >
                  <span
                    className="px-2 py-1 text-sm rounded"
                    style={{ backgroundColor: tag.color + '20', color: tag.color }}
                  >
                    #{tag.name}
                  </span>
                  <span className="text-xs text-gray-500">
                    {tag.post_count}
                  </span>
                  <div className="flex items-center gap-1 ml-2">
                    <button
                      onClick={() => handleEdit(tag)}
                      className="p-1 text-gray-400 hover:text-gray-600"
                    >
                      <PencilIcon className="h-3 w-3" />
                    </button>
                    <button
                      onClick={() => handleDelete(tag.tag_id)}
                      className="p-1 text-gray-400 hover:text-red-600"
                    >
                      <TrashIcon className="h-3 w-3" />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* 创建/编辑模态框 */}
      {showCreateModal && createPortal(
        <div
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4"
          style={{ zIndex: 9999 }}
          onClick={() => {
            setShowCreateModal(false)
            resetForm()
          }}
        >
          <div className="bg-white rounded-lg max-w-md w-full shadow-xl" onClick={(e) => e.stopPropagation()}>
            <div className="flex items-center justify-between p-6 border-b">
              <h2 className="text-lg font-semibold text-gray-900">
                {editingTag ? '编辑标签' : '添加标签'}
              </h2>
              <button
                onClick={() => {
                  setShowCreateModal(false)
                  resetForm()
                }}
                className="text-gray-400 hover:text-gray-600"
              >
                ×
              </button>
            </div>

            <form onSubmit={handleSubmit} className="p-6 space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  标签名称 *
                </label>
                <input
                  type="text"
                  value={form.name}
                  onChange={(e) => {
                    const name = e.target.value
                    setForm(prev => ({
                      ...prev,
                      name,
                      slug: prev.slug || generateSlug(name)
                    }))
                  }}
                  className="input w-full"
                  placeholder="输入标签名称..."
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  标签别名
                </label>
                <input
                  type="text"
                  value={form.slug}
                  onChange={(e) => setForm(prev => ({ ...prev, slug: e.target.value }))}
                  className="input w-full"
                  placeholder="tag-slug"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  标签颜色
                </label>
                <div className="flex items-center gap-2">
                  <input
                    type="color"
                    value={form.color}
                    onChange={(e) => setForm(prev => ({ ...prev, color: e.target.value }))}
                    className="w-10 h-10 rounded border border-gray-300"
                  />
                  <input
                    type="text"
                    value={form.color}
                    onChange={(e) => setForm(prev => ({ ...prev, color: e.target.value }))}
                    className="input flex-1"
                    placeholder="#10b981"
                  />
                </div>
              </div>

              <div className="flex justify-end gap-2 pt-4">
                <button
                  type="button"
                  onClick={() => {
                    setShowCreateModal(false)
                    resetForm()
                  }}
                  className="btn btn-ghost"
                >
                  取消
                </button>
                <button type="submit" className="btn btn-primary">
                  {editingTag ? '更新' : '创建'}
                </button>
              </div>
            </form>
          </div>
        </div>,
        document.getElementById('modal-root')!
      )}
    </div>
  )
}
