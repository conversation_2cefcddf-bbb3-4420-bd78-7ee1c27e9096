'use client'

import { useState, useEffect } from 'react'
import { createPortal } from 'react-dom'
import { PlusIcon, PencilIcon, TrashIcon } from '@heroicons/react/24/outline'
import { BlogCategory } from '@/types'
import { get, post, put, del } from '@/lib/api'
import toast from 'react-hot-toast'
import BlogNavigation from '@/components/blog/BlogNavigation'

export default function CategoriesPage() {
  const [categories, setCategories] = useState<BlogCategory[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [editingCategory, setEditingCategory] = useState<BlogCategory | null>(null)
  const [form, setForm] = useState({
    name: '',
    slug: '',
    description: '',
    color: '#6366f1'
  })

  useEffect(() => {
    fetchCategories()
  }, [])

  const fetchCategories = async () => {
    try {
      setIsLoading(true)
      const categoriesData = await get('/blog/categories/')
      setCategories(Array.isArray(categoriesData) ? categoriesData : (categoriesData?.results || []))
    } catch (error) {
      console.error('获取分类失败:', error)
      toast.error('获取分类失败')
      setCategories([])
    } finally {
      setIsLoading(false)
    }
  }

  const resetForm = () => {
    setForm({
      name: '',
      slug: '',
      description: '',
      color: '#6366f1'
    })
    setEditingCategory(null)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!form.name.trim()) {
      toast.error('请输入分类名称')
      return
    }

    try {
      if (editingCategory) {
        await put(`/blog/categories/${editingCategory.category_id}/`, form)
        toast.success('分类更新成功')
      } else {
        await post('/blog/categories/', form)
        toast.success('分类创建成功')
      }
      
      setShowCreateModal(false)
      resetForm()
      fetchCategories()
    } catch (error) {
      console.error('保存分类失败:', error)
      toast.error('保存分类失败')
    }
  }

  const handleEdit = (category: BlogCategory) => {
    setForm({
      name: category.name,
      slug: category.slug,
      description: category.description,
      color: category.color
    })
    setEditingCategory(category)
    setShowCreateModal(true)
  }

  const handleDelete = async (categoryId: number) => {
    if (!confirm('确定要删除这个分类吗？')) {
      return
    }

    try {
      await del(`/blog/categories/${categoryId}/`)
      toast.success('分类删除成功')
      fetchCategories()
    } catch (error) {
      console.error('删除分类失败:', error)
      toast.error('删除分类失败')
    }
  }

  const generateSlug = (name: string) => {
    return name.toLowerCase().replace(/[^\w\s-]/g, '').replace(/\s+/g, '-')
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="loading-spinner w-8 h-8"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* 博客导航 */}
      <BlogNavigation />

      {/* 页面标题和操作 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">分类管理</h1>
          <p className="text-gray-600">管理博客文章分类</p>
        </div>
        <button
          onClick={() => {
            resetForm()
            setShowCreateModal(true)
          }}
          className="btn btn-primary"
        >
          <PlusIcon className="h-5 w-5 mr-2" />
          添加分类
        </button>
      </div>

      {/* 分类列表 */}
      <div className="clean-card">
        {categories.length === 0 ? (
          <div className="p-8 text-center text-gray-500">
            还没有分类，创建第一个分类吧！
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {categories.map(category => (
              <div key={category.category_id} className="p-4 flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <div
                    className="w-4 h-4 rounded-full"
                    style={{ backgroundColor: category.color }}
                  />
                  <div>
                    <h3 className="font-medium text-gray-900">{category.name}</h3>
                    <p className="text-sm text-gray-500">{category.description}</p>
                    <p className="text-xs text-gray-400">别名: {category.slug}</p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-sm text-gray-500">
                    {category.post_count} 篇文章
                  </span>
                  <button
                    onClick={() => handleEdit(category)}
                    className="btn btn-ghost btn-sm"
                  >
                    <PencilIcon className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => handleDelete(category.category_id)}
                    className="btn btn-ghost btn-sm text-red-600"
                  >
                    <TrashIcon className="h-4 w-4" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* 创建/编辑模态框 */}
      {showCreateModal && createPortal(
        <div
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4"
          style={{ zIndex: 9999 }}
          onClick={() => {
            setShowCreateModal(false)
            resetForm()
          }}
        >
          <div className="bg-white rounded-lg max-w-md w-full shadow-xl" onClick={(e) => e.stopPropagation()}>
            <div className="flex items-center justify-between p-6 border-b">
              <h2 className="text-lg font-semibold text-gray-900">
                {editingCategory ? '编辑分类' : '添加分类'}
              </h2>
              <button
                onClick={() => {
                  setShowCreateModal(false)
                  resetForm()
                }}
                className="text-gray-400 hover:text-gray-600"
              >
                ×
              </button>
            </div>

            <form onSubmit={handleSubmit} className="p-6 space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  分类名称 *
                </label>
                <input
                  type="text"
                  value={form.name}
                  onChange={(e) => {
                    const name = e.target.value
                    setForm(prev => ({
                      ...prev,
                      name,
                      slug: prev.slug || generateSlug(name)
                    }))
                  }}
                  className="input w-full"
                  placeholder="输入分类名称..."
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  分类别名
                </label>
                <input
                  type="text"
                  value={form.slug}
                  onChange={(e) => setForm(prev => ({ ...prev, slug: e.target.value }))}
                  className="input w-full"
                  placeholder="category-slug"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  分类描述
                </label>
                <textarea
                  value={form.description}
                  onChange={(e) => setForm(prev => ({ ...prev, description: e.target.value }))}
                  className="input w-full h-20 resize-none"
                  placeholder="输入分类描述..."
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  分类颜色
                </label>
                <div className="flex items-center gap-2">
                  <input
                    type="color"
                    value={form.color}
                    onChange={(e) => setForm(prev => ({ ...prev, color: e.target.value }))}
                    className="w-10 h-10 rounded border border-gray-300"
                  />
                  <input
                    type="text"
                    value={form.color}
                    onChange={(e) => setForm(prev => ({ ...prev, color: e.target.value }))}
                    className="input flex-1"
                    placeholder="#6366f1"
                  />
                </div>
              </div>

              <div className="flex justify-end gap-2 pt-4">
                <button
                  type="button"
                  onClick={() => {
                    setShowCreateModal(false)
                    resetForm()
                  }}
                  className="btn btn-ghost"
                >
                  取消
                </button>
                <button type="submit" className="btn btn-primary">
                  {editingCategory ? '更新' : '创建'}
                </button>
              </div>
            </form>
          </div>
        </div>,
        document.getElementById('modal-root')!
      )}
    </div>
  )
}
