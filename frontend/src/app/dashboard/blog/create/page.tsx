/**
 * 博客文章创建页面
 *
 * 功能说明：
 * - 提供富文本编辑器用于创建博客文章
 * - 支持文章标题、内容、摘要、分类、标签等字段
 * - 集成AI生成标题和摘要功能
 * - 支持草稿保存和直接发布
 * - 提供实时预览功能
 *
 * API交互：
 * - GET /blog/categories/ - 获取分类列表
 * - GET /blog/tags/ - 获取标签列表
 * - POST /blog/posts/create/ - 创建文章
 *
 * TODO: 添加图片上传功能
 * TODO: 添加Markdown编辑器支持
 * TODO: 添加自动保存功能
 */
'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { ArrowLeftIcon, EyeIcon, SparklesIcon } from '@heroicons/react/24/outline'
import { BlogCategory, BlogTag, BlogPostCreate } from '@/types'
import { get, post } from '@/lib/api'
import toast from 'react-hot-toast'
import TitleSummaryModal from '@/components/ai/TitleSummaryModal'

export default function CreateBlogPage() {
  const router = useRouter()
  const [categories, setCategories] = useState<BlogCategory[]>([])
  const [tags, setTags] = useState<BlogTag[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [showAIModal, setShowAIModal] = useState(false)
  const [isPreview, setIsPreview] = useState(false)
  
  const [form, setForm] = useState<BlogPostCreate>({
    title: '',
    slug: '',
    content: '',
    excerpt: '',
    status: 'draft',
    category: null,
    tag_ids: [],
    featured_image: '',
    meta_description: '',
    meta_keywords: ''
  })

  useEffect(() => {
    fetchMetadata()
  }, [])

  const fetchMetadata = async () => {
    try {
      const [categoriesData, tagsData] = await Promise.all([
        get('/blog/categories/'),
        get('/blog/tags/')
      ])

      setCategories(Array.isArray(categoriesData) ? categoriesData : (categoriesData?.results || []))
      setTags(Array.isArray(tagsData) ? tagsData : (tagsData?.results || []))
    } catch (error) {
      console.error('获取元数据失败:', error)
      toast.error('获取分类和标签失败')
      setCategories([])
      setTags([])
    }
  }

  const generateSlug = (title: string) => {
    return title
      .toLowerCase()
      .replace(/[^\w\s-]/g, '')
      .replace(/\s+/g, '-')
      .trim()
  }

  const handleTitleChange = (title: string) => {
    setForm(prev => ({
      ...prev,
      title,
      slug: prev.slug || generateSlug(title)
    }))
  }

  const handleTagToggle = (tagId: number) => {
    setForm(prev => ({
      ...prev,
      tag_ids: prev.tag_ids?.includes(tagId)
        ? prev.tag_ids.filter(id => id !== tagId)
        : [...(prev.tag_ids || []), tagId]
    }))
  }

  // AI生成标题和摘要
  const handleAIGenerate = () => {
    if (!form.content.trim()) {
      toast.error('请先输入文章内容')
      return
    }
    setShowAIModal(true)
  }

  const handleAIGenerated = (title: string, summary: string) => {
    setForm(prev => ({
      ...prev,
      title: title,
      excerpt: summary
    }))
    toast.success('AI生成的标题和摘要已应用')
  }

  const handleSubmit = async (status: 'draft' | 'published') => {
    if (!form.title.trim()) {
      toast.error('请输入文章标题')
      return
    }

    if (!form.content.trim()) {
      toast.error('请输入文章内容')
      return
    }

    try {
      setIsLoading(true)

      const submitData = {
        ...form,
        status,
        excerpt: form.excerpt || form.content.substring(0, 200) + '...',
        // 确保category为null时不发送该字段，或者发送null
        category: form.category || null
      }

      await post('/blog/posts/create/', submitData)

      toast.success(status === 'draft' ? '草稿保存成功' : '文章发布成功')
      router.push('/dashboard/blog')
    } catch (error) {
      console.error('保存文章失败:', error)
      toast.error('保存文章失败')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* 页面标题和操作 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <button
            onClick={() => router.push('/dashboard/blog')}
            className="btn btn-ghost"
          >
            <ArrowLeftIcon className="h-5 w-5 mr-2" />
            返回博客列表
          </button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">写文章</h1>
            <p className="text-gray-600">创作您的新文章</p>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <button
            onClick={() => setIsPreview(!isPreview)}
            className="btn btn-outline"
          >
            <EyeIcon className="h-5 w-5 mr-2" />
            {isPreview ? '编辑' : '预览'}
          </button>
          <button
            onClick={() => handleSubmit('draft')}
            disabled={isLoading}
            className="btn btn-secondary"
          >
            保存草稿
          </button>
          <button
            onClick={() => handleSubmit('published')}
            disabled={isLoading}
            className="btn btn-primary"
          >
            发布文章
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 主要内容区域 */}
        <div className="lg:col-span-2 space-y-6">
          {!isPreview ? (
            <>
              {/* 文章标题 */}
              <div className="clean-card p-6">
                <div className="flex items-center gap-3">
                  <input
                    type="text"
                    placeholder="输入文章标题..."
                    value={form.title}
                    onChange={(e) => handleTitleChange(e.target.value)}
                    className="flex-1 text-2xl font-bold border-none outline-none bg-transparent placeholder-gray-400"
                  />
                  <button
                    onClick={handleAIGenerate}
                    className="flex-shrink-0 p-2 text-purple-600 hover:text-purple-700 hover:bg-purple-50 rounded-md transition-colors"
                    title="AI生成标题和摘要"
                  >
                    <SparklesIcon className="h-6 w-6" />
                  </button>
                </div>
              </div>

              {/* 文章内容 */}
              <div className="clean-card p-6">
                <textarea
                  placeholder="开始写作..."
                  value={form.content}
                  onChange={(e) => setForm(prev => ({ ...prev, content: e.target.value }))}
                  className="w-full h-96 border-none outline-none bg-transparent resize-none placeholder-gray-400"
                />
              </div>

              {/* 文章摘要 */}
              <div className="clean-card p-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  文章摘要（可选）
                </label>
                <textarea
                  placeholder="输入文章摘要，留空将自动生成..."
                  value={form.excerpt}
                  onChange={(e) => setForm(prev => ({ ...prev, excerpt: e.target.value }))}
                  className="input w-full h-20 resize-none"
                />
              </div>
            </>
          ) : (
            /* 预览模式 */
            <div className="clean-card p-6">
              <h1 className="text-3xl font-bold text-gray-900 mb-4">{form.title || '文章标题'}</h1>
              <div className="prose max-w-none">
                {form.content ? (
                  <div className="whitespace-pre-wrap">{form.content}</div>
                ) : (
                  <p className="text-gray-500">文章内容将在这里显示...</p>
                )}
              </div>
            </div>
          )}
        </div>

        {/* 侧边栏设置 */}
        <div className="space-y-6">
          {/* 发布设置 */}
          <div className="clean-card p-4">
            <h3 className="font-medium text-gray-900 mb-3">发布设置</h3>
            <div className="space-y-3">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  文章别名
                </label>
                <input
                  type="text"
                  value={form.slug}
                  onChange={(e) => setForm(prev => ({ ...prev, slug: e.target.value }))}
                  className="input w-full text-sm"
                  placeholder="article-slug"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  分类
                </label>
                <select
                  value={form.category || ''}
                  onChange={(e) => setForm(prev => ({ 
                    ...prev, 
                    category: e.target.value ? parseInt(e.target.value) : null 
                  }))}
                  className="input w-full"
                >
                  <option value="">选择分类</option>
                  {categories.map(category => (
                    <option key={category.category_id} value={category.category_id}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>

          {/* 标签 */}
          <div className="clean-card p-4">
            <h3 className="font-medium text-gray-900 mb-3">标签</h3>
            <div className="flex flex-wrap gap-2">
              {tags.map(tag => (
                <button
                  key={tag.tag_id}
                  onClick={() => handleTagToggle(tag.tag_id)}
                  className={`px-3 py-1 text-sm rounded-full border transition-colors ${
                    form.tag_ids?.includes(tag.tag_id)
                      ? 'bg-primary-100 border-primary-300 text-primary-700'
                      : 'bg-gray-50 border-gray-200 text-gray-700 hover:bg-gray-100'
                  }`}
                >
                  #{tag.name}
                </button>
              ))}
            </div>
          </div>

          {/* SEO设置 */}
          <div className="clean-card p-4">
            <h3 className="font-medium text-gray-900 mb-3">SEO设置</h3>
            <div className="space-y-3">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  特色图片URL
                </label>
                <input
                  type="url"
                  value={form.featured_image}
                  onChange={(e) => setForm(prev => ({ ...prev, featured_image: e.target.value }))}
                  className="input w-full text-sm"
                  placeholder="https://example.com/image.jpg"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  SEO描述
                </label>
                <textarea
                  value={form.meta_description}
                  onChange={(e) => setForm(prev => ({ ...prev, meta_description: e.target.value }))}
                  className="input w-full h-16 text-sm resize-none"
                  placeholder="文章的SEO描述..."
                  maxLength={160}
                />
                <div className="text-xs text-gray-500 mt-1">
                  {form.meta_description?.length || 0}/160
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  关键词
                </label>
                <input
                  type="text"
                  value={form.meta_keywords}
                  onChange={(e) => setForm(prev => ({ ...prev, meta_keywords: e.target.value }))}
                  className="input w-full text-sm"
                  placeholder="关键词1, 关键词2, 关键词3"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* AI标题摘要生成模态框 */}
      <TitleSummaryModal
        isOpen={showAIModal}
        onClose={() => setShowAIModal(false)}
        content={form.content}
        contentType="blog"
        onGenerated={handleAIGenerated}
      />
    </div>
  )
}
