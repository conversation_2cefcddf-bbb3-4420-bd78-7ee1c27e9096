'use client'

import { useState, useEffect } from 'react'
import {
  ChartBarIcon,
  TrophyIcon,
  TagIcon,
  StarIcon,
  PlusIcon,
} from '@heroicons/react/24/outline'
import { get } from '@/lib/api'
import { GrowthStats, Tag, UserValue } from '@/types'
import { calculateCompletionRate } from '@/lib/utils'
import { CompassPageSkeleton } from '@/components/ui'

export default function CompassPage() {
  const [growthStats, setGrowthStats] = useState<GrowthStats | null>(null)
  const [userValues, setUserValues] = useState<UserValue[]>([])
  const [skillTags, setSkillTags] = useState<Tag[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    fetchCompassData()
  }, [])

  const fetchCompassData = async () => {
    try {
      const [statsData, valuesData, tagsData] = await Promise.all([
        get<GrowthStats>('/growth/stats/'),
        get<UserValue[]>('/auth/values/'),
        get<Tag[]>('/growth/tags/?category=技能'),
      ])

      if (statsData) setGrowthStats(statsData)
      if (valuesData) setUserValues(valuesData)
      if (tagsData) setSkillTags(tagsData)
    } catch (error) {
      console.error('Failed to fetch compass data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  if (isLoading) {
    return <CompassPageSkeleton />
  }

  const completionRate = growthStats 
    ? calculateCompletionRate(growthStats.completed, growthStats.total_items)
    : 0

  return (
    <div className="space-y-8">
      {/* 页面标题 */}
      <div>
        <h1 className="text-2xl font-bold text-theme-primary">价值罗盘</h1>
        <p className="mt-1 text-sm text-theme-secondary">
          发现您的技能图谱，明确价值方向
        </p>
      </div>

      {/* 成长概览 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="card">
          <div className="card-body text-center">
            <div className="mx-auto w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mb-4">
              <TrophyIcon className="h-8 w-8 text-primary-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900">总体进度</h3>
            <p className="text-3xl font-bold text-primary-600 mt-2">{completionRate}%</p>
            <p className="text-sm text-gray-500 mt-1">
              已完成 {growthStats?.completed || 0} / {growthStats?.total_items || 0} 个项目
            </p>
          </div>
        </div>

        <div className="card">
          <div className="card-body text-center">
            <div className="mx-auto w-16 h-16 bg-success-100 rounded-full flex items-center justify-center mb-4">
              <ChartBarIcon className="h-8 w-8 text-success-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900">目标达成</h3>
            <p className="text-3xl font-bold text-success-600 mt-2">
              {growthStats?.completed_goals || 0}
            </p>
            <p className="text-sm text-gray-500 mt-1">
              / {growthStats?.total_goals || 0} 个目标
            </p>
          </div>
        </div>

        <div className="card">
          <div className="card-body text-center">
            <div className="mx-auto w-16 h-16 bg-secondary-100 rounded-full flex items-center justify-center mb-4">
              <TagIcon className="h-8 w-8 text-secondary-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900">技能标签</h3>
            <p className="text-3xl font-bold text-secondary-600 mt-2">
              {growthStats?.most_used_tags?.length || 0}
            </p>
            <p className="text-sm text-gray-500 mt-1">个活跃技能</p>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* 技能图谱 */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900">技能图谱</h3>
            <p className="text-sm text-gray-500">基于您完成的项目生成</p>
          </div>
          <div className="card-body">
            {growthStats?.most_used_tags && growthStats.most_used_tags.length > 0 ? (
              <div className="space-y-4">
                {growthStats.most_used_tags.map((tag, index) => {
                  const maxUsage = growthStats.most_used_tags[0]?.usage_count || 1
                  const percentage = (tag.usage_count / maxUsage) * 100
                  
                  return (
                    <div key={index} className="space-y-2">
                      <div className="flex justify-between items-center">
                        <span className="text-sm font-medium text-gray-700">
                          {tag.name}
                        </span>
                        <span className="text-sm text-gray-500">
                          {tag.usage_count} 次
                        </span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-primary-600 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${percentage}%` }}
                        />
                      </div>
                    </div>
                  )
                })}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <TagIcon className="h-12 w-12 mx-auto mb-2 text-gray-300" />
                <p>完成更多项目来构建您的技能图谱</p>
              </div>
            )}
          </div>
        </div>

        {/* 个人价值观 */}
        <div className="card">
          <div className="card-header flex justify-between items-center">
            <div>
              <h3 className="text-lg font-medium text-gray-900">核心价值观</h3>
              <p className="text-sm text-gray-500">指导您成长方向的价值锚点</p>
            </div>
            <button className="btn-outline btn-sm">
              <PlusIcon className="h-4 w-4 mr-1" />
              添加
            </button>
          </div>
          <div className="card-body">
            {userValues.length > 0 ? (
              <div className="space-y-4">
                {userValues.slice(0, 5).map((value) => (
                  <div key={value.value_id} className="flex items-start space-x-3">
                    <div className="flex-shrink-0">
                      <StarIcon className="h-5 w-5 text-warning-500" />
                    </div>
                    <div className="flex-1">
                      <h4 className="text-sm font-medium text-gray-900">
                        {value.title}
                      </h4>
                      {value.description && (
                        <p className="text-sm text-gray-600 mt-1">
                          {value.description}
                        </p>
                      )}
                    </div>
                    <div className="flex-shrink-0">
                      <span className="badge-secondary text-xs">
                        优先级 {value.priority}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <StarIcon className="h-12 w-12 mx-auto mb-2 text-gray-300" />
                <p>添加您的核心价值观</p>
                <p className="text-xs mt-1">让AI更好地理解您的成长方向</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 成长建议 */}
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-medium text-gray-900">AI成长建议</h3>
        </div>
        <div className="card-body">
          <div className="bg-gradient-to-r from-primary-50 to-secondary-50 rounded-lg p-6">
            <div className="flex items-start space-x-4">
              <div className="flex-shrink-0">
                <div className="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center">
                  <span className="text-sm font-bold text-primary-600">AI</span>
                </div>
              </div>
              <div className="flex-1">
                <h4 className="text-sm font-medium text-gray-900 mb-2">
                  基于您的成长数据分析
                </h4>
                {growthStats?.most_used_tags && growthStats.most_used_tags.length > 0 ? (
                  <div className="space-y-2 text-sm text-gray-700">
                    <p>
                      • 您在 <strong>{growthStats.most_used_tags[0]?.name}</strong> 领域表现突出，
                      建议继续深化相关技能
                    </p>
                    <p>
                      • 当前完成率为 <strong>{completionRate}%</strong>，
                      建议适当调整目标设定策略
                    </p>
                    <p>
                      • 可以考虑在现有优势基础上，拓展相关的新技能领域
                    </p>
                  </div>
                ) : (
                  <p className="text-sm text-gray-700">
                    开始创建和完成更多目标，AI将为您提供个性化的成长建议。
                  </p>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 技能发展路径 */}
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-medium text-gray-900">推荐技能发展路径</h3>
        </div>
        <div className="card-body">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {['前端开发', '项目管理', '数据分析', '产品设计', '团队协作', '战略思维'].map((skill, index) => (
              <div key={index} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="text-sm font-medium text-gray-900">{skill}</h4>
                  <span className="text-xs text-gray-500">推荐</span>
                </div>
                <p className="text-xs text-gray-600 mb-3">
                  基于您的兴趣和当前技能推荐
                </p>
                <div className="flex justify-between items-center">
                  <span className="text-xs text-gray-500">匹配度</span>
                  <div className="flex space-x-1">
                    {[...Array(5)].map((_, i) => (
                      <div
                        key={i}
                        className={`w-2 h-2 rounded-full ${
                          i < Math.floor(Math.random() * 5) + 1
                            ? 'bg-primary-500'
                            : 'bg-gray-200'
                        }`}
                      />
                    ))}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}
