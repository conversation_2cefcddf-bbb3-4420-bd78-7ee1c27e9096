'use client'

import { useState, useEffect } from 'react'
import { 
  UserIcon, 
  EnvelopeIcon, 
  PhoneIcon, 
  BuildingOfficeIcon,
  GlobeAltIcon,
  CalendarIcon,
  MapPinIcon,
  PencilIcon,
  CameraIcon
} from '@heroicons/react/24/outline'
import { get, patch } from '@/lib/api'
import toast from 'react-hot-toast'

interface UserProfile {
  profile_id: string
  avatar: string
  display_name: string
  phone: string
  organization: string
  bio: string
  location: string
  website: string
  birth_date: string
  timezone: string
  language: string
  created_at: string
  updated_at: string
}

interface User {
  user_id: string
  email: string
  username: string
  first_name: string
  last_name: string
  created_at: string
  updated_at: string
  profile: UserProfile
}

export default function ProfilePage() {
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isEditing, setIsEditing] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [formData, setFormData] = useState<Partial<UserProfile>>({})

  useEffect(() => {
    fetchUserProfile()
  }, [])

  const fetchUserProfile = async () => {
    try {
      setIsLoading(true)
      const userData = await get<User>('/auth/profile/complete/')
      setUser(userData)
      setFormData(userData?.profile || {})
    } catch (error) {
      console.error('Failed to fetch user profile:', error)
      toast.error('获取用户资料失败')
    } finally {
      setIsLoading(false)
    }
  }

  const handleSave = async () => {
    try {
      setIsSaving(true)
      const updatedProfile = await patch<UserProfile>('/auth/profile/detail/', formData)
      
      if (user) {
        setUser({
          ...user,
          profile: updatedProfile
        })
      }
      
      setIsEditing(false)
      toast.success('资料更新成功')
    } catch (error) {
      console.error('Failed to update profile:', error)
      toast.error('更新资料失败')
    } finally {
      setIsSaving(false)
    }
  }

  const handleCancel = () => {
    setFormData(user?.profile || {})
    setIsEditing(false)
  }

  const formatDate = (dateString: string) => {
    if (!dateString) return '未设置'
    return new Date(dateString).toLocaleDateString('zh-CN')
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-4">
            <div className="h-32 bg-gray-200 rounded"></div>
            <div className="h-64 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-theme-primary">个人资料</h1>
          <p className="mt-1 text-theme-secondary">
            管理您的个人信息和偏好设置
          </p>
        </div>
        <div className="flex gap-3">
          {isEditing ? (
            <>
              <button
                onClick={handleCancel}
                className="btn btn-secondary"
                disabled={isSaving}
              >
                取消
              </button>
              <button
                onClick={handleSave}
                className="btn btn-primary"
                disabled={isSaving}
              >
                {isSaving ? '保存中...' : '保存'}
              </button>
            </>
          ) : (
            <button
              onClick={() => setIsEditing(true)}
              className="btn btn-primary"
            >
              <PencilIcon className="h-4 w-4 mr-2" />
              编辑资料
            </button>
          )}
        </div>
      </div>

      {/* 头像和基本信息 */}
      <div className="card">
        <div className="card-body">
          <div className="flex items-start gap-6">
            <div className="relative">
              <div className="w-24 h-24 rounded-full bg-gradient-to-br from-primary-400 to-primary-600 flex items-center justify-center text-white text-2xl font-bold">
                {user?.profile?.avatar ? (
                  <img 
                    src={user.profile.avatar} 
                    alt="头像" 
                    className="w-24 h-24 rounded-full object-cover"
                  />
                ) : (
                  <UserIcon className="w-12 h-12" />
                )}
              </div>
              {isEditing && (
                <button className="absolute -bottom-2 -right-2 w-8 h-8 bg-primary-500 rounded-full flex items-center justify-center text-white hover:bg-primary-600 transition-colors">
                  <CameraIcon className="w-4 h-4" />
                </button>
              )}
            </div>
            
            <div className="flex-1">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    显示昵称
                  </label>
                  {isEditing ? (
                    <input
                      type="text"
                      value={formData.display_name || ''}
                      onChange={(e) => setFormData({...formData, display_name: e.target.value})}
                      className="input"
                      placeholder="请输入显示昵称"
                    />
                  ) : (
                    <p className="text-gray-900">{user?.profile?.display_name || user?.username || '未设置'}</p>
                  )}
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    邮箱地址
                  </label>
                  <p className="text-gray-900 flex items-center">
                    <EnvelopeIcon className="w-4 h-4 mr-2 text-gray-400" />
                    {user?.email}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 详细信息 */}
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-medium text-theme-primary">详细信息</h3>
        </div>
        <div className="card-body">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <PhoneIcon className="w-4 h-4 inline mr-1" />
                手机号码
              </label>
              {isEditing ? (
                <input
                  type="tel"
                  value={formData.phone || ''}
                  onChange={(e) => setFormData({...formData, phone: e.target.value})}
                  className="input"
                  placeholder="请输入手机号码"
                />
              ) : (
                <p className="text-gray-900">{user?.profile?.phone || '未设置'}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <BuildingOfficeIcon className="w-4 h-4 inline mr-1" />
                组织/公司
              </label>
              {isEditing ? (
                <input
                  type="text"
                  value={formData.organization || ''}
                  onChange={(e) => setFormData({...formData, organization: e.target.value})}
                  className="input"
                  placeholder="请输入组织或公司名称"
                />
              ) : (
                <p className="text-gray-900">{user?.profile?.organization || '未设置'}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <MapPinIcon className="w-4 h-4 inline mr-1" />
                所在地
              </label>
              {isEditing ? (
                <input
                  type="text"
                  value={formData.location || ''}
                  onChange={(e) => setFormData({...formData, location: e.target.value})}
                  className="input"
                  placeholder="请输入所在地"
                />
              ) : (
                <p className="text-gray-900">{user?.profile?.location || '未设置'}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <GlobeAltIcon className="w-4 h-4 inline mr-1" />
                个人网站
              </label>
              {isEditing ? (
                <input
                  type="url"
                  value={formData.website || ''}
                  onChange={(e) => setFormData({...formData, website: e.target.value})}
                  className="input"
                  placeholder="https://example.com"
                />
              ) : (
                <p className="text-gray-900">
                  {user?.profile?.website ? (
                    <a href={user.profile.website} target="_blank" rel="noopener noreferrer" className="text-primary-600 hover:text-primary-700">
                      {user.profile.website}
                    </a>
                  ) : '未设置'}
                </p>
              )}
            </div>

            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                个性签名
              </label>
              {isEditing ? (
                <textarea
                  value={formData.bio || ''}
                  onChange={(e) => setFormData({...formData, bio: e.target.value})}
                  className="input"
                  rows={3}
                  placeholder="写下您的个性签名或简介..."
                />
              ) : (
                <p className="text-gray-900">{user?.profile?.bio || '未设置'}</p>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* 账户信息 */}
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-medium text-theme-primary">账户信息</h3>
        </div>
        <div className="card-body">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <CalendarIcon className="w-4 h-4 inline mr-1" />
                账户创建时间
              </label>
              <p className="text-gray-900">{formatDate(user?.created_at || '')}</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                最后更新时间
              </label>
              <p className="text-gray-900">{formatDate(user?.updated_at || '')}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
