'use client'

import { useState, useEffect, useMemo, useCallback } from 'react'
import { PlusIcon, ClockIcon, CheckCircleIcon, CalendarIcon } from '@heroicons/react/24/outline'
import { get, post, patch, put, del } from '@/lib/api'
import { GrowthItem, GrowthItemCreate, Tag } from '@/types'
import toast from 'react-hot-toast'
import GrowthItemCardWithSubtasks from '@/components/growth/GrowthItemCardWithSubtasks'
import CreateItemModal from '@/components/growth/CreateItemModal'
import EditItemModal from '@/components/growth/EditItemModal'
import { GrowthPageSkeleton } from '@/components/ui'
import DeleteConfirmModal from '@/components/growth/DeleteConfirmModal'
import SubtaskGenerationModal from '@/components/ai/SubtaskGenerationModal'
import GrowthFilters from '@/components/growth/GrowthFilters'

export default function GrowthPage() {
  const [items, setItems] = useState<GrowthItem[]>([])
  const [tags, setTags] = useState<Tag[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [showEditModal, setShowEditModal] = useState(false)
  const [showDeleteModal, setShowDeleteModal] = useState(false)
  const [showAIModal, setShowAIModal] = useState(false)
  const [selectedItem, setSelectedItem] = useState<GrowthItem | null>(null)
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [typeFilter, setTypeFilter] = useState<string>('all')
  const [searchQuery, setSearchQuery] = useState('')
  const [isCreating, setIsCreating] = useState(false)
  const [isUpdating, setIsUpdating] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)

  // 获取数据
  const fetchItems = useCallback(async () => {
    try {
      const params = new URLSearchParams()
      if (statusFilter !== 'all') params.append('status', statusFilter)
      if (typeFilter !== 'all') params.append('item_type', typeFilter)

      const itemsData = await get<any>(`/growth/items/?${params.toString()}`)

      // 处理分页响应格式
      const items = itemsData?.results || itemsData || []
      setItems(Array.isArray(items) ? items : [])
    } catch (error) {
      console.error('获取成长项失败:', error)
      toast.error('获取成长项失败')
      setItems([])
    }
  }, [statusFilter, typeFilter])

  const fetchTags = useCallback(async () => {
    try {
      const tagsData = await get<Tag[]>('/growth/tags/')
      setTags(Array.isArray(tagsData) ? tagsData : [])
    } catch (error) {
      console.error('获取标签失败:', error)
      toast.error('获取标签失败')
      setTags([])
    }
  }, [])

  useEffect(() => {
    const initData = async () => {
      setIsLoading(true)
      try {
        await Promise.all([fetchItems(), fetchTags()])
      } finally {
        setIsLoading(false)
      }
    }
    initData()
  }, [fetchItems, fetchTags])

  // 当过滤条件变化时重新获取数据
  useEffect(() => {
    fetchItems()
  }, [statusFilter, typeFilter])

  // 创建项目
  const handleCreateSubmit = useCallback(async (form: GrowthItemCreate) => {
    setIsCreating(true)
    try {
      await post<GrowthItem>('/growth/items/', form)
      await fetchItems()
      toast.success('项目创建成功')
      setShowCreateModal(false)
    } catch (error) {
      console.error('创建失败:', error)
      toast.error('创建失败，请重试')
    } finally {
      setIsCreating(false)
    }
  }, [fetchItems])

  // 编辑项目
  const handleEdit = useCallback((item: GrowthItem) => {
    setSelectedItem(item)
    setShowEditModal(true)
  }, [])

  const handleEditSubmit = useCallback(async (itemId: string, form: Partial<GrowthItem>) => {
    setIsUpdating(true)
    try {
      await patch(`/growth/items/${itemId}/`, form)
      toast.success('项目更新成功')
      await fetchItems()
      setShowEditModal(false)
      setSelectedItem(null)
    } catch (error) {
      console.error('更新失败:', error)
      toast.error('更新失败，请重试')
    } finally {
      setIsUpdating(false)
    }
  }, [fetchItems])

  // 删除项目
  const handleDelete = useCallback((item: GrowthItem) => {
    setSelectedItem(item)
    setShowDeleteModal(true)
  }, [])

  const handleDeleteConfirm = useCallback(async (itemId: string) => {
    setIsDeleting(true)
    try {
      await del(`/growth/items/${itemId}/`)
      toast.success('项目删除成功')
      await fetchItems()
      setShowDeleteModal(false)
      setSelectedItem(null)
    } catch (error) {
      console.error('删除失败:', error)
      toast.error('删除失败，请重试')
    } finally {
      setIsDeleting(false)
    }
  }, [fetchItems])

  // 状态更新
  const handleStatusChange = useCallback(async (itemId: string, newStatus: string) => {
    try {
      await patch(`/growth/items/${itemId}/status/`, { status: newStatus })
      toast.success('状态更新成功')
      fetchItems()
    } catch (error) {
      console.error('状态更新失败:', error)
      toast.error('状态更新失败')
    }
  }, [fetchItems])

  // AI生成子任务
  const handleAIGenerate = useCallback((item: GrowthItem) => {
    setSelectedItem(item)
    setShowAIModal(true)
  }, [])

  // 处理AI生成的子任务
  const handleSubtasksGenerated = useCallback(async (response: any) => {
    if (response.parent_updated) {
      toast.success(`成功生成 ${response.subtasks.length} 个子任务并更新父任务状态！`)
      // 刷新数据以显示更新后的任务状态和新的子任务
      fetchItems()
    } else {
      toast.success(`成功生成 ${response.subtasks.length} 个子任务！`)
    }
    console.log('Generated subtasks response:', response)
  }, [fetchItems])

  // 过滤数据
  const filteredItems = useMemo(() => {
    const itemsArray = Array.isArray(items) ? items : []
    if (!searchQuery) return itemsArray

    const query = searchQuery.toLowerCase()
    return itemsArray.filter(item =>
      item.title.toLowerCase().includes(query) ||
      item.description?.toLowerCase().includes(query)
    )
  }, [items, searchQuery])

  const groupedItems = useMemo(() => {
    // 只显示顶级任务（没有父任务的任务），子任务会在父任务卡片中显示
    const topLevelItems = filteredItems.filter(item => !item.parent)

    return {
      future_plan: topLevelItems.filter(item => item.status === 'future_plan'),
      in_progress: topLevelItems.filter(item => item.status === 'in_progress'),
      completed: topLevelItems.filter(item => item.status === 'completed'),
    }
  }, [filteredItems])

  if (isLoading) {
    return <GrowthPageSkeleton />
  }

  return (
    <div className="space-y-6">
      {/* 页面标题和操作 */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-theme-primary">
            成长引擎
          </h1>
          <p className="mt-1 text-theme-secondary">
            管理您的成长目标和任务
          </p>
        </div>
        <div className="mt-4 sm:mt-0">
          <button
            onClick={() => setShowCreateModal(true)}
            className="btn btn-primary"
          >
            <PlusIcon className="h-5 w-5 mr-2" />
            添加项目
          </button>
        </div>
      </div>

      {/* 搜索和过滤 */}
      <GrowthFilters
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        statusFilter={statusFilter}
        onStatusFilterChange={setStatusFilter}
        typeFilter={typeFilter}
        onTypeFilterChange={setTypeFilter}
      />

      {/* 成长项目列表 */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 未来计划 */}
        <div className="space-y-4">
          <h2 className="text-lg font-semibold text-gray-900 flex items-center">
            <ClockIcon className="h-5 w-5 mr-2 text-yellow-500" />
            未来计划 ({groupedItems.future_plan.length})
          </h2>
          <div className="space-y-3">
            {groupedItems.future_plan.map((item) => (
              <GrowthItemCardWithSubtasks
                key={item.item_id}
                item={item}
                onStatusChange={handleStatusChange}
                onEdit={handleEdit}
                onDelete={handleDelete}
                onAIGenerate={handleAIGenerate}
              />
            ))}
            {groupedItems.future_plan.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                暂无未来计划
              </div>
            )}
          </div>
        </div>

        {/* 进行中 */}
        <div className="space-y-4">
          <h2 className="text-lg font-semibold text-gray-900 flex items-center">
            <CheckCircleIcon className="h-5 w-5 mr-2 text-blue-500" />
            进行中 ({groupedItems.in_progress.length})
          </h2>
          <div className="space-y-3">
            {groupedItems.in_progress.map((item) => (
              <GrowthItemCardWithSubtasks
                key={item.item_id}
                item={item}
                onStatusChange={handleStatusChange}
                onEdit={handleEdit}
                onDelete={handleDelete}
                onAIGenerate={handleAIGenerate}
              />
            ))}
            {groupedItems.in_progress.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                暂无进行中的项目
              </div>
            )}
          </div>
        </div>

        {/* 已完成 */}
        <div className="space-y-4">
          <h2 className="text-lg font-semibold text-gray-900 flex items-center">
            <CalendarIcon className="h-5 w-5 mr-2 text-green-500" />
            已完成 ({groupedItems.completed.length})
          </h2>
          <div className="space-y-3">
            {groupedItems.completed.map((item) => (
              <GrowthItemCardWithSubtasks
                key={item.item_id}
                item={item}
                onStatusChange={handleStatusChange}
                onEdit={handleEdit}
                onDelete={handleDelete}
                onAIGenerate={handleAIGenerate}
              />
            ))}
            {groupedItems.completed.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                暂无已完成的项目
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 创建模态框 */}
      <CreateItemModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onSubmit={handleCreateSubmit}
        tags={tags}
        isCreating={isCreating}
      />

      {/* 编辑模态框 */}
      <EditItemModal
        isOpen={showEditModal}
        onClose={() => {
          setShowEditModal(false)
          setSelectedItem(null)
        }}
        onSubmit={handleEditSubmit}
        item={selectedItem}
        tags={tags}
        isUpdating={isUpdating}
      />

      {/* 删除确认模态框 */}
      <DeleteConfirmModal
        isOpen={showDeleteModal}
        onClose={() => {
          setShowDeleteModal(false)
          setSelectedItem(null)
        }}
        onConfirm={handleDeleteConfirm}
        item={selectedItem}
        isDeleting={isDeleting}
      />

      {/* AI子任务生成模态框 */}
      <SubtaskGenerationModal
        isOpen={showAIModal}
        onClose={() => {
          setShowAIModal(false)
          setSelectedItem(null)
        }}
        projectTitle={selectedItem?.title || ''}
        projectDescription={selectedItem?.description || ''}
        parentItemId={selectedItem?.item_id}
        onSubtasksGenerated={handleSubtasksGenerated}
      />
    </div>
  )
}
