'use client'

import React, { useState } from 'react'
import { 
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ed<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Type<PERSON>,
  FloatingCard,
  Pulse,
  <PERSON><PERSON>,
  <PERSON>low,
  MorphingButton,
  ProgressRing
} from '@/components/ui'
import { useTheme } from '@/contexts/ThemeContext'

export default function DemoPage() {
  const { colors, setBaseColor } = useTheme()
  const [morphTrigger, setMorphTrigger] = useState(false)

  const demoCards = [
    { title: '动画卡片 1', content: '这是一个带有动画效果的卡片' },
    { title: '动画卡片 2', content: '支持交错动画显示' },
    { title: '动画卡片 3', content: '提升用户体验' },
    { title: '动画卡片 4', content: '现代化的界面设计' }
  ]

  const colorOptions = [
    { name: '青绿色', value: '#14b8a6' },
    { name: '蓝色', value: '#3b82f6' },
    { name: '紫色', value: '#8b5cf6' },
    { name: '粉色', value: '#ec4899' },
    { name: '橙色', value: '#f59e0b' }
  ]

  return (
    <div className="space-y-12 p-6">
      {/* 页面标题 */}
      <AnimatedContainer animation="fade-in">
        <div className="text-center">
          <h1 className="text-4xl font-bold gradient-text mb-4">
            <Typewriter text="UI/UX 优化演示" speed={100} />
          </h1>
          <p className="text-neutral-600 text-lg">
            体验全新的界面设计和交互动效
          </p>
        </div>
      </AnimatedContainer>

      {/* 主题色彩选择器 */}
      <AnimatedContainer animation="slide-in-left" delay={200}>
        <div className="card p-6">
          <h2 className="text-2xl font-semibold mb-4 flex items-center">
            <Pulse color="primary" className="mr-3">
              <div className="w-4 h-4 bg-primary-500 rounded-full" />
            </Pulse>
            主题色彩系统
          </h2>
          <p className="text-neutral-600 mb-6">
            选择不同的主题色彩，体验统一的配色系统
          </p>
          <div className="flex flex-wrap gap-3">
            {colorOptions.map((color) => (
              <button
                key={color.value}
                onClick={() => setBaseColor(color.value)}
                className="btn btn-outline interactive-scale"
                style={{ borderColor: color.value, color: color.value }}
              >
                {color.name}
              </button>
            ))}
          </div>
        </div>
      </AnimatedContainer>

      {/* 统计数据展示 */}
      <AnimatedContainer animation="slide-in-right" delay={400}>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          {[
            { label: '用户满意度', value: 95, suffix: '%' },
            { label: '性能提升', value: 40, suffix: '%' },
            { label: '加载速度', value: 2.3, suffix: 's', decimals: 1 },
            { label: '动画效果', value: 12, suffix: '种' }
          ].map((stat, index) => (
            <FloatingCard key={index} className="card p-6 text-center">
              <div className="text-3xl font-bold text-primary-600 mb-2">
                <CountUp 
                  end={stat.value} 
                  suffix={stat.suffix}
                  decimals={stat.decimals || 0}
                  duration={2000 + index * 200}
                />
              </div>
              <div className="text-neutral-600">{stat.label}</div>
            </FloatingCard>
          ))}
        </div>
      </AnimatedContainer>

      {/* 进度环展示 */}
      <AnimatedContainer animation="scale-in" delay={600}>
        <div className="card p-8">
          <h2 className="text-2xl font-semibold mb-6 text-center">
            项目完成进度
          </h2>
          <div className="flex justify-center space-x-8">
            <div className="text-center">
              <ProgressRing progress={85} color="primary" />
              <p className="mt-4 text-neutral-600">UI设计</p>
            </div>
            <div className="text-center">
              <ProgressRing progress={92} color="secondary" />
              <p className="mt-4 text-neutral-600">动效实现</p>
            </div>
            <div className="text-center">
              <ProgressRing progress={78} color="success" />
              <p className="mt-4 text-neutral-600">性能优化</p>
            </div>
          </div>
        </div>
      </AnimatedContainer>

      {/* 交错动画列表 */}
      <AnimatedContainer animation="fade-in" delay={800}>
        <div className="card p-6">
          <h2 className="text-2xl font-semibold mb-6">
            <Glow color="primary">
              交错动画效果
            </Glow>
          </h2>
          <StaggeredList delay={150}>
            {demoCards.map((card, index) => (
              <FloatingCard key={index} className="card p-4 mb-4">
                <h3 className="font-semibold text-primary-700 mb-2">
                  {card.title}
                </h3>
                <p className="text-neutral-600">{card.content}</p>
              </FloatingCard>
            ))}
          </StaggeredList>
        </div>
      </AnimatedContainer>

      {/* 按钮动效展示 */}
      <AnimatedContainer animation="slide-in-left" delay={1000}>
        <div className="card p-6">
          <h2 className="text-2xl font-semibold mb-6">按钮交互效果</h2>
          <div className="flex flex-wrap gap-4">
            <button className="btn btn-primary">
              主要按钮
            </button>
            <button className="btn btn-secondary">
              次要按钮
            </button>
            <button className="btn btn-success">
              成功按钮
            </button>
            <button className="btn btn-warning">
              警告按钮
            </button>
            <button className="btn btn-error">
              错误按钮
            </button>
            <button className="btn btn-outline">
              轮廓按钮
            </button>
            <button className="btn btn-ghost">
              幽灵按钮
            </button>
          </div>
        </div>
      </AnimatedContainer>

      {/* 变形按钮演示 */}
      <AnimatedContainer animation="slide-in-right" delay={1200}>
        <div className="card p-6">
          <h2 className="text-2xl font-semibold mb-6">变形按钮效果</h2>
          <div className="flex items-center space-x-4">
            <MorphingButton
              trigger={morphTrigger}
              morphTo={
                <div className="flex items-center">
                  <div className="loading-spinner w-4 h-4 mr-2" />
                  处理中...
                </div>
              }
              className="btn btn-primary"
            >
              点击提交
            </MorphingButton>
            <button
              onClick={() => {
                setMorphTrigger(true)
                setTimeout(() => setMorphTrigger(false), 2000)
              }}
              className="btn btn-outline"
            >
              触发变形
            </button>
          </div>
        </div>
      </AnimatedContainer>

      {/* Shimmer效果展示 */}
      <AnimatedContainer animation="fade-in" delay={1400}>
        <div className="card p-6">
          <h2 className="text-2xl font-semibold mb-6">Shimmer 加载效果</h2>
          <div className="space-y-4">
            <Shimmer className="h-4 bg-neutral-200 rounded" />
            <Shimmer className="h-4 bg-neutral-200 rounded w-3/4" />
            <Shimmer className="h-4 bg-neutral-200 rounded w-1/2" />
          </div>
        </div>
      </AnimatedContainer>

      {/* 徽章展示 */}
      <AnimatedContainer animation="scale-in" delay={1600}>
        <div className="card p-6">
          <h2 className="text-2xl font-semibold mb-6">徽章样式</h2>
          <div className="flex flex-wrap gap-3">
            <span className="badge badge-primary">主要</span>
            <span className="badge badge-secondary">次要</span>
            <span className="badge badge-success">成功</span>
            <span className="badge badge-warning">警告</span>
            <span className="badge badge-error">错误</span>
            <span className="badge badge-info">信息</span>
          </div>
        </div>
      </AnimatedContainer>

      {/* 总结 */}
      <AnimatedContainer animation="fade-in" delay={1800}>
        <div className="card p-8 text-center">
          <h2 className="text-3xl font-bold gradient-text mb-4">
            优化完成！
          </h2>
          <p className="text-neutral-600 text-lg mb-6">
            全新的UI/UX设计为您带来更好的使用体验
          </p>
          <div className="flex justify-center space-x-4">
            <Pulse color="success">
              <span className="text-success-600 font-medium">系统就绪</span>
            </Pulse>
          </div>
        </div>
      </AnimatedContainer>
    </div>
  )
}
