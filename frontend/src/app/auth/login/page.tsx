/**
 * 灵境(Mentia)登录页面 - 符合应用设计风格的极光玻璃效果版本
 *
 * 设计特色：
 * - 极光背景动画效果
 * - 玻璃拟态卡片设计
 * - 符合应用整体视觉风格
 * - 优化的性能和用户体验
 *
 * 技术优化：
 * - 使用统一的API客户端
 * - 集成react-hot-toast通知系统
 * - 使用应用UI组件库
 * - 符合Next.js最佳实践
 */

'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { EyeIcon, EyeSlashIcon } from '@heroicons/react/24/outline'
import { post, tokenManager } from '@/lib/api'
import { Button, Input } from '@/components/ui'
import toast from 'react-hot-toast'

// 表单验证模式
const loginSchema = z.object({
  email: z.string().email('请输入有效的邮箱地址'),
  password: z.string().min(1, '请输入密码'),
})

type LoginFormData = z.infer<typeof loginSchema>

export default function LoginPage() {
  const router = useRouter()
  const [showPassword, setShowPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
  })

  const onSubmit = async (data: LoginFormData) => {
    setIsLoading(true)

    try {
      const response = await post('/auth/login/', data)

      // 保存令牌
      tokenManager.setTokens(response.tokens)

      toast.success('登录成功！')

      // 重定向到仪表板
      router.push('/dashboard')
    } catch (error) {
      console.error('登录失败:', error)
      toast.error('登录失败，请检查邮箱和密码')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center relative overflow-hidden">
      {/* 极光背景动画 */}
      <div className="absolute inset-0 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
        <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,_var(--tw-gradient-stops))] from-purple-900/20 via-slate-900/40 to-slate-900"></div>

        {/* 极光效果 */}
        <div className="absolute top-0 left-1/4 w-96 h-96 bg-gradient-to-r from-blue-400/30 to-purple-600/30 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-gradient-to-r from-purple-400/20 to-pink-600/20 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-gradient-to-r from-teal-400/20 to-blue-600/20 rounded-full blur-2xl animate-pulse delay-500"></div>
      </div>

      {/* 登录卡片 */}
      <div className="relative z-10 max-w-md w-full mx-4">
        <div className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-2xl shadow-2xl p-8 space-y-8">
          {/* 头部 */}
          <div className="text-center">
            <div className="mx-auto h-16 w-16 flex items-center justify-center rounded-full bg-gradient-to-r from-purple-500 to-blue-500 shadow-lg">
              <span className="text-3xl font-bold text-white">灵</span>
            </div>
            <h2 className="mt-6 text-3xl font-bold text-white">
              登录到灵境
            </h2>
            <p className="mt-2 text-sm text-gray-300">
              还没有账户？{' '}
              <Link
                href="/auth/register"
                className="font-medium text-purple-400 hover:text-purple-300 transition-colors"
              >
                立即注册
              </Link>
            </p>
          </div>

          {/* 登录表单 */}
          <form className="space-y-6" onSubmit={handleSubmit(onSubmit)}>
            <div className="space-y-4">
              <Input
                {...register('email')}
                type="email"
                label="邮箱地址"
                placeholder="请输入邮箱地址"
                autoComplete="email"
                error={errors.email?.message}
                className="bg-white/5 border-white/20 text-white placeholder-gray-400 focus:border-purple-400 focus:ring-purple-400"
              />

              <Input
                {...register('password')}
                type={showPassword ? 'text' : 'password'}
                label="密码"
                placeholder="请输入密码"
                autoComplete="current-password"
                error={errors.password?.message}
                className="bg-white/5 border-white/20 text-white placeholder-gray-400 focus:border-purple-400 focus:ring-purple-400"
                rightIcon={showPassword ? <EyeSlashIcon className="h-5 w-5 text-gray-400" /> : <EyeIcon className="h-5 w-5 text-gray-400" />}
                onRightIconClick={() => setShowPassword(!showPassword)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <input
                  id="remember-me"
                  name="remember-me"
                  type="checkbox"
                  className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded bg-white/10"
                />
                <label htmlFor="remember-me" className="ml-2 block text-sm text-gray-300">
                  记住我
                </label>
              </div>

              <div className="text-sm">
                <Link
                  href="/auth/forgot-password"
                  className="font-medium text-purple-400 hover:text-purple-300 transition-colors"
                >
                  忘记密码？
                </Link>
              </div>
            </div>

            <div>
              <Button
                type="submit"
                variant="primary"
                size="lg"
                loading={isLoading}
                className="w-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 border-0 shadow-lg"
              >
                {isLoading ? '登录中...' : '登录'}
              </Button>
            </div>
          </form>

          {/* 底部信息 */}
          <div className="text-center">
            <p className="text-xs text-gray-400">
              通过登录，您同意我们的{' '}
              <Link href="/terms" className="text-purple-400 hover:text-purple-300">
                服务条款
              </Link>{' '}
              和{' '}
              <Link href="/privacy" className="text-purple-400 hover:text-purple-300">
                隐私政策
              </Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
