'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { tokenManager } from '@/lib/api'
import { StageLoader, loadingStages } from '@/components/ui'

export default function HomePage() {
  const router = useRouter()
  const [showLoader, setShowLoader] = useState(true)

  useEffect(() => {
    // 检查用户是否已登录
    if (tokenManager.isAuthenticated()) {
      // 已登录，显示加载动画后重定向到仪表板
      setTimeout(() => {
        router.push('/dashboard')
      }, 3200) // 总加载时间约3.2秒
    } else {
      // 未登录，显示加载动画后重定向到登录页
      setTimeout(() => {
        router.push('/auth/login')
      }, 2400) // 较短的加载时间
    }
  }, [router])

  const handleLoadingComplete = () => {
    setShowLoader(false)
  }

  if (showLoader) {
    return (
      <StageLoader
        stages={loadingStages.dashboard}
        onComplete={handleLoadingComplete}
      />
    )
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-primary-50 to-secondary-50">
      <div className="text-center">
        <div className="loading-spinner w-8 h-8 mx-auto mb-4"></div>
        <p className="text-neutral-600">正在跳转...</p>
      </div>
    </div>
  )
}
