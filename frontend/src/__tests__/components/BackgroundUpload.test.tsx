/**
 * 背景上传组件测试 - 灵境(Mentia)前端
 * 
 * 文件功能：
 *   测试背景图片上传组件的各种功能和边界情况
 * 
 * 测试覆盖：
 *   1. 组件渲染
 *   2. 文件选择和验证
 *   3. 上传功能
 *   4. 错误处理
 *   5. 用户交互
 * 
 * 运行方式：
 *   npm test BackgroundUpload.test.tsx
 *   npm test -- --watch
 * 
 * 作者: Mentia前端团队
 * 创建时间: 2025-09-05
 * 版本: v1.0
 */

import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { jest } from '@jest/globals'
import BackgroundUpload from '@/components/ui/BackgroundUpload'
import * as api from '@/lib/api'
import * as errorHandler from '@/lib/errorHandler'

// Mock dependencies
jest.mock('@/lib/api')
jest.mock('@/lib/errorHandler')
jest.mock('react-hot-toast', () => ({
  success: jest.fn(),
  error: jest.fn()
}))

const mockPost = api.post as jest.MockedFunction<typeof api.post>
const mockDel = api.del as jest.MockedFunction<typeof api.del>
const mockHandleError = errorHandler.handleError as jest.MockedFunction<typeof errorHandler.handleError>

describe('BackgroundUpload Component', () => {
  const defaultProps = {
    onBackgroundChange: jest.fn(),
    onOpacityChange: jest.fn(),
    onBlurChange: jest.fn()
  }

  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('Rendering', () => {
    it('renders upload area when no background is set', () => {
      render(<BackgroundUpload {...defaultProps} />)
      
      expect(screen.getByText('选择背景图片')).toBeInTheDocument()
      expect(screen.getByText('支持 JPEG、PNG、WebP、GIF 格式，最大 10MB')).toBeInTheDocument()
    })

    it('renders background controls when background is set', () => {
      render(
        <BackgroundUpload 
          {...defaultProps} 
          currentBackground="https://example.com/bg.jpg" 
        />
      )
      
      expect(screen.getByText('更换')).toBeInTheDocument()
      expect(screen.getByText('删除')).toBeInTheDocument()
    })

    it('renders opacity and blur controls', () => {
      render(<BackgroundUpload {...defaultProps} />)
      
      expect(screen.getByText('背景透明度')).toBeInTheDocument()
      expect(screen.getByText('背景模糊度')).toBeInTheDocument()
    })
  })

  describe('File Validation', () => {
    it('accepts valid image files', async () => {
      const user = userEvent.setup()
      render(<BackgroundUpload {...defaultProps} />)
      
      const file = new File(['test'], 'test.jpg', { type: 'image/jpeg' })
      const input = screen.getByRole('button', { name: '选择背景图片' })
      
      // Mock successful upload
      mockPost.mockResolvedValueOnce({
        background_url: 'https://example.com/uploaded.jpg',
        message: '上传成功'
      })
      
      await user.click(input)
      
      // Simulate file selection
      const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement
      Object.defineProperty(fileInput, 'files', {
        value: [file],
        writable: false
      })
      
      fireEvent.change(fileInput)
      
      await waitFor(() => {
        expect(mockPost).toHaveBeenCalledWith(
          '/auth/background/upload/',
          expect.any(FormData)
        )
      })
    })

    it('rejects files that are too large', async () => {
      const user = userEvent.setup()
      render(<BackgroundUpload {...defaultProps} />)
      
      // Create a file larger than 10MB
      const largeFile = new File(['x'.repeat(11 * 1024 * 1024)], 'large.jpg', { 
        type: 'image/jpeg' 
      })
      
      const input = screen.getByRole('button', { name: '选择背景图片' })
      await user.click(input)
      
      const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement
      Object.defineProperty(fileInput, 'files', {
        value: [largeFile],
        writable: false
      })
      
      fireEvent.change(fileInput)
      
      // Should not call upload API
      expect(mockPost).not.toHaveBeenCalled()
    })

    it('rejects invalid file types', async () => {
      const user = userEvent.setup()
      render(<BackgroundUpload {...defaultProps} />)
      
      const invalidFile = new File(['test'], 'test.txt', { type: 'text/plain' })
      
      const input = screen.getByRole('button', { name: '选择背景图片' })
      await user.click(input)
      
      const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement
      Object.defineProperty(fileInput, 'files', {
        value: [invalidFile],
        writable: false
      })
      
      fireEvent.change(fileInput)
      
      // Should not call upload API
      expect(mockPost).not.toHaveBeenCalled()
    })

    it('rejects files with dangerous characters in filename', async () => {
      const user = userEvent.setup()
      render(<BackgroundUpload {...defaultProps} />)
      
      const dangerousFile = new File(['test'], '../../../etc/passwd.jpg', { 
        type: 'image/jpeg' 
      })
      
      const input = screen.getByRole('button', { name: '选择背景图片' })
      await user.click(input)
      
      const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement
      Object.defineProperty(fileInput, 'files', {
        value: [dangerousFile],
        writable: false
      })
      
      fireEvent.change(fileInput)
      
      // Should not call upload API
      expect(mockPost).not.toHaveBeenCalled()
    })
  })

  describe('Upload Functionality', () => {
    it('uploads file successfully', async () => {
      const user = userEvent.setup()
      const onBackgroundChange = jest.fn()
      
      render(
        <BackgroundUpload 
          {...defaultProps} 
          onBackgroundChange={onBackgroundChange}
        />
      )
      
      const file = new File(['test'], 'test.jpg', { type: 'image/jpeg' })
      
      mockPost.mockResolvedValueOnce({
        background_url: 'https://example.com/uploaded.jpg',
        message: '上传成功'
      })
      
      const input = screen.getByRole('button', { name: '选择背景图片' })
      await user.click(input)
      
      const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement
      Object.defineProperty(fileInput, 'files', {
        value: [file],
        writable: false
      })
      
      fireEvent.change(fileInput)
      
      await waitFor(() => {
        expect(onBackgroundChange).toHaveBeenCalledWith('https://example.com/uploaded.jpg')
      })
    })

    it('handles upload errors gracefully', async () => {
      const user = userEvent.setup()
      render(<BackgroundUpload {...defaultProps} />)
      
      const file = new File(['test'], 'test.jpg', { type: 'image/jpeg' })
      
      const uploadError = new Error('Upload failed')
      mockPost.mockRejectedValueOnce(uploadError)
      
      const input = screen.getByRole('button', { name: '选择背景图片' })
      await user.click(input)
      
      const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement
      Object.defineProperty(fileInput, 'files', {
        value: [file],
        writable: false
      })
      
      fireEvent.change(fileInput)
      
      await waitFor(() => {
        expect(mockHandleError).toHaveBeenCalledWith(
          uploadError,
          expect.objectContaining({
            context: 'background-upload',
            showToast: true
          })
        )
      })
    })

    it('shows loading state during upload', async () => {
      const user = userEvent.setup()
      render(<BackgroundUpload {...defaultProps} />)
      
      const file = new File(['test'], 'test.jpg', { type: 'image/jpeg' })
      
      // Mock a delayed response
      mockPost.mockImplementation(() => 
        new Promise(resolve => setTimeout(() => resolve({
          background_url: 'https://example.com/uploaded.jpg'
        }), 100))
      )
      
      const input = screen.getByRole('button', { name: '选择背景图片' })
      await user.click(input)
      
      const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement
      Object.defineProperty(fileInput, 'files', {
        value: [file],
        writable: false
      })
      
      fireEvent.change(fileInput)
      
      // Should show loading state
      expect(screen.getByText('上传中...')).toBeInTheDocument()
      
      await waitFor(() => {
        expect(screen.queryByText('上传中...')).not.toBeInTheDocument()
      })
    })
  })

  describe('Delete Functionality', () => {
    it('deletes background successfully', async () => {
      const user = userEvent.setup()
      const onBackgroundChange = jest.fn()
      
      render(
        <BackgroundUpload 
          {...defaultProps}
          currentBackground="https://example.com/bg.jpg"
          onBackgroundChange={onBackgroundChange}
        />
      )
      
      mockDel.mockResolvedValueOnce({ message: '删除成功' })
      
      const deleteButton = screen.getByRole('button', { name: '删除' })
      await user.click(deleteButton)
      
      await waitFor(() => {
        expect(mockDel).toHaveBeenCalledWith('/auth/background/upload/')
        expect(onBackgroundChange).toHaveBeenCalledWith(null)
      })
    })

    it('handles delete errors gracefully', async () => {
      const user = userEvent.setup()
      
      render(
        <BackgroundUpload 
          {...defaultProps}
          currentBackground="https://example.com/bg.jpg"
        />
      )
      
      const deleteError = new Error('Delete failed')
      mockDel.mockRejectedValueOnce(deleteError)
      
      const deleteButton = screen.getByRole('button', { name: '删除' })
      await user.click(deleteButton)
      
      await waitFor(() => {
        expect(mockHandleError).toHaveBeenCalledWith(
          deleteError,
          expect.objectContaining({
            context: 'background-delete',
            showToast: true
          })
        )
      })
    })
  })

  describe('Opacity and Blur Controls', () => {
    it('calls onOpacityChange when opacity slider changes', async () => {
      const user = userEvent.setup()
      const onOpacityChange = jest.fn()
      
      render(
        <BackgroundUpload 
          {...defaultProps}
          onOpacityChange={onOpacityChange}
          backgroundOpacity={0.5}
        />
      )
      
      const opacitySlider = screen.getByDisplayValue('0.5')
      await user.clear(opacitySlider)
      await user.type(opacitySlider, '0.8')
      
      expect(onOpacityChange).toHaveBeenCalledWith(0.8)
    })

    it('calls onBlurChange when blur slider changes', async () => {
      const user = userEvent.setup()
      const onBlurChange = jest.fn()
      
      render(
        <BackgroundUpload 
          {...defaultProps}
          onBlurChange={onBlurChange}
          backgroundBlur={2}
        />
      )
      
      const blurSlider = screen.getByDisplayValue('2')
      await user.clear(blurSlider)
      await user.type(blurSlider, '5')
      
      expect(onBlurChange).toHaveBeenCalledWith(5)
    })
  })

  describe('Accessibility', () => {
    it('has proper ARIA labels', () => {
      render(<BackgroundUpload {...defaultProps} />)
      
      expect(screen.getByLabelText('背景透明度')).toBeInTheDocument()
      expect(screen.getByLabelText('背景模糊度')).toBeInTheDocument()
    })

    it('supports keyboard navigation', async () => {
      const user = userEvent.setup()
      render(<BackgroundUpload {...defaultProps} />)
      
      const uploadButton = screen.getByRole('button', { name: '选择背景图片' })
      
      // Should be focusable
      await user.tab()
      expect(uploadButton).toHaveFocus()
      
      // Should be activatable with Enter
      await user.keyboard('{Enter}')
      // File input should be triggered (though we can't easily test the actual file dialog)
    })
  })
})
