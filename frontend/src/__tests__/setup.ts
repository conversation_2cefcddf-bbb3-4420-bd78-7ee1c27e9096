/**
 * Jest测试设置文件 - 灵境(Mentia)前端
 * 
 * 文件功能：
 *   配置Jest测试环境，包括全局模拟、测试工具设置等
 * 
 * 设置内容：
 *   1. Testing Library配置
 *   2. 全局模拟设置
 *   3. 测试工具扩展
 *   4. 环境变量设置
 * 
 * 作者: Mentia前端团队
 * 创建时间: 2025-09-05
 * 版本: v1.0
 */

import '@testing-library/jest-dom'

// 全局模拟设置
beforeAll(() => {
  // 模拟window.matchMedia
  Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: jest.fn().mockImplementation(query => ({
      matches: false,
      media: query,
      onchange: null,
      addListener: jest.fn(), // deprecated
      removeListener: jest.fn(), // deprecated
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
      dispatchEvent: jest.fn(),
    })),
  })

  // 模拟ResizeObserver
  global.ResizeObserver = jest.fn().mockImplementation(() => ({
    observe: jest.fn(),
    unobserve: jest.fn(),
    disconnect: jest.fn(),
  }))

  // 模拟IntersectionObserver
  global.IntersectionObserver = jest.fn().mockImplementation(() => ({
    observe: jest.fn(),
    unobserve: jest.fn(),
    disconnect: jest.fn(),
  }))

  // 模拟scrollTo
  window.scrollTo = jest.fn()

  // 模拟localStorage
  const localStorageMock = {
    getItem: jest.fn(),
    setItem: jest.fn(),
    removeItem: jest.fn(),
    clear: jest.fn(),
  }
  Object.defineProperty(window, 'localStorage', {
    value: localStorageMock
  })

  // 模拟sessionStorage
  const sessionStorageMock = {
    getItem: jest.fn(),
    setItem: jest.fn(),
    removeItem: jest.fn(),
    clear: jest.fn(),
  }
  Object.defineProperty(window, 'sessionStorage', {
    value: sessionStorageMock
  })

  // 模拟URL.createObjectURL
  global.URL.createObjectURL = jest.fn(() => 'mocked-url')
  global.URL.revokeObjectURL = jest.fn()

  // 模拟FileReader
  global.FileReader = jest.fn().mockImplementation(() => ({
    readAsDataURL: jest.fn(),
    readAsText: jest.fn(),
    onload: null,
    onerror: null,
    result: null,
  }))

  // 模拟Image
  global.Image = jest.fn().mockImplementation(() => ({
    onload: null,
    onerror: null,
    src: '',
    width: 0,
    height: 0,
  }))

  // 模拟fetch（如果需要）
  global.fetch = jest.fn()

  // 模拟console方法（避免测试输出污染）
  const originalConsole = { ...console }
  global.console = {
    ...console,
    // 保留error和warn用于调试
    log: jest.fn(),
    debug: jest.fn(),
    info: jest.fn(),
    // 保留error和warn
    error: originalConsole.error,
    warn: originalConsole.warn,
  }
})

// 每个测试前的清理
beforeEach(() => {
  // 清理所有模拟
  jest.clearAllMocks()
  
  // 重置localStorage
  ;(window.localStorage.getItem as jest.Mock).mockClear()
  ;(window.localStorage.setItem as jest.Mock).mockClear()
  ;(window.localStorage.removeItem as jest.Mock).mockClear()
  ;(window.localStorage.clear as jest.Mock).mockClear()
  
  // 重置sessionStorage
  ;(window.sessionStorage.getItem as jest.Mock).mockClear()
  ;(window.sessionStorage.setItem as jest.Mock).mockClear()
  ;(window.sessionStorage.removeItem as jest.Mock).mockClear()
  ;(window.sessionStorage.clear as jest.Mock).mockClear()
  
  // 重置fetch模拟
  if (global.fetch) {
    ;(global.fetch as jest.Mock).mockClear()
  }
})

// 每个测试后的清理
afterEach(() => {
  // 清理DOM
  document.body.innerHTML = ''
  
  // 清理定时器
  jest.clearAllTimers()
})

// 全局测试工具函数
global.testUtils = {
  // 创建模拟文件
  createMockFile: (name: string, size: number, type: string) => {
    const file = new File([''], name, { type })
    Object.defineProperty(file, 'size', {
      value: size,
      writable: false
    })
    return file
  },

  // 创建模拟图片文件
  createMockImageFile: (name: string = 'test.jpg', size: number = 1024) => {
    return global.testUtils.createMockFile(name, size, 'image/jpeg')
  },

  // 等待异步操作
  waitFor: async (callback: () => void, timeout: number = 1000) => {
    return new Promise((resolve, reject) => {
      const startTime = Date.now()
      const check = () => {
        try {
          callback()
          resolve(undefined)
        } catch (error) {
          if (Date.now() - startTime >= timeout) {
            reject(error)
          } else {
            setTimeout(check, 10)
          }
        }
      }
      check()
    })
  },

  // 模拟用户事件
  mockUserEvent: {
    click: (element: HTMLElement) => {
      element.click()
    },
    type: (element: HTMLInputElement, text: string) => {
      element.value = text
      element.dispatchEvent(new Event('input', { bubbles: true }))
      element.dispatchEvent(new Event('change', { bubbles: true }))
    },
    clear: (element: HTMLInputElement) => {
      element.value = ''
      element.dispatchEvent(new Event('input', { bubbles: true }))
      element.dispatchEvent(new Event('change', { bubbles: true }))
    }
  }
}

// 环境变量设置
process.env.NODE_ENV = 'test'
process.env.NEXT_PUBLIC_API_URL = 'http://localhost:8000/api'

// 错误处理
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason)
})

// 测试超时警告
const originalTimeout = setTimeout
global.setTimeout = (callback: (...args: any[]) => void, delay?: number) => {
  if (delay && delay > 5000) {
    console.warn(`Long timeout detected: ${delay}ms. Consider using jest.setTimeout() or reducing the timeout.`)
  }
  return originalTimeout(callback, delay)
}

// 导出类型声明
declare global {
  namespace jest {
    interface Matchers<R> {
      toBeInTheDocument(): R
      toHaveClass(className: string): R
      toHaveStyle(style: Record<string, any>): R
      toHaveAttribute(attr: string, value?: string): R
      toHaveValue(value: string | number): R
      toBeVisible(): R
      toBeDisabled(): R
      toHaveFocus(): R
    }
  }

  var testUtils: {
    createMockFile: (name: string, size: number, type: string) => File
    createMockImageFile: (name?: string, size?: number) => File
    waitFor: (callback: () => void, timeout?: number) => Promise<void>
    mockUserEvent: {
      click: (element: HTMLElement) => void
      type: (element: HTMLInputElement, text: string) => void
      clear: (element: HTMLInputElement) => void
    }
  }
}
