'use client'

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'

// 颜色工具函数
export const colorUtils = {
  // 将十六进制颜色转换为 HSL
  hexToHsl: (hex: string): [number, number, number] => {
    const r = parseInt(hex.slice(1, 3), 16) / 255
    const g = parseInt(hex.slice(3, 5), 16) / 255
    const b = parseInt(hex.slice(5, 7), 16) / 255

    const max = Math.max(r, g, b)
    const min = Math.min(r, g, b)
    let h = 0, s = 0, l = (max + min) / 2

    if (max !== min) {
      const d = max - min
      s = l > 0.5 ? d / (2 - max - min) : d / (max + min)
      
      switch (max) {
        case r: h = (g - b) / d + (g < b ? 6 : 0); break
        case g: h = (b - r) / d + 2; break
        case b: h = (r - g) / d + 4; break
      }
      h /= 6
    }

    return [h * 360, s * 100, l * 100]
  },

  // 将 HSL 转换为十六进制颜色
  hslToHex: (h: number, s: number, l: number): string => {
    h /= 360
    s /= 100
    l /= 100

    const hue2rgb = (p: number, q: number, t: number) => {
      if (t < 0) t += 1
      if (t > 1) t -= 1
      if (t < 1/6) return p + (q - p) * 6 * t
      if (t < 1/2) return q
      if (t < 2/3) return p + (q - p) * (2/3 - t) * 6
      return p
    }

    let r, g, b
    if (s === 0) {
      r = g = b = l
    } else {
      const q = l < 0.5 ? l * (1 + s) : l + s - l * s
      const p = 2 * l - q
      r = hue2rgb(p, q, h + 1/3)
      g = hue2rgb(p, q, h)
      b = hue2rgb(p, q, h - 1/3)
    }

    const toHex = (c: number) => {
      const hex = Math.round(c * 255).toString(16)
      return hex.length === 1 ? '0' + hex : hex
    }

    return `#${toHex(r)}${toHex(g)}${toHex(b)}`
  },

  // 生成颜色调色板 - 优化对比度和可访问性
  generatePalette: (baseColor: string) => {
    const [h, s, l] = colorUtils.hexToHsl(baseColor)

    return {
      50: colorUtils.hslToHex(h, Math.max(s - 40, 8), Math.min(l + 48, 98)),
      100: colorUtils.hslToHex(h, Math.max(s - 30, 12), Math.min(l + 38, 94)),
      200: colorUtils.hslToHex(h, Math.max(s - 20, 18), Math.min(l + 28, 88)),
      300: colorUtils.hslToHex(h, Math.max(s - 10, 25), Math.min(l + 18, 80)),
      400: colorUtils.hslToHex(h, Math.min(s + 5, 80), Math.min(l + 8, 70)),
      500: baseColor, // 基础颜色
      600: colorUtils.hslToHex(h, Math.min(s + 10, 85), Math.max(l - 12, 35)),
      700: colorUtils.hslToHex(h, Math.min(s + 15, 90), Math.max(l - 22, 25)),
      800: colorUtils.hslToHex(h, Math.min(s + 20, 95), Math.max(l - 32, 15)),
      900: colorUtils.hslToHex(h, Math.min(s + 25, 100), Math.max(l - 42, 8)),
    }
  },

  // 生成语义化颜色
  generateSemanticColors: (baseColor: string) => {
    const [h, s, l] = colorUtils.hexToHsl(baseColor)

    return {
      success: {
        50: colorUtils.hslToHex(142, Math.max(s - 30, 15), 97),
        100: colorUtils.hslToHex(142, Math.max(s - 20, 20), 92),
        500: colorUtils.hslToHex(142, Math.min(s + 10, 85), Math.max(l - 5, 45)),
        600: colorUtils.hslToHex(142, Math.min(s + 15, 90), Math.max(l - 15, 35)),
        700: colorUtils.hslToHex(142, Math.min(s + 20, 95), Math.max(l - 25, 25)),
      },
      warning: {
        50: colorUtils.hslToHex(45, Math.max(s - 25, 20), 97),
        100: colorUtils.hslToHex(45, Math.max(s - 15, 25), 92),
        500: colorUtils.hslToHex(45, Math.min(s + 15, 90), Math.max(l - 10, 50)),
        600: colorUtils.hslToHex(45, Math.min(s + 20, 95), Math.max(l - 20, 40)),
        700: colorUtils.hslToHex(45, Math.min(s + 25, 100), Math.max(l - 30, 30)),
      },
      error: {
        50: colorUtils.hslToHex(0, Math.max(s - 25, 20), 97),
        100: colorUtils.hslToHex(0, Math.max(s - 15, 25), 92),
        500: colorUtils.hslToHex(0, Math.min(s + 20, 95), Math.max(l - 5, 45)),
        600: colorUtils.hslToHex(0, Math.min(s + 25, 100), Math.max(l - 15, 35)),
        700: colorUtils.hslToHex(0, Math.min(s + 30, 100), Math.max(l - 25, 25)),
      },
      info: {
        50: colorUtils.hslToHex(210, Math.max(s - 20, 20), 97),
        100: colorUtils.hslToHex(210, Math.max(s - 10, 25), 92),
        500: colorUtils.hslToHex(210, Math.min(s + 5, 80), Math.max(l, 50)),
        600: colorUtils.hslToHex(210, Math.min(s + 10, 85), Math.max(l - 10, 40)),
        700: colorUtils.hslToHex(210, Math.min(s + 15, 90), Math.max(l - 20, 30)),
      }
    }
  },

  // 生成中性色调色板
  generateNeutralPalette: (baseColor: string) => {
    const [h, s, l] = colorUtils.hexToHsl(baseColor)
    const neutralHue = h
    const neutralSat = Math.min(s * 0.15, 10) // 极低饱和度，但保持一点色彩倾向

    return {
      50: colorUtils.hslToHex(neutralHue, neutralSat, 98),
      100: colorUtils.hslToHex(neutralHue, neutralSat, 96),
      200: colorUtils.hslToHex(neutralHue, neutralSat, 90),
      300: colorUtils.hslToHex(neutralHue, neutralSat, 83),
      400: colorUtils.hslToHex(neutralHue, neutralSat, 64),
      500: colorUtils.hslToHex(neutralHue, neutralSat, 45),
      600: colorUtils.hslToHex(neutralHue, neutralSat, 32),
      700: colorUtils.hslToHex(neutralHue, neutralSat, 25),
      800: colorUtils.hslToHex(neutralHue, neutralSat, 15),
      900: colorUtils.hslToHex(neutralHue, neutralSat, 9),
    }
  },

  // 判断颜色是否为深色
  isDark: (hex: string): boolean => {
    const [, , l] = colorUtils.hexToHsl(hex)
    return l < 50
  },

  // 获取对比色（用于文字）
  getContrastColor: (hex: string): string => {
    return colorUtils.isDark(hex) ? '#ffffff' : '#000000'
  },

  // 计算颜色对比度
  getContrastRatio: (color1: string, color2: string): number => {
    const getLuminance = (hex: string): number => {
      const [r, g, b] = [
        parseInt(hex.slice(1, 3), 16) / 255,
        parseInt(hex.slice(3, 5), 16) / 255,
        parseInt(hex.slice(5, 7), 16) / 255,
      ].map(c => c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4))

      return 0.2126 * r + 0.7152 * g + 0.0722 * b
    }

    const lum1 = getLuminance(color1)
    const lum2 = getLuminance(color2)
    const brightest = Math.max(lum1, lum2)
    const darkest = Math.min(lum1, lum2)

    return (brightest + 0.05) / (darkest + 0.05)
  }
}

// 主题类型定义
export interface ThemeColors {
  primary: Record<string, string>
  secondary: Record<string, string>
  neutral: Record<string, string>
  semantic: {
    success: Record<string, string>
    warning: Record<string, string>
    error: Record<string, string>
    info: Record<string, string>
  }
  background: string
  surface: string
  surfaceElevated: string
  border: string
  borderLight: string
  text: {
    primary: string
    secondary: string
    muted: string
    inverse: string
  }
  shadow: {
    sm: string
    md: string
    lg: string
    xl: string
  }
}

export interface ThemeContextType {
  baseColor: string
  setBaseColor: (color: string) => void
  colors: ThemeColors
  isDarkMode: boolean
  toggleDarkMode: () => void
}

// 默认主题
const defaultBaseColor = '#14b8a6' // teal-500

const generateThemeColors = (baseColor: string, isDarkMode: boolean = false): ThemeColors => {
  const primaryPalette = colorUtils.generatePalette(baseColor)
  const [h, s, l] = colorUtils.hexToHsl(baseColor)

  // 生成互补色作为辅助色，调整饱和度和亮度
  const complementaryHue = (h + 180) % 360
  const secondaryColor = colorUtils.hslToHex(complementaryHue, Math.min(s * 0.85, 65), Math.max(l, 50))
  const secondaryPalette = colorUtils.generatePalette(secondaryColor)

  // 生成中性色调色板
  const neutralPalette = colorUtils.generateNeutralPalette(baseColor)

  // 生成语义化颜色
  const semanticColors = colorUtils.generateSemanticColors(baseColor)

  // 使用传入的暗色模式参数
  const shouldUseDarkTheme = isDarkMode

  return {
    primary: primaryPalette,
    secondary: secondaryPalette,
    neutral: neutralPalette,
    semantic: semanticColors,
    background: shouldUseDarkTheme ? neutralPalette[900] : primaryPalette[50],
    surface: shouldUseDarkTheme ? 'rgba(30, 30, 30, 0.85)' : 'rgba(255, 255, 255, 0.85)',
    surfaceElevated: shouldUseDarkTheme ? 'rgba(40, 40, 40, 0.95)' : 'rgba(255, 255, 255, 0.95)',
    border: shouldUseDarkTheme ? neutralPalette[700] : primaryPalette[200],
    borderLight: shouldUseDarkTheme ? neutralPalette[800] : primaryPalette[100],
    text: {
      primary: shouldUseDarkTheme ? neutralPalette[50] : neutralPalette[900],
      secondary: shouldUseDarkTheme ? neutralPalette[300] : neutralPalette[600],
      muted: shouldUseDarkTheme ? neutralPalette[400] : neutralPalette[500],
      inverse: shouldUseDarkTheme ? neutralPalette[900] : neutralPalette[50]
    },
    shadow: {
      sm: shouldUseDarkTheme
        ? `0 1px 2px 0 rgba(0, 0, 0, 0.3)`
        : `0 1px 2px 0 ${primaryPalette[500]}20`, // 使用主题色阴影
      md: shouldUseDarkTheme
        ? `0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.3)`
        : `0 4px 6px -1px ${primaryPalette[500]}15, 0 2px 4px -1px ${primaryPalette[500]}10`,
      lg: shouldUseDarkTheme
        ? `0 10px 15px -3px rgba(0, 0, 0, 0.5), 0 4px 6px -2px rgba(0, 0, 0, 0.4)`
        : `0 10px 15px -3px ${primaryPalette[500]}20, 0 4px 6px -2px ${primaryPalette[500]}10`,
      xl: shouldUseDarkTheme
        ? `0 20px 25px -5px rgba(0, 0, 0, 0.6), 0 10px 10px -5px rgba(0, 0, 0, 0.5)`
        : `0 20px 25px -5px ${primaryPalette[500]}25, 0 10px 10px -5px ${primaryPalette[500]}15`
    }
  }
}

// 创建上下文
const ThemeContext = createContext<ThemeContextType | undefined>(undefined)

// 主题提供者组件
export function ThemeProvider({ children }: { children: ReactNode }) {
  const [baseColor, setBaseColor] = useState(defaultBaseColor)
  const [colors, setColors] = useState<ThemeColors>(generateThemeColors(defaultBaseColor))
  const [isDarkMode, setIsDarkMode] = useState(false)
  const [isLoaded, setIsLoaded] = useState(false)

  // 从后端和 localStorage 加载保存的主题
  useEffect(() => {
    const loadThemeSettings = async () => {
      try {
        // 首先尝试从localStorage加载（快速显示）
        const savedColor = localStorage.getItem('theme-base-color')
        const savedDarkMode = localStorage.getItem('theme-dark-mode')
        if (savedColor) {
          setBaseColor(savedColor)
        }
        if (savedDarkMode) {
          setIsDarkMode(savedDarkMode === 'true')
        }

        // 然后从后端加载用户设置（如果用户已登录）
        const { tokenManager } = await import('@/lib/api')
        if (tokenManager.isAuthenticated()) {
          const { get } = await import('@/lib/api')
          try {
            const userSettings = await get('/auth/settings/')

            // 将后端的primary_color映射到hex颜色
            const colorMap: Record<string, string> = {
              'teal': '#14b8a6',
              'blue': '#3b82f6',
              'violet': '#8b5cf6',
              'pink': '#ec4899',
              'red': '#ef4444',
              'orange': '#f97316',
              'yellow': '#eab308',
              'green': '#22c55e',
              'indigo': '#6366f1',
              'rose': '#f43f5e'
            }

            const backendColor = colorMap[userSettings.primary_color] || userSettings.primary_color
            if (backendColor && backendColor !== savedColor) {
              setBaseColor(backendColor)
              localStorage.setItem('theme-base-color', backendColor)
            }
          } catch (error) {
            console.log('Failed to load user theme settings:', error)
          }
        }
      } catch (error) {
        console.log('Failed to load theme settings:', error)
      } finally {
        setIsLoaded(true)
      }
    }

    loadThemeSettings()
  }, [])

  // 切换暗色模式
  const toggleDarkMode = () => {
    const newDarkMode = !isDarkMode
    setIsDarkMode(newDarkMode)
    localStorage.setItem('theme-dark-mode', newDarkMode.toString())
  }

  // 当基础颜色或暗色模式改变时更新主题
  useEffect(() => {
    const newColors = generateThemeColors(baseColor, isDarkMode)
    setColors(newColors)
    localStorage.setItem('theme-base-color', baseColor)

    // 更新 CSS 变量
    const root = document.documentElement

    // 辅助函数：将hex颜色转换为RGB值
    const hexToRgb = (hex: string): string => {
      const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
      if (!result) return '0, 0, 0'
      return `${parseInt(result[1], 16)}, ${parseInt(result[2], 16)}, ${parseInt(result[3], 16)}`
    }

    // 主色调及其RGB值
    Object.entries(newColors.primary).forEach(([key, value]) => {
      root.style.setProperty(`--color-primary-${key}`, value)
      root.style.setProperty(`--color-primary-${key}-rgb`, hexToRgb(value))
    })

    // 辅助色及其RGB值
    Object.entries(newColors.secondary).forEach(([key, value]) => {
      root.style.setProperty(`--color-secondary-${key}`, value)
      root.style.setProperty(`--color-secondary-${key}-rgb`, hexToRgb(value))
    })

    // 中性色及其RGB值
    Object.entries(newColors.neutral).forEach(([key, value]) => {
      root.style.setProperty(`--color-neutral-${key}`, value)
      root.style.setProperty(`--color-neutral-${key}-rgb`, hexToRgb(value))
    })

    // 语义化颜色
    Object.entries(newColors.semantic.success).forEach(([key, value]) => {
      root.style.setProperty(`--color-success-${key}`, value)
    })
    Object.entries(newColors.semantic.warning).forEach(([key, value]) => {
      root.style.setProperty(`--color-warning-${key}`, value)
    })
    Object.entries(newColors.semantic.error).forEach(([key, value]) => {
      root.style.setProperty(`--color-error-${key}`, value)
    })
    Object.entries(newColors.semantic.info).forEach(([key, value]) => {
      root.style.setProperty(`--color-info-${key}`, value)
    })

    // 基础颜色
    root.style.setProperty('--color-background', newColors.background)
    root.style.setProperty('--color-surface', newColors.surface)
    root.style.setProperty('--color-surface-elevated', newColors.surfaceElevated)
    root.style.setProperty('--color-border', newColors.border)
    root.style.setProperty('--color-border-light', newColors.borderLight)

    // 文字颜色
    root.style.setProperty('--color-text-primary', newColors.text.primary)
    root.style.setProperty('--color-text-secondary', newColors.text.secondary)
    root.style.setProperty('--color-text-muted', newColors.text.muted)
    root.style.setProperty('--color-text-inverse', newColors.text.inverse)

    // 阴影
    root.style.setProperty('--shadow-sm', newColors.shadow.sm)
    root.style.setProperty('--shadow-md', newColors.shadow.md)
    root.style.setProperty('--shadow-lg', newColors.shadow.lg)
    root.style.setProperty('--shadow-xl', newColors.shadow.xl)

    // 设置暗色模式类
    if (isDarkMode) {
      document.documentElement.classList.add('dark')
    } else {
      document.documentElement.classList.remove('dark')
    }
  }, [baseColor, isDarkMode])

  // 自定义的setBaseColor函数，同时更新后端
  const updateBaseColor = async (color: string) => {
    setBaseColor(color)

    // 同步到后端
    try {
      const { tokenManager } = await import('@/lib/api')
      if (tokenManager.isAuthenticated()) {
        const { patch } = await import('@/lib/api')

        // 将hex颜色映射回后端的颜色名称
        const reverseColorMap: Record<string, string> = {
          '#14b8a6': 'teal',
          '#3b82f6': 'blue',
          '#8b5cf6': 'violet',
          '#ec4899': 'pink',
          '#ef4444': 'red',
          '#f97316': 'orange',
          '#eab308': 'yellow',
          '#22c55e': 'green',
          '#6366f1': 'indigo',
          '#f43f5e': 'rose'
        }

        const backendColorName = reverseColorMap[color] || color
        await patch('/auth/settings/', {
          primary_color: backendColorName
        })
      }
    } catch (error) {
      console.log('Failed to sync theme color to backend:', error)
    }
  }

  return (
    <ThemeContext.Provider value={{ baseColor, setBaseColor: updateBaseColor, colors, isDarkMode, toggleDarkMode }}>
      {children}
    </ThemeContext.Provider>
  )
}

// 使用主题的 Hook
export function useTheme() {
  const context = useContext(ThemeContext)
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider')
  }
  return context
}
