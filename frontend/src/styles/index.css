/**
 * 主样式索引文件
 *
 * 文件功能：
 *   统一导入所有样式模块，替代原有的globals.css
 *
 * 导入顺序（重要）：
 *   1. 字体导入
 *   2. Tailwind基础层
 *   3. 基础样式（foundation）
 *   4. 组件样式（components）
 *   5. 工具类（utilities）
 *   6. 主题样式（themes）
 *
 * 作者: Mentia前端团队
 * 创建时间: 2025-09-11
 * 版本: v1.0 - CSS模块化重构
 */

/* 模块化CSS导入 - 不包含Tailwind和字体，避免重复导入 */

/* 组件使用指南注释 */
/**
 * 🎨 Mentia 前端组件使用指南
 *
 * 基本原则：
 * - ✅ 优先使用CSS类：.btn, .card, .input, .modal
 * - ⚠️  React组件仅用于复杂交互：表单验证、状态管理、动画效果
 *
 * 推荐使用方式：
 * <div className="card">                    // ✅ 推荐 - 简单高效
 * <button className="btn btn-primary">     // ✅ 推荐 - 性能更好
 * <input className="input" />              // ✅ 推荐 - 维护简单
 *
 * 避免使用方式：
 * <CardContainer>                          // ❌ 已删除 - 重复实现
 * <ButtonContainer>                        // ❌ 已删除 - 重复实现
 * <InputContainer>                         // ❌ 已删除 - 重复实现
 *
 * React组件适用场景：
 * <Button loading={true} onClick={submit}> // ✅ 复杂交互时使用
 * <Input error={errors.email} />          // ✅ 表单验证时使用
 * <Toast message="成功" />                 // ✅ 全局功能时使用
 */

/* 基础样式模块 */
@import './foundation/index.css';

/* 组件样式模块 */
@import './components/index.css';

/* 工具类模块 */
@import './utilities/index.css';

/* 主题样式模块 */
/* TODO: 创建themes模块后启用
@import './themes/index.css';
*/
