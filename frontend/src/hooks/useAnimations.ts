'use client'

import { useEffect, useRef, useState } from 'react'

// 交集观察器Hook，用于触发进入视口的动画
export function useIntersectionObserver(
  options: IntersectionObserverInit = {}
) {
  const [isIntersecting, setIsIntersecting] = useState(false)
  const [hasIntersected, setHasIntersected] = useState(false)
  const ref = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const element = ref.current
    if (!element) return

    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsIntersecting(entry.isIntersecting)
        if (entry.isIntersecting && !hasIntersected) {
          setHasIntersected(true)
        }
      },
      {
        threshold: 0.1,
        rootMargin: '50px',
        ...options,
      }
    )

    observer.observe(element)

    return () => {
      observer.unobserve(element)
    }
  }, [hasIntersected, options])

  return { ref, isIntersecting, hasIntersected }
}

// 延迟动画Hook
export function useStaggeredAnimation(
  itemCount: number,
  delay: number = 100
) {
  const [visibleItems, setVisibleItems] = useState<Set<number>>(new Set())
  const { ref, hasIntersected } = useIntersectionObserver()

  useEffect(() => {
    if (!hasIntersected) return

    const timeouts: NodeJS.Timeout[] = []

    for (let i = 0; i < itemCount; i++) {
      const timeout = setTimeout(() => {
        setVisibleItems(prev => new Set(Array.from(prev).concat(i)))
      }, i * delay)
      timeouts.push(timeout)
    }

    return () => {
      timeouts.forEach(clearTimeout)
    }
  }, [hasIntersected, itemCount, delay])

  return { ref, visibleItems }
}

// 鼠标跟踪Hook
export function useMousePosition() {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 })

  useEffect(() => {
    const updateMousePosition = (e: MouseEvent) => {
      setMousePosition({ x: e.clientX, y: e.clientY })
    }

    window.addEventListener('mousemove', updateMousePosition)

    return () => {
      window.removeEventListener('mousemove', updateMousePosition)
    }
  }, [])

  return mousePosition
}

// 视差滚动Hook
export function useParallax(speed: number = 0.5) {
  const [offset, setOffset] = useState(0)
  const ref = useRef<HTMLElement>(null)

  useEffect(() => {
    const handleScroll = () => {
      if (!ref.current) return
      
      const rect = ref.current.getBoundingClientRect()
      const scrolled = window.pageYOffset
      const rate = scrolled * -speed
      
      setOffset(rate)
    }

    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [speed])

  return { ref, offset }
}

// 悬停动画Hook
export function useHoverAnimation() {
  const [isHovered, setIsHovered] = useState(false)
  const ref = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const element = ref.current
    if (!element) return

    const handleMouseEnter = () => setIsHovered(true)
    const handleMouseLeave = () => setIsHovered(false)

    element.addEventListener('mouseenter', handleMouseEnter)
    element.addEventListener('mouseleave', handleMouseLeave)

    return () => {
      element.removeEventListener('mouseenter', handleMouseEnter)
      element.removeEventListener('mouseleave', handleMouseLeave)
    }
  }, [])

  return { ref, isHovered }
}

// 打字机效果Hook
export function useTypewriter(
  text: string,
  speed: number = 50,
  startDelay: number = 0
) {
  const [displayText, setDisplayText] = useState('')
  const [isComplete, setIsComplete] = useState(false)

  useEffect(() => {
    if (!text) return

    setDisplayText('')
    setIsComplete(false)

    const startTimeout = setTimeout(() => {
      let index = 0
      const timer = setInterval(() => {
        if (index < text.length) {
          setDisplayText(text.slice(0, index + 1))
          index++
        } else {
          setIsComplete(true)
          clearInterval(timer)
        }
      }, speed)

      return () => clearInterval(timer)
    }, startDelay)

    return () => clearTimeout(startTimeout)
  }, [text, speed, startDelay])

  return { displayText, isComplete }
}

// 数字计数动画Hook
export function useCountUp(
  end: number,
  duration: number = 2000,
  start: number = 0,
  decimals: number = 0
) {
  const [count, setCount] = useState(start)
  const [isAnimating, setIsAnimating] = useState(false)
  const { ref, hasIntersected } = useIntersectionObserver()

  useEffect(() => {
    if (!hasIntersected || isAnimating) return

    setIsAnimating(true)
    const startTime = Date.now()
    const startValue = start
    const endValue = end

    const animate = () => {
      const now = Date.now()
      const elapsed = now - startTime
      const progress = Math.min(elapsed / duration, 1)

      // 使用缓动函数
      const easeOutQuart = 1 - Math.pow(1 - progress, 4)
      const currentValue = startValue + (endValue - startValue) * easeOutQuart

      setCount(Number(currentValue.toFixed(decimals)))

      if (progress < 1) {
        requestAnimationFrame(animate)
      } else {
        setIsAnimating(false)
      }
    }

    requestAnimationFrame(animate)
  }, [hasIntersected, end, duration, start, decimals, isAnimating])

  return { ref, count, isAnimating }
}

// 弹性动画Hook
export function useSpringAnimation(
  trigger: boolean,
  config: {
    tension?: number
    friction?: number
    mass?: number
  } = {}
) {
  const [value, setValue] = useState(0)
  const { tension = 120, friction = 14, mass = 1 } = config

  useEffect(() => {
    if (!trigger) return

    let animationId: number
    let velocity = 0
    let position = 0
    const target = 1

    const animate = () => {
      const spring = -tension * (position - target)
      const damper = -friction * velocity
      const acceleration = (spring + damper) / mass

      velocity += acceleration * 0.016 // 60fps
      position += velocity * 0.016

      setValue(position)

      if (Math.abs(velocity) > 0.01 || Math.abs(position - target) > 0.01) {
        animationId = requestAnimationFrame(animate)
      }
    }

    animationId = requestAnimationFrame(animate)

    return () => {
      if (animationId) {
        cancelAnimationFrame(animationId)
      }
    }
  }, [trigger, tension, friction, mass])

  return value
}

// 滚动触发动画Hook
export function useScrollTrigger(threshold: number = 100) {
  const [isTriggered, setIsTriggered] = useState(false)

  useEffect(() => {
    const handleScroll = () => {
      const scrollY = window.scrollY
      setIsTriggered(scrollY > threshold)
    }

    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [threshold])

  return isTriggered
}

// 页面加载动画Hook
export function usePageTransition() {
  const [isLoading, setIsLoading] = useState(true)
  const [isExiting, setIsExiting] = useState(false)

  useEffect(() => {
    // 页面加载完成后延迟一点时间再显示内容
    const timer = setTimeout(() => {
      setIsLoading(false)
    }, 300)

    return () => clearTimeout(timer)
  }, [])

  const startExit = () => {
    setIsExiting(true)
  }

  return { isLoading, isExiting, startExit }
}
