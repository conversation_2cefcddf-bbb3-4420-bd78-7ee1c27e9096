/**
 * Next.js配置文件 - 灵境(Mentia)前端构建和运行时配置
 *
 * 文件功能：
 *   定义Next.js应用的构建配置、性能优化、API代理等核心设置
 *
 * 主要配置项：
 *   1. 构建配置 - 自定义构建目录和环境变量
 *   2. 性能优化 - 包导入优化、代码分割、图片优化
 *   3. API代理 - 开发环境下的后端API代理配置
 *   4. Webpack配置 - 自定义打包和优化策略
 *
 * 环境变量：
 *   - NEXT_PUBLIC_API_URL: 后端API地址
 *   - NEXT_PUBLIC_APP_NAME: 应用名称
 *   - BACKEND_PORT: 后端服务端口（用于API代理）
 *   - BACKEND_HOST: 后端服务主机（用于API代理）
 *
 * 性能优化策略：
 *   - 启用gzip压缩
 *   - 优化图片格式（WebP、AVIF）
 *   - 代码分割和vendor chunk分离
 *   - 包导入优化（特别是图标库）
 *
 * 作者: Mentia前端团队
 * 创建时间: 2024
 * 最后更新: 2025-09-05
 * 版本: v2.0 - 支持动态端口配置
 *
 * @type {import('next').NextConfig}
 */
const nextConfig = {
  distDir: '.next-build',
  env: {
    NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api',
    NEXT_PUBLIC_APP_NAME: process.env.NEXT_PUBLIC_APP_NAME || '灵境 Mentia',
  },
  // 开发环境性能优化
  ...(process.env.NODE_ENV === 'development' && {
    // 禁用源码映射以提升构建速度
    productionBrowserSourceMaps: false,
    // 优化开发服务器
    onDemandEntries: {
      // 页面在内存中保持的时间（毫秒）
      maxInactiveAge: 60 * 1000, // 增加到60秒
      // 同时保持的页面数
      pagesBufferLength: 5, // 增加缓存页面数
    },
    // 启用SWC压缩以提升性能
    swcMinify: true,
    // Turbopack配置移除，通过命令行启用
  }),
  // 现代化配置 - 启用性能优化功能
  experimental: {
    // 包导入优化 - 减少bundle大小
    optimizePackageImports: [
      '@heroicons/react',
      '@headlessui/react',
      'react-hook-form',
      'zod',
      'date-fns'
    ],
    // 服务端组件优化
    serverComponentsExternalPackages: [],
    // 启用并行构建
    parallelServerBuildTraces: true,
  },
  // 编译器优化 (Turbopack不支持，暂时禁用)
  // compiler: {
  //   // 生产环境移除console.log
  //   removeConsole: process.env.NODE_ENV === 'production',
  // },
  // 压缩配置
  compress: true,
  // 图片优化
  images: {
    formats: ['image/webp', 'image/avif'],
    minimumCacheTTL: 60,
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
  },
  // 输出配置
  output: 'standalone',
  // 缓存处理器 (暂时禁用，存在构造函数问题)
  // cacheHandler: require.resolve('./cache-handler.js'),
  // 缓存配置
  onDemandEntries: {
    // 页面在内存中保持的时间（毫秒）
    maxInactiveAge: 25 * 1000,
    // 同时保持的页面数
    pagesBufferLength: 5,
  },
  // 代码分割优化
  webpack: (config, { dev, isServer, webpack }) => {
    // 生产环境优化
    if (!dev && !isServer) {
      config.optimization.splitChunks = {
        chunks: 'all',
        minSize: 20000,
        maxSize: 244000,
        cacheGroups: {
          default: {
            minChunks: 2,
            priority: -20,
            reuseExistingChunk: true,
          },
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendors',
            priority: -10,
            chunks: 'all',
          },
          react: {
            test: /[\\/]node_modules[\\/](react|react-dom)[\\/]/,
            name: 'react',
            priority: 10,
            chunks: 'all',
          },
          ui: {
            test: /[\\/]node_modules[\\/](@headlessui|@heroicons)[\\/]/,
            name: 'ui',
            priority: 5,
            chunks: 'all',
          },
        },
      }
    }

    // 开发环境优化（简化版本以避免运行时错误）
    if (dev) {
      // 基础缓存配置
      const path = require('path')
      config.cache = {
        type: 'filesystem',
        cacheDirectory: path.resolve(process.cwd(), '.next/cache/webpack'),
      }

      // 基础优化
      config.resolve.symlinks = false
      config.stats = 'errors-warnings'

      // 简化的开发优化
      config.optimization = {
        ...config.optimization,
        removeAvailableModules: false,
        removeEmptyChunks: false,
        splitChunks: false,
      }

      // 减少文件系统调用
      config.snapshot = {
        managedPaths: [/^(.+?[\\/]node_modules[\\/])/],
        immutablePaths: [],
        buildDependencies: {
          hash: true,
          timestamp: true,
        },
        module: {
          timestamp: true,
        },
        resolve: {
          timestamp: true,
        },
        resolveBuildDependencies: {
          hash: true,
          timestamp: true,
        },
      }
    }

    return config
  },
  async rewrites() {
    // 支持动态后端端口配置
    const backendPort = process.env.BACKEND_PORT || '8000'
    const backendHost = process.env.BACKEND_HOST || 'localhost'
    const backendUrl = `http://${backendHost}:${backendPort}`

    console.log(`[Next.js] API rewrites configured: /api/* -> ${backendUrl}/api/*`)

    return [
      // 处理API路径，代理到后端API
      {
        source: '/api/:path*',
        destination: `${backendUrl}/api/:path*`,
      },
    ]
  },
}

module.exports = nextConfig
