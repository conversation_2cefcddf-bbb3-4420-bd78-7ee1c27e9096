{"compilerOptions": {"target": "ES2022", "lib": ["dom", "dom.iterable", "ES2022"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "tsBuildInfoFile": ".next/cache/tsconfig.tsbuildinfo", "plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/components/*": ["./src/components/*"], "@/lib/*": ["./src/lib/*"], "@/types/*": ["./src/types/*"], "@/app/*": ["./src/app/*"]}, "assumeChangesOnlyAffectDirectDependencies": true, "disableSourceOfProjectReferenceRedirect": true}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts", ".next-build/types/**/*.ts"], "exclude": ["node_modules", ".next", ".next-build", "out", "dist", "**/*.test.ts", "**/*.test.tsx"], "ts-node": {"esm": true}}