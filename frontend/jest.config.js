/**
 * Jest测试配置 - 灵境(Mentia)前端
 * 
 * 文件功能：
 *   配置Jest测试环境，包括模块解析、测试环境设置等
 * 
 * 配置说明：
 *   - 支持TypeScript和JSX
 *   - 配置路径别名
 *   - 设置测试环境
 *   - 配置覆盖率报告
 * 
 * 作者: Mentia前端团队
 * 创建时间: 2025-09-05
 * 版本: v1.0
 */

const nextJest = require('next/jest')

const createJestConfig = nextJest({
  // Provide the path to your Next.js app to load next.config.js and .env files
  dir: './',
})

// Add any custom config to be passed to Jest
const customJestConfig = {
  // 设置测试环境
  testEnvironment: 'jsdom',
  
  // 测试文件匹配模式 - 更新为统一测试目录
  testMatch: [
    '<rootDir>/../test/frontend/**/*.{js,jsx,ts,tsx}',
    '<rootDir>/src/**/*.{test,spec}.{js,jsx,ts,tsx}'
  ],
  
  // 模块路径映射（与tsconfig.json保持一致）
  moduleNameMapper: {
    // 路径映射
    '^@/(.*)$': '<rootDir>/src/$1',
    '^@/components/(.*)$': '<rootDir>/src/components/$1',
    '^@/lib/(.*)$': '<rootDir>/src/lib/$1',
    '^@/types/(.*)$': '<rootDir>/src/types/$1',
    '^@/hooks/(.*)$': '<rootDir>/src/hooks/$1',
    '^@/utils/(.*)$': '<rootDir>/src/utils/$1',

    // 处理CSS模块
    '\\.(css|less|scss|sass)$': 'identity-obj-proxy',

    // 处理图片和其他静态资源
    '\\.(jpg|jpeg|png|gif|eot|otf|webp|svg|ttf|woff|woff2|mp4|webm|wav|mp3|m4a|aac|oga)$': '<rootDir>/../test/frontend/__mocks__/fileMock.js'
  },
  
  // 设置文件 - 更新为统一测试目录
  setupFilesAfterEnv: ['<rootDir>/src/__tests__/setup.ts'],
  
  // 模块文件扩展名
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json'],
  
  // 转换配置
  transform: {
    '^.+\\.(ts|tsx)$': ['ts-jest', {
      tsconfig: 'tsconfig.json'
    }]
  },
  
  // 忽略转换的模块
  transformIgnorePatterns: [
    'node_modules/(?!(.*\\.mjs$|@heroicons/react))'
  ],
  

  
  // 覆盖率配置
  collectCoverageFrom: [
    'src/**/*.{ts,tsx}',
    '!src/**/*.d.ts',
    '!src/**/*.stories.{ts,tsx}',
    '!src/**/__tests__/**',
    '!src/**/__mocks__/**',
    '!../test/**',
    '!src/types/**',
    '!src/app/layout.tsx',
    '!src/app/globals.css'
  ],
  
  // 覆盖率阈值
  coverageThreshold: {
    global: {
      branches: 70,
      functions: 70,
      lines: 70,
      statements: 70
    }
  },
  
  // 覆盖率报告格式
  coverageReporters: [
    'text',
    'lcov',
    'html',
    'json-summary'
  ],
  
  // 覆盖率输出目录
  coverageDirectory: 'coverage',
  
  // 测试超时时间（毫秒）
  testTimeout: 10000,
  
  // 全局变量
  globals: {
    'ts-jest': {
      tsconfig: 'tsconfig.json'
    }
  },
  
  // 清理模拟
  clearMocks: true,
  restoreMocks: true,
  
  // 详细输出
  verbose: true,
  
  // 错误时显示完整的差异
  expand: true,
  
  // 监视模式下的通知
  notify: true,
  notifyMode: 'failure-change',
  
  // 测试结果处理器
  reporters: [
    'default',
    ['jest-junit', {
      outputDirectory: 'test-results',
      outputName: 'junit.xml'
    }]
  ],
  
  // 快照序列化器
  snapshotSerializers: [
    '@emotion/jest/serializer'
  ]
}

// 创建Jest配置
module.exports = createJestConfig(customJestConfig)
