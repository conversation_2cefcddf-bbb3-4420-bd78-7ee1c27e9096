#!/usr/bin/env node

/**
 * 前端性能分析脚本
 * 
 * 功能：
 * - 分析编译时间
 * - 检查依赖大小
 * - 识别性能瓶颈
 * - 提供优化建议
 */

const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')

console.log('🔍 开始前端性能分析...\n')

// 1. 分析package.json依赖
function analyzeDependencies() {
  console.log('📦 分析依赖包大小...')
  
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'))
  const deps = Object.keys(packageJson.dependencies || {})
  const devDeps = Object.keys(packageJson.devDependencies || {})
  
  console.log(`  - 生产依赖: ${deps.length} 个`)
  console.log(`  - 开发依赖: ${devDeps.length} 个`)
  
  // 检查大型依赖
  const heavyDeps = [
    '@testing-library/react',
    '@testing-library/jest-dom', 
    '@testing-library/user-event',
    'jest',
    'ts-jest',
    'prettier'
  ]
  
  const foundHeavyDeps = heavyDeps.filter(dep => 
    deps.includes(dep) || devDeps.includes(dep)
  )
  
  if (foundHeavyDeps.length > 0) {
    console.log(`  ⚠️  发现重型依赖: ${foundHeavyDeps.join(', ')}`)
  }
  
  console.log()
}

// 2. 分析TypeScript配置
function analyzeTypeScriptConfig() {
  console.log('📝 分析TypeScript配置...')
  
  if (fs.existsSync('tsconfig.json')) {
    const tsConfig = JSON.parse(fs.readFileSync('tsconfig.json', 'utf8'))
    const compilerOptions = tsConfig.compilerOptions || {}
    
    console.log(`  - Target: ${compilerOptions.target || 'ES5'}`)
    console.log(`  - 增量编译: ${compilerOptions.incremental ? '✅' : '❌'}`)
    console.log(`  - 跳过库检查: ${compilerOptions.skipLibCheck ? '✅' : '❌'}`)
    
    if (!compilerOptions.incremental) {
      console.log('  💡 建议启用增量编译以提升性能')
    }
  }
  
  console.log()
}

// 3. 分析Next.js配置
function analyzeNextConfig() {
  console.log('⚡ 分析Next.js配置...')
  
  if (fs.existsSync('next.config.js')) {
    const configContent = fs.readFileSync('next.config.js', 'utf8')
    
    const hasSwcMinify = configContent.includes('swcMinify')
    const hasTurbo = configContent.includes('turbo')
    const hasOptimizePackageImports = configContent.includes('optimizePackageImports')
    
    console.log(`  - SWC压缩: ${hasSwcMinify ? '✅' : '❌'}`)
    console.log(`  - Turbo模式: ${hasTurbo ? '✅' : '❌'}`)
    console.log(`  - 包导入优化: ${hasOptimizePackageImports ? '✅' : '❌'}`)
    
    if (!hasSwcMinify) {
      console.log('  💡 建议启用SWC压缩')
    }
    if (!hasTurbo) {
      console.log('  💡 建议启用Turbo模式')
    }
  }
  
  console.log()
}

// 4. 分析文件结构
function analyzeFileStructure() {
  console.log('📁 分析文件结构...')
  
  const srcPath = path.join(process.cwd(), 'src')
  if (fs.existsSync(srcPath)) {
    const getFileCount = (dir) => {
      let count = 0
      const files = fs.readdirSync(dir)
      
      for (const file of files) {
        const filePath = path.join(dir, file)
        const stat = fs.statSync(filePath)
        
        if (stat.isDirectory()) {
          count += getFileCount(filePath)
        } else if (file.endsWith('.tsx') || file.endsWith('.ts')) {
          count++
        }
      }
      
      return count
    }
    
    const totalFiles = getFileCount(srcPath)
    console.log(`  - TypeScript/React文件总数: ${totalFiles}`)
    
    if (totalFiles > 100) {
      console.log('  ⚠️  文件数量较多，可能影响编译性能')
      console.log('  💡 建议考虑代码分割和懒加载')
    }
  }
  
  console.log()
}

// 5. 提供优化建议
function provideOptimizationSuggestions() {
  console.log('💡 性能优化建议:')
  console.log('  1. 启用Turbopack: npm run dev (已配置)')
  console.log('  2. 使用增量编译: tsconfig.json中启用incremental')
  console.log('  3. 优化依赖导入: 使用tree-shaking友好的导入方式')
  console.log('  4. 启用缓存: ESLint和TypeScript缓存')
  console.log('  5. 减少重型依赖: 考虑移除不必要的开发依赖')
  console.log('  6. 使用SWC: 替代Babel进行更快的编译')
  console.log()
}

// 6. 运行性能测试
function runPerformanceTest() {
  console.log('🚀 运行编译性能测试...')
  
  try {
    const startTime = Date.now()
    
    // 清理缓存
    console.log('  - 清理缓存...')
    execSync('npm run clean', { stdio: 'pipe' })
    
    // 运行类型检查
    console.log('  - 运行类型检查...')
    const typeCheckStart = Date.now()
    execSync('npm run type-check', { stdio: 'pipe' })
    const typeCheckTime = Date.now() - typeCheckStart
    
    console.log(`  ✅ 类型检查完成: ${typeCheckTime}ms`)
    
    const totalTime = Date.now() - startTime
    console.log(`  🏁 总耗时: ${totalTime}ms`)
    
    if (totalTime > 10000) {
      console.log('  ⚠️  编译时间较长，建议进行优化')
    } else {
      console.log('  ✅ 编译性能良好')
    }
    
  } catch (error) {
    console.log('  ❌ 性能测试失败:', error.message)
  }
  
  console.log()
}

// 主函数
function main() {
  analyzeDependencies()
  analyzeTypeScriptConfig()
  analyzeNextConfig()
  analyzeFileStructure()
  provideOptimizationSuggestions()
  
  // 可选：运行性能测试
  const runTest = process.argv.includes('--test')
  if (runTest) {
    runPerformanceTest()
  }
  
  console.log('✅ 性能分析完成!')
  
  if (!runTest) {
    console.log('💡 运行 `node scripts/analyze-performance.js --test` 进行性能测试')
  }
}

main()
