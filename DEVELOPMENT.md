# 灵境(Mentia) 开发指南

## 概述

本文档提供了灵境(Mentia)项目的详细开发指南，包括环境设置、开发流程、代码规范、测试指南等内容。

## 开发环境设置

### 系统要求

- **操作系统**: Linux/macOS/Windows (推荐Linux/macOS)
- **Docker**: 20.10+ (用于数据库服务)
- **Python**: 3.12+
- **Node.js**: 18+
- **Git**: 2.30+

### 快速启动

```bash
# 克隆项目
git clone <repository-url>
cd Mentia

# 一键启动开发环境
./start-dev.sh

# 或使用自定义端口
./start-dev.sh --frontend-port 9331 --backend-port 8001
```

### 手动启动

如果需要手动控制各个服务：

```bash
# 1. 启动数据库服务
docker-compose up -d

# 2. 启动后端服务
cd backend
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
pip install -r requirements.txt
python manage.py migrate
python manage.py runserver

# 3. 启动前端服务
cd frontend
npm install
npm run dev
```

## 项目架构

### 目录结构

```
Mentia/
├── backend/                    # Django后端
│   ├── mentia_backend/        # 项目配置
│   ├── apps/                  # Django应用
│   │   ├── common/           # 通用工具和中间件
│   │   ├── users/            # 用户管理
│   │   ├── growth/           # 成长引擎
│   │   ├── journal/          # 心灵私域
│   │   ├── blog/             # 博客系统
│   │   └── ai/               # AI功能
│   ├── media/                # 媒体文件
│   ├── static/               # 静态文件
│   └── requirements.txt      # Python依赖
├── frontend/                  # Next.js前端
│   ├── src/
│   │   ├── app/              # App Router页面
│   │   ├── components/       # React组件
│   │   ├── lib/              # 工具函数
│   │   ├── types/            # TypeScript类型
│   │   └── __tests__/        # 测试文件
│   ├── public/               # 公共资源
│   └── package.json          # Node.js依赖
├── doc/                      # 文档
├── docker-compose.yml        # Docker配置
└── start-dev.sh             # 启动脚本
```

### 技术栈详解

#### 后端技术栈
- **Django 5.0+**: Web框架
- **Django REST Framework**: API框架
- **PostgreSQL**: 主数据库
- **Redis**: 缓存和会话存储
- **Celery**: 异步任务队列
- **JWT**: 身份认证
- **drf-spectacular**: API文档生成

#### 前端技术栈
- **Next.js 14**: React框架
- **TypeScript**: 类型安全
- **Tailwind CSS**: 样式框架
- **Zustand**: 状态管理
- **React Hook Form**: 表单处理
- **Axios**: HTTP客户端

## 开发规范

### 代码风格

#### Python代码规范
- 使用**Black**进行代码格式化
- 使用**Flake8**进行代码检查
- 遵循**PEP 8**规范
- 使用**Type Hints**提高代码可读性

```bash
# 代码格式化
black .

# 代码检查
flake8 .

# 类型检查
mypy .
```

#### TypeScript/JavaScript代码规范
- 使用**ESLint**进行代码检查
- 使用**Prettier**进行代码格式化
- 遵循**Airbnb**风格指南
- 严格的TypeScript配置

```bash
# 代码检查
npm run lint

# 代码格式化
npm run format

# 类型检查
npm run type-check
```

### 注释规范

#### 文件头注释
每个文件都应包含详细的文件头注释：

```python
"""
文件名 - 灵境(Mentia)后端

文件功能：
    简要描述文件的主要功能和用途

主要功能：
    1. 功能点1
    2. 功能点2
    3. 功能点3

使用方式：
    from module import function
    result = function(params)

作者: Mentia开发团队
创建时间: YYYY-MM-DD
版本: v1.0
"""
```

#### 方法注释
所有公共方法都应包含详细注释：

```python
def process_data(self, data: Dict[str, Any], options: Optional[Dict] = None) -> ProcessResult:
    """
    处理数据的核心方法
    
    参数:
    - data: 待处理的数据字典
    - options: 可选的处理选项
    
    返回:
    - ProcessResult: 处理结果对象
    
    抛出:
    - ValueError: 当数据格式不正确时
    - ProcessingError: 当处理失败时
    """
```

#### TODO注释
在有扩展潜力的地方添加TODO注释：

```python
# TODO: 考虑添加缓存机制提升性能
# TODO: 支持批量处理功能
# TODO: 添加更多的数据验证规则
```

### Git提交规范

使用**Conventional Commits**规范：

```bash
# 功能添加
git commit -m "feat: 添加用户背景上传功能"

# 问题修复
git commit -m "fix: 修复文件上传415错误"

# 文档更新
git commit -m "docs: 更新开发指南"

# 样式调整
git commit -m "style: 统一卡片组件样式"

# 重构代码
git commit -m "refactor: 重构查询优化工具"

# 性能优化
git commit -m "perf: 优化数据库查询性能"

# 测试相关
git commit -m "test: 添加文件上传安全测试"
```

## 开发流程

### 功能开发流程

1. **需求分析**: 明确功能需求和技术方案
2. **分支创建**: 从main分支创建功能分支
3. **开发实现**: 按照代码规范进行开发
4. **测试编写**: 为新功能编写单元测试和集成测试
5. **代码审查**: 提交Pull Request进行代码审查
6. **测试验证**: 确保所有测试通过
7. **文档更新**: 更新相关文档
8. **合并部署**: 合并到主分支并部署

### 分支管理

- **main**: 主分支，保持稳定
- **develop**: 开发分支，集成最新功能
- **feature/xxx**: 功能分支，开发新功能
- **fix/xxx**: 修复分支，修复问题
- **hotfix/xxx**: 热修复分支，紧急修复

### 代码审查清单

- [ ] 代码符合项目规范
- [ ] 包含必要的注释和文档
- [ ] 通过所有测试
- [ ] 没有明显的性能问题
- [ ] 安全性考虑充分
- [ ] 错误处理完善
- [ ] 向后兼容性良好

## 测试指南

### 后端测试

#### 运行测试
```bash
cd backend

# 运行所有测试
python run_tests.py

# 运行特定应用测试
python run_tests.py --app users

# 生成覆盖率报告
python run_tests.py --coverage

# 详细输出
python run_tests.py --verbose
```

#### 测试编写规范
```python
class UserModelTestCase(TestCase):
    """用户模型测试"""
    
    def setUp(self):
        """测试前准备"""
        self.user_data = {
            'email': '<EMAIL>',
            'password': 'testpass123'
        }
    
    def test_create_user(self):
        """测试创建用户"""
        user = User.objects.create_user(**self.user_data)
        self.assertEqual(user.email, self.user_data['email'])
    
    def tearDown(self):
        """测试后清理"""
        User.objects.all().delete()
```

### 前端测试

#### 运行测试
```bash
cd frontend

# 运行所有测试
npm test

# 生成覆盖率报告
npm run test:coverage

# 监视模式
npm run test:watch
```

#### 测试编写规范
```typescript
describe('BackgroundUpload Component', () => {
  const defaultProps = {
    onBackgroundChange: jest.fn(),
    onOpacityChange: jest.fn()
  }

  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders upload area when no background is set', () => {
    render(<BackgroundUpload {...defaultProps} />)
    expect(screen.getByText('选择背景图片')).toBeInTheDocument()
  })
})
```

## 性能优化

### 数据库优化

#### 查询优化
```python
# 使用select_related优化外键查询
posts = Post.objects.select_related('author', 'category')

# 使用prefetch_related优化多对多查询
posts = Post.objects.prefetch_related('tags', 'comments')

# 使用查询优化装饰器
@optimize_queryset(['user'], ['messages'])
def get_conversations(self):
    return AIConversation.objects.all()
```

#### 缓存策略
```python
# 使用缓存装饰器
@cache_with_config('statistics')
def get_user_stats(request):
    return Response(stats_data)

# 手动缓存管理
cache_key = f"user_stats_{user.id}"
stats = cache.get(cache_key)
if not stats:
    stats = calculate_stats(user)
    cache.set(cache_key, stats, 300)
```

### 前端优化

#### 组件优化
```typescript
// 使用React.memo优化组件渲染
const OptimizedComponent = React.memo(({ data }) => {
  return <div>{data.title}</div>
})

// 使用useMemo优化计算
const expensiveValue = useMemo(() => {
  return calculateExpensiveValue(data)
}, [data])

// 使用useCallback优化函数
const handleClick = useCallback(() => {
  onItemClick(item.id)
}, [item.id, onItemClick])
```

## 安全指南

### 文件上传安全

#### 后端验证
```python
# 使用安全验证器
validator = ImageValidator(max_size=10*1024*1024)
is_valid, error = validate_uploaded_file(file, 'background')

if not is_valid:
    return Response({'error': error}, status=400)
```

#### 前端验证
```typescript
// 客户端预验证
const validateFile = (file: File) => {
  const allowedTypes = ['image/jpeg', 'image/png']
  const maxSize = 10 * 1024 * 1024 // 10MB
  
  if (!allowedTypes.includes(file.type)) {
    throw new Error('不支持的文件类型')
  }
  
  if (file.size > maxSize) {
    throw new Error('文件大小超过限制')
  }
}
```

### API安全

#### 频率限制
```python
# 使用中间件限制请求频率
class RateLimitMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response
    
    def __call__(self, request):
        # 实现频率限制逻辑
        return self.get_response(request)
```

#### 输入验证
```python
# 使用序列化器验证输入
class UserInputSerializer(serializers.Serializer):
    title = serializers.CharField(max_length=200)
    content = serializers.CharField()
    
    def validate_title(self, value):
        # 自定义验证逻辑
        if len(value.strip()) == 0:
            raise serializers.ValidationError("标题不能为空")
        return value
```

## 部署指南

### 开发环境部署

使用提供的启动脚本：
```bash
./start-dev.sh
```

### 生产环境部署

#### Docker部署
```bash
# 构建镜像
docker-compose -f docker-compose.prod.yml build

# 启动服务
docker-compose -f docker-compose.prod.yml up -d

# 数据库迁移
docker-compose -f docker-compose.prod.yml exec backend python manage.py migrate

# 收集静态文件
docker-compose -f docker-compose.prod.yml exec backend python manage.py collectstatic
```

#### 环境变量配置
```bash
# .env.prod
DEBUG=False
SECRET_KEY=your-secret-key
DATABASE_URL=postgresql://user:pass@localhost/dbname
REDIS_URL=redis://localhost:6379/0
ALLOWED_HOSTS=yourdomain.com
```

## 故障排除

### 常见问题

#### 数据库连接问题
```bash
# 检查Docker服务状态
docker-compose ps

# 重启数据库服务
docker-compose restart db

# 查看数据库日志
docker-compose logs db
```

#### 端口冲突问题
```bash
# 使用自定义端口
./start-dev.sh --frontend-port 9331 --backend-port 8001

# 检查端口占用
lsof -i :3000
lsof -i :8000
```

#### 依赖安装问题
```bash
# 清理Python缓存
pip cache purge

# 重新安装依赖
pip install -r requirements.txt --force-reinstall

# 清理Node.js缓存
npm cache clean --force

# 重新安装依赖
rm -rf node_modules package-lock.json
npm install
```

## 贡献指南

### 提交代码

1. Fork项目到个人仓库
2. 创建功能分支
3. 提交代码并推送到个人仓库
4. 创建Pull Request
5. 等待代码审查和合并

### 报告问题

1. 检查是否已有相同问题
2. 提供详细的问题描述
3. 包含复现步骤
4. 提供环境信息
5. 附上相关日志

### 功能建议

1. 描述功能需求
2. 说明使用场景
3. 提供设计方案
4. 考虑实现难度
5. 评估影响范围

## 联系方式

- 项目仓库: [GitHub Repository]
- 问题反馈: [Issues]
- 讨论交流: [Discussions]
- 邮件联系: [Email]

---

本文档会随着项目发展持续更新，请定期查看最新版本。
