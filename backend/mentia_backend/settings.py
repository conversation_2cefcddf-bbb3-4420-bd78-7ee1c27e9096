"""
Django settings for mentia_backend project.
灵境 (Mentia) 后端项目配置文件
"""

import os
from pathlib import Path
from decouple import config

from datetime import <PERSON><PERSON><PERSON>

# Helper to robustly parse integer env values that might have comments
from typing import Any

def int_env(name: str, default: int) -> int:
    try:
        raw: Any = config(name, default=str(default))
        # handle values like "60  # minutes"
        return int(str(raw).strip().split()[0])
    except Exception:
        return default

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = config('SECRET_KEY', default='django-insecure-change-me-in-production')

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = config('DEBUG', default=True, cast=bool)

ALLOWED_HOSTS = config('ALLOWED_HOSTS', default='localhost,127.0.0.1').split(',')

# Application definition
DJANGO_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
]

THIRD_PARTY_APPS = [
    'rest_framework',
    'rest_framework_simplejwt',
    'rest_framework_simplejwt.token_blacklist',
    'corsheaders',
    'drf_spectacular',
    'pgvector.django',
]

# 将 users 放在 THIRD_PARTY_APPS 之前，确保其表在 token_blacklist 运行数据迁移前已创建
USERS_APP = [
    'apps.users',
]

LOCAL_APPS = [
    'apps.growth',
    'apps.journal',
    'apps.blog.apps.BlogConfig',
]

# 在SQLite模式下跳过AI应用以避免pgvector依赖
if not config('USE_SQLITE', default=False, cast=bool):
    LOCAL_APPS.append('apps.ai')

INSTALLED_APPS = DJANGO_APPS + USERS_APP + THIRD_PARTY_APPS + LOCAL_APPS

MIDDLEWARE = [
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

ROOT_URLCONF = 'mentia_backend.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'mentia_backend.wsgi.application'

# Database
USE_SQLITE = config('USE_SQLITE', default=False, cast=bool)

DATABASES = {
    'default': {
        **({
            'ENGINE': 'django.db.backends.sqlite3',
            'NAME': BASE_DIR / 'db.sqlite3',
        } if USE_SQLITE else {
            'ENGINE': 'django.db.backends.postgresql',
            'NAME': config('DB_NAME', default='mentia_db'),
            'USER': config('DB_USER', default='mentia_user'),
            'PASSWORD': config('DB_PASSWORD', default='mentia_password'),
            'HOST': config('DB_HOST', default='localhost'),
            'PORT': config('DB_PORT', default='5432'),
        })
    }
}

# Password validation
AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

# Internationalization
LANGUAGE_CODE = 'zh-hans'
TIME_ZONE = 'Asia/Shanghai'
USE_I18N = True
USE_TZ = True

# Static files (CSS, JavaScript, Images)
STATIC_URL = '/static/'
STATIC_ROOT = BASE_DIR / 'staticfiles'

# Media files
MEDIA_URL = '/media/'
MEDIA_ROOT = BASE_DIR / 'media'

# Default primary key field type
DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# Custom User Model
AUTH_USER_MODEL = 'users.User'

# Django REST Framework
REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': [
        'rest_framework_simplejwt.authentication.JWTAuthentication',
    ],
    'DEFAULT_PERMISSION_CLASSES': [
        'rest_framework.permissions.IsAuthenticated',
    ],
    'DEFAULT_SCHEMA_CLASS': 'drf_spectacular.openapi.AutoSchema',
    'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.PageNumberPagination',
    'PAGE_SIZE': 20,
}

# JWT Settings
SIMPLE_JWT = {
    'ACCESS_TOKEN_LIFETIME': timedelta(minutes=int_env('JWT_ACCESS_TOKEN_LIFETIME', 60)),
    'REFRESH_TOKEN_LIFETIME': timedelta(days=int_env('JWT_REFRESH_TOKEN_LIFETIME', 7)),
    'ROTATE_REFRESH_TOKENS': True,
    'BLACKLIST_AFTER_ROTATION': True,
    'SIGNING_KEY': config('JWT_SECRET_KEY', default=SECRET_KEY),
    'AUTH_HEADER_TYPES': ('Bearer',),
    'USER_ID_FIELD': 'user_id',
}


# CORS Settings
# 支持动态前端端口配置，从环境变量读取或使用默认值
FRONTEND_PORT = config('FRONTEND_PORT', default='3000')
FRONTEND_HOSTS = [
    f'http://localhost:{FRONTEND_PORT}',
    f'http://127.0.0.1:{FRONTEND_PORT}',
]

# 添加额外的允许源（如果在环境变量中指定）
EXTRA_CORS_ORIGINS = config('CORS_ALLOWED_ORIGINS', default='').split(',')
EXTRA_CORS_ORIGINS = [origin.strip() for origin in EXTRA_CORS_ORIGINS if origin.strip()]

CORS_ALLOWED_ORIGINS = FRONTEND_HOSTS + EXTRA_CORS_ORIGINS

CORS_ALLOW_CREDENTIALS = True
CORS_ALLOW_ALL_HEADERS = True
CORS_ALLOW_METHODS = [
    'DELETE',
    'GET',
    'OPTIONS',
    'PATCH',
    'POST',
    'PUT',
]

CSRF_TRUSTED_ORIGINS = FRONTEND_HOSTS + EXTRA_CORS_ORIGINS

# Redis Cache
CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': config('REDIS_URL', default='redis://127.0.0.1:6379/1'),
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
        }
    }
}

# AI Configuration
# 先尝试从环境变量读取，再从decouple读取
OPENAI_API_KEY = os.environ.get('OPENAI_API_KEY') or config('OPENAI_API_KEY', default='')
BASE_URL = os.environ.get('BASE_URL') or config('BASE_URL', default='')  # 支持阿里云等第三方API
EMBEDDING_MODEL = os.environ.get('EMBEDDING_MODEL') or config('EMBEDDING_MODEL', default='text-embedding-ada-002')

# 多模型配置 - 根据任务复杂度智能选择
CHAT_MODEL = os.environ.get('CHAT_MODEL') or config('CHAT_MODEL', default='gpt-3.5-turbo')
FLASH_MODEL = os.environ.get('FLASH_MODEL') or config('FLASH_MODEL', default='qwen-flash')
PLUS_MODEL = os.environ.get('PLUS_MODEL') or config('PLUS_MODEL', default='qwen-plus')
MAX_MODEL = os.environ.get('MAX_MODEL') or config('MAX_MODEL', default='qwen-max')

# Logging
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': BASE_DIR / 'logs' / 'django.log',
        },
        'console': {
            'level': 'INFO',
            'class': 'logging.StreamHandler',
        },
    },
    'root': {
        'handlers': ['console', 'file'],
        'level': 'INFO',
    },
}

# Create logs directory if it doesn't exist
os.makedirs(BASE_DIR / 'logs', exist_ok=True)
