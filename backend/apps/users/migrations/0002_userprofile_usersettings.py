# Generated by Django 5.0.7 on 2025-09-04 01:01

import django.db.models.deletion
import django.utils.timezone
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("users", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="UserProfile",
            fields=[
                (
                    "profile_id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        verbose_name="资料ID",
                    ),
                ),
                (
                    "avatar",
                    models.URLField(
                        blank=True,
                        help_text="用户头像图片链接",
                        null=True,
                        verbose_name="头像URL",
                    ),
                ),
                (
                    "display_name",
                    models.CharField(
                        blank=True,
                        help_text="用户自定义显示名称",
                        max_length=100,
                        verbose_name="显示昵称",
                    ),
                ),
                (
                    "phone",
                    models.Char<PERSON>ield(
                        blank=True, max_length=20, verbose_name="手机号码"
                    ),
                ),
                (
                    "organization",
                    models.CharField(
                        blank=True, max_length=200, verbose_name="组织/公司"
                    ),
                ),
                (
                    "bio",
                    models.TextField(
                        blank=True,
                        help_text="用户个人简介或座右铭",
                        verbose_name="个性签名",
                    ),
                ),
                (
                    "location",
                    models.CharField(blank=True, max_length=100, verbose_name="所在地"),
                ),
                ("website", models.URLField(blank=True, verbose_name="个人网站")),
                (
                    "birth_date",
                    models.DateField(blank=True, null=True, verbose_name="出生日期"),
                ),
                (
                    "timezone",
                    models.CharField(
                        default="Asia/Shanghai", max_length=50, verbose_name="时区"
                    ),
                ),
                (
                    "language",
                    models.CharField(
                        default="zh-CN", max_length=10, verbose_name="语言偏好"
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, verbose_name="创建时间"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="profile",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="用户",
                    ),
                ),
            ],
            options={
                "verbose_name": "用户资料",
                "verbose_name_plural": "用户资料",
                "db_table": "user_profiles",
            },
        ),
        migrations.CreateModel(
            name="UserSettings",
            fields=[
                (
                    "settings_id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        verbose_name="设置ID",
                    ),
                ),
                (
                    "theme",
                    models.CharField(
                        choices=[
                            ("light", "亮色模式"),
                            ("dark", "暗色模式"),
                            ("auto", "跟随系统"),
                        ],
                        default="auto",
                        max_length=20,
                        verbose_name="主题模式",
                    ),
                ),
                (
                    "primary_color",
                    models.CharField(
                        default="teal",
                        help_text="主题色彩方案",
                        max_length=20,
                        verbose_name="主题色",
                    ),
                ),
                (
                    "email_notifications",
                    models.BooleanField(default=True, verbose_name="邮件通知"),
                ),
                (
                    "push_notifications",
                    models.BooleanField(default=True, verbose_name="推送通知"),
                ),
                (
                    "weekly_summary",
                    models.BooleanField(default=True, verbose_name="周报总结"),
                ),
                (
                    "profile_visibility",
                    models.CharField(
                        choices=[("public", "公开"), ("private", "私密")],
                        default="private",
                        max_length=20,
                        verbose_name="资料可见性",
                    ),
                ),
                (
                    "auto_save_interval",
                    models.PositiveIntegerField(
                        default=30, verbose_name="自动保存间隔(秒)"
                    ),
                ),
                (
                    "default_view",
                    models.CharField(
                        choices=[
                            ("dashboard", "概览"),
                            ("growth", "成长引擎"),
                            ("compass", "价值罗盘"),
                            ("blog", "私人博客"),
                        ],
                        default="dashboard",
                        max_length=20,
                        verbose_name="默认首页",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, verbose_name="创建时间"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="settings",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="用户",
                    ),
                ),
            ],
            options={
                "verbose_name": "用户设置",
                "verbose_name_plural": "用户设置",
                "db_table": "user_settings",
            },
        ),
    ]
