"""
用户管理应用的Django Admin配置
"""
from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from .models import User, UserValue


@admin.register(User)
class UserAdmin(BaseUserAdmin):
    """自定义用户管理界面"""
    list_display = ('email', 'username', 'is_active', 'is_staff', 'created_at')
    list_filter = ('is_active', 'is_staff', 'is_superuser', 'created_at')
    search_fields = ('email', 'username')
    ordering = ('-created_at',)
    
    fieldsets = (
        (None, {'fields': ('email', 'password')}),
        ('个人信息', {'fields': ('username', 'first_name', 'last_name')}),
        ('权限', {'fields': ('is_active', 'is_staff', 'is_superuser', 'groups', 'user_permissions')}),
        ('重要日期', {'fields': ('last_login', 'date_joined', 'created_at', 'updated_at')}),
        ('安全信息', {'fields': ('encryption_key_salt',)}),
    )
    
    add_fieldsets = (
        (None, {
            'classes': ('wide',),
            'fields': ('email', 'username', 'password1', 'password2'),
        }),
    )
    
    readonly_fields = ('user_id', 'created_at', 'updated_at', 'encryption_key_salt')


@admin.register(UserValue)
class UserValueAdmin(admin.ModelAdmin):
    """用户价值观管理界面"""
    list_display = ('user', 'title', 'priority', 'created_at')
    list_filter = ('priority', 'created_at')
    search_fields = ('user__email', 'title', 'description')
    ordering = ('user', 'priority')
    
    fieldsets = (
        (None, {'fields': ('user', 'title', 'description', 'priority')}),
        ('时间信息', {'fields': ('created_at', 'updated_at')}),
    )
    
    readonly_fields = ('value_id', 'created_at', 'updated_at')
