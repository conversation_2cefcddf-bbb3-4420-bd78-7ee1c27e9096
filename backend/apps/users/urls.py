"""
用户应用的URL配置
"""
from django.urls import path
from rest_framework_simplejwt.views import TokenRefreshView
from . import views

app_name = 'users'

urlpatterns = [
    # 认证相关
    path('register/', views.UserRegistrationView.as_view(), name='register'),
    path('login/', views.UserLoginView.as_view(), name='login'),
    path('logout/', views.logout_view, name='logout'),
    path('token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
    
    # 用户资料
    path('profile/', views.UserProfileView.as_view(), name='profile'),
    path('profile/detail/', views.UserProfileDetailView.as_view(), name='profile_detail'),
    path('profile/complete/', views.UserCompleteProfileView.as_view(), name='profile_complete'),
    path('settings/', views.UserSettingsView.as_view(), name='settings'),
    path('background/upload/', views.BackgroundUploadView.as_view(), name='background_upload'),
    path('password/change/', views.PasswordChangeView.as_view(), name='password_change'),
    path('stats/', views.UserStatsView.as_view(), name='stats'),
    path('encryption-salt/', views.user_encryption_salt_view, name='encryption_salt'),
    
    # 用户价值观
    path('values/', views.UserValueListCreateView.as_view(), name='value_list_create'),
    path('values/<uuid:pk>/', views.UserValueDetailView.as_view(), name='value_detail'),
]
