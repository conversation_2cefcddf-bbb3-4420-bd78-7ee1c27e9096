"""
用户应用测试 - 灵境(Mentia)后端

文件功能：
    为用户应用提供全面的单元测试和集成测试

测试覆盖：
    1. 用户注册和登录
    2. 用户资料管理
    3. 背景图片上传
    4. 权限验证
    5. API端点测试

运行方式：
    python manage.py test apps.users
    python manage.py test apps.users.tests.UserRegistrationTestCase

作者: Mentia后端团队
创建时间: 2025-09-05
版本: v1.0
"""

import os
import tempfile
from django.test import TestCase, override_settings
from django.urls import reverse
from django.core.files.uploadedfile import SimpleUploadedFile
from django.contrib.auth import get_user_model
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken
from PIL import Image
from io import BytesIO

from .models import UserProfile, UserSettings, UserValue
from .serializers import UserRegistrationSerializer, UserProfileSerializer

User = get_user_model()


class UserModelTestCase(TestCase):
    """用户模型测试"""
    
    def setUp(self):
        """测试前准备"""
        self.user_data = {
            'email': '<EMAIL>',
            'password': 'testpassword123',
            'username': 'testuser'
        }
    
    def test_create_user(self):
        """测试创建用户"""
        user = User.objects.create_user(**self.user_data)
        
        self.assertEqual(user.email, self.user_data['email'])
        self.assertEqual(user.username, self.user_data['username'])
        self.assertTrue(user.check_password(self.user_data['password']))
        self.assertTrue(user.is_active)
        self.assertFalse(user.is_staff)
        self.assertFalse(user.is_superuser)
    
    def test_create_superuser(self):
        """测试创建超级用户"""
        user = User.objects.create_superuser(**self.user_data)
        
        self.assertTrue(user.is_staff)
        self.assertTrue(user.is_superuser)
    
    def test_user_str_representation(self):
        """测试用户字符串表示"""
        user = User.objects.create_user(**self.user_data)
        self.assertEqual(str(user), self.user_data['email'])
    
    def test_user_profile_creation(self):
        """测试用户资料自动创建"""
        user = User.objects.create_user(**self.user_data)
        profile, created = UserProfile.objects.get_or_create(user=user)
        
        self.assertIsNotNone(profile)
        self.assertEqual(profile.user, user)
    
    def test_user_settings_creation(self):
        """测试用户设置自动创建"""
        user = User.objects.create_user(**self.user_data)
        settings, created = UserSettings.objects.get_or_create(user=user)
        
        self.assertIsNotNone(settings)
        self.assertEqual(settings.user, user)


class UserRegistrationTestCase(APITestCase):
    """用户注册API测试"""
    
    def setUp(self):
        """测试前准备"""
        self.registration_url = reverse('user-registration')
        self.valid_data = {
            'email': '<EMAIL>',
            'password': 'newpassword123',
            'username': 'newuser'
        }
    
    def test_user_registration_success(self):
        """测试用户注册成功"""
        response = self.client.post(self.registration_url, self.valid_data)
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertIn('message', response.data)
        self.assertIn('user', response.data)
        self.assertIn('tokens', response.data)
        
        # 验证用户已创建
        user = User.objects.get(email=self.valid_data['email'])
        self.assertEqual(user.username, self.valid_data['username'])
    
    def test_user_registration_duplicate_email(self):
        """测试重复邮箱注册"""
        # 先创建一个用户
        User.objects.create_user(**self.valid_data)
        
        # 尝试用相同邮箱注册
        response = self.client.post(self.registration_url, self.valid_data)
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
    
    def test_user_registration_invalid_email(self):
        """测试无效邮箱注册"""
        invalid_data = self.valid_data.copy()
        invalid_data['email'] = 'invalid-email'
        
        response = self.client.post(self.registration_url, invalid_data)
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
    
    def test_user_registration_weak_password(self):
        """测试弱密码注册"""
        weak_data = self.valid_data.copy()
        weak_data['password'] = '123'
        
        response = self.client.post(self.registration_url, weak_data)
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)


class UserLoginTestCase(APITestCase):
    """用户登录API测试"""
    
    def setUp(self):
        """测试前准备"""
        self.login_url = reverse('user-login')
        self.user_data = {
            'email': '<EMAIL>',
            'password': 'testpassword123',
            'username': 'testuser'
        }
        self.user = User.objects.create_user(**self.user_data)
    
    def test_user_login_success(self):
        """测试用户登录成功"""
        login_data = {
            'email': self.user_data['email'],
            'password': self.user_data['password']
        }
        
        response = self.client.post(self.login_url, login_data)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('tokens', response.data)
        self.assertIn('user', response.data)
    
    def test_user_login_invalid_credentials(self):
        """测试无效凭据登录"""
        invalid_data = {
            'email': self.user_data['email'],
            'password': 'wrongpassword'
        }
        
        response = self.client.post(self.login_url, invalid_data)
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
    
    def test_user_login_nonexistent_user(self):
        """测试不存在用户登录"""
        nonexistent_data = {
            'email': '<EMAIL>',
            'password': 'somepassword'
        }
        
        response = self.client.post(self.login_url, nonexistent_data)
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)


class AuthenticatedUserTestCase(APITestCase):
    """需要认证的用户API测试基类"""
    
    def setUp(self):
        """测试前准备"""
        self.user_data = {
            'email': '<EMAIL>',
            'password': 'authpassword123',
            'username': 'authuser'
        }
        self.user = User.objects.create_user(**self.user_data)
        
        # 生成JWT令牌
        refresh = RefreshToken.for_user(self.user)
        self.access_token = str(refresh.access_token)
        
        # 设置认证头
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.access_token}')


class UserProfileTestCase(AuthenticatedUserTestCase):
    """用户资料API测试"""
    
    def test_get_user_profile(self):
        """测试获取用户资料"""
        url = reverse('user-profile')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['email'], self.user.email)
    
    def test_update_user_profile(self):
        """测试更新用户资料"""
        url = reverse('user-profile')
        update_data = {
            'username': 'updateduser',
            'first_name': 'Updated',
            'last_name': 'User'
        }
        
        response = self.client.patch(url, update_data)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.user.refresh_from_db()
        self.assertEqual(self.user.username, update_data['username'])


@override_settings(MEDIA_ROOT=tempfile.mkdtemp())
class BackgroundUploadTestCase(AuthenticatedUserTestCase):
    """背景图片上传测试"""
    
    def setUp(self):
        """测试前准备"""
        super().setUp()
        self.upload_url = reverse('background-upload')
    
    def create_test_image(self, format='JPEG', size=(100, 100)):
        """创建测试图片"""
        image = Image.new('RGB', size, color='red')
        image_io = BytesIO()
        image.save(image_io, format=format)
        image_io.seek(0)
        return image_io
    
    def test_background_upload_success(self):
        """测试背景图片上传成功"""
        image_data = self.create_test_image()
        uploaded_file = SimpleUploadedFile(
            'test_background.jpg',
            image_data.getvalue(),
            content_type='image/jpeg'
        )
        
        response = self.client.post(self.upload_url, {'background': uploaded_file})
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('background_url', response.data)
        self.assertIn('message', response.data)
    
    def test_background_upload_invalid_format(self):
        """测试上传无效格式文件"""
        # 创建一个文本文件
        text_file = SimpleUploadedFile(
            'test.txt',
            b'This is not an image',
            content_type='text/plain'
        )
        
        response = self.client.post(self.upload_url, {'background': text_file})
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('error', response.data)
    
    def test_background_upload_no_file(self):
        """测试没有文件的上传请求"""
        response = self.client.post(self.upload_url, {})
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('error', response.data)
    
    def test_background_delete_success(self):
        """测试删除背景图片成功"""
        # 先上传一个背景图片
        image_data = self.create_test_image()
        uploaded_file = SimpleUploadedFile(
            'test_background.jpg',
            image_data.getvalue(),
            content_type='image/jpeg'
        )
        self.client.post(self.upload_url, {'background': uploaded_file})
        
        # 删除背景图片
        response = self.client.delete(self.upload_url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('message', response.data)


class UserValueTestCase(AuthenticatedUserTestCase):
    """用户价值观测试"""
    
    def setUp(self):
        """测试前准备"""
        super().setUp()
        self.value_data = {
            'title': '诚实',
            'description': '始终保持诚实和透明',
            'priority': 1
        }
    
    def test_create_user_value(self):
        """测试创建用户价值观"""
        value = UserValue.objects.create(user=self.user, **self.value_data)
        
        self.assertEqual(value.title, self.value_data['title'])
        self.assertEqual(value.user, self.user)
        self.assertEqual(value.priority, self.value_data['priority'])
    
    def test_user_value_str_representation(self):
        """测试用户价值观字符串表示"""
        value = UserValue.objects.create(user=self.user, **self.value_data)
        expected_str = f"{self.user.username} - {self.value_data['title']}"
        
        self.assertEqual(str(value), expected_str)


class UserSerializerTestCase(TestCase):
    """用户序列化器测试"""
    
    def setUp(self):
        """测试前准备"""
        self.user_data = {
            'email': '<EMAIL>',
            'password': 'serializerpass123',
            'username': 'serializeruser'
        }
    
    def test_user_registration_serializer_valid(self):
        """测试用户注册序列化器验证"""
        serializer = UserRegistrationSerializer(data=self.user_data)
        
        self.assertTrue(serializer.is_valid())
    
    def test_user_registration_serializer_invalid_email(self):
        """测试用户注册序列化器无效邮箱"""
        invalid_data = self.user_data.copy()
        invalid_data['email'] = 'invalid-email'
        
        serializer = UserRegistrationSerializer(data=invalid_data)
        
        self.assertFalse(serializer.is_valid())
        self.assertIn('email', serializer.errors)
    
    def test_user_profile_serializer(self):
        """测试用户资料序列化器"""
        user = User.objects.create_user(**self.user_data)
        serializer = UserProfileSerializer(user)
        
        self.assertEqual(serializer.data['email'], user.email)
        self.assertEqual(serializer.data['username'], user.username)
