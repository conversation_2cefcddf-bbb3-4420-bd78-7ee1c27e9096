"""
用户应用的序列化器
处理用户注册、登录、个人信息等数据序列化
"""
from rest_framework import serializers
from django.contrib.auth import authenticate
from django.contrib.auth.password_validation import validate_password
from .models import User, UserValue, UserProfile, UserSettings


class UserRegistrationSerializer(serializers.ModelSerializer):
    """用户注册序列化器"""
    password = serializers.CharField(
        write_only=True,
        validators=[validate_password],
        style={'input_type': 'password'}
    )
    password_confirm = serializers.CharField(
        write_only=True,
        style={'input_type': 'password'}
    )
    
    class Meta:
        model = User
        fields = ('email', 'username', 'password', 'password_confirm')
        extra_kwargs = {
            'username': {'required': False, 'allow_blank': True},
        }

    def validate(self, attrs):
        """验证密码确认"""
        if attrs['password'] != attrs['password_confirm']:
            raise serializers.ValidationError("密码和确认密码不匹配")
        return attrs

    def create(self, validated_data):
        """创建用户"""
        validated_data.pop('password_confirm')
        email = validated_data['email']
        username = (validated_data.get('username') or '').strip()
        if not username:
            # 默认使用邮箱前缀作为用户名，避免必填约束
            username = email.split('@')[0]
        # 确保用户名唯一：如已存在则追加数字后缀
        base = username
        i = 0
        while User.objects.filter(username=username).exists():
            i += 1
            username = f"{base}{i}"
        user = User.objects.create_user(
            email=email,
            username=username,
            password=validated_data['password']
        )
        return user


class UserLoginSerializer(serializers.Serializer):
    """用户登录序列化器"""
    email = serializers.EmailField()
    password = serializers.CharField(
        style={'input_type': 'password'},
        write_only=True
    )
    
    def validate(self, attrs):
        """验证登录凭据"""
        email = attrs.get('email')
        password = attrs.get('password')
        
        if email and password:
            user = authenticate(
                request=self.context.get('request'),
                username=email,
                password=password
            )
            
            if not user:
                raise serializers.ValidationError('邮箱或密码错误')
            
            if not user.is_active:
                raise serializers.ValidationError('用户账户已被禁用')
            
            attrs['user'] = user
            return attrs
        else:
            raise serializers.ValidationError('必须提供邮箱和密码')


class UserProfileSerializer(serializers.ModelSerializer):
    """用户个人资料序列化器"""
    class Meta:
        model = User
        fields = (
            'user_id', 'email', 'username', 'first_name', 'last_name',
            'created_at', 'updated_at'
        )
        read_only_fields = ('user_id', 'email', 'created_at', 'updated_at')


class UserValueSerializer(serializers.ModelSerializer):
    """用户价值观序列化器"""
    class Meta:
        model = UserValue
        fields = (
            'value_id', 'title', 'description', 'priority',
            'created_at', 'updated_at'
        )
        read_only_fields = ('value_id', 'created_at', 'updated_at')
    
    def create(self, validated_data):
        """创建用户价值观"""
        validated_data['user'] = self.context['request'].user
        return super().create(validated_data)


class PasswordChangeSerializer(serializers.Serializer):
    """密码修改序列化器"""
    old_password = serializers.CharField(
        write_only=True,
        style={'input_type': 'password'}
    )
    new_password = serializers.CharField(
        write_only=True,
        validators=[validate_password],
        style={'input_type': 'password'}
    )
    new_password_confirm = serializers.CharField(
        write_only=True,
        style={'input_type': 'password'}
    )
    
    def validate_old_password(self, value):
        """验证旧密码"""
        user = self.context['request'].user
        if not user.check_password(value):
            raise serializers.ValidationError('旧密码错误')
        return value
    
    def validate(self, attrs):
        """验证新密码确认"""
        if attrs['new_password'] != attrs['new_password_confirm']:
            raise serializers.ValidationError('新密码和确认密码不匹配')
        return attrs
    
    def save(self):
        """保存新密码"""
        user = self.context['request'].user
        user.set_password(self.validated_data['new_password'])
        user.save()
        return user


class UserProfileDetailSerializer(serializers.ModelSerializer):
    """用户详细资料序列化器"""
    class Meta:
        model = UserProfile
        fields = (
            'profile_id', 'avatar', 'display_name', 'phone', 'organization',
            'bio', 'location', 'website', 'birth_date', 'timezone', 'language',
            'created_at', 'updated_at'
        )
        read_only_fields = ('profile_id', 'created_at', 'updated_at')


class UserSettingsSerializer(serializers.ModelSerializer):
    """用户设置序列化器"""
    class Meta:
        model = UserSettings
        fields = (
            'settings_id', 'theme', 'primary_color', 'email_notifications',
            'push_notifications', 'weekly_summary', 'profile_visibility',
            'auto_save_interval', 'default_view', 'custom_background',
            'background_opacity', 'background_blur', 'created_at', 'updated_at'
        )
        read_only_fields = ('settings_id', 'created_at', 'updated_at')


class UserCompleteProfileSerializer(serializers.ModelSerializer):
    """用户完整资料序列化器（包含基本信息、详细资料和设置）"""
    profile = UserProfileDetailSerializer(read_only=True)
    settings = UserSettingsSerializer(read_only=True)

    class Meta:
        model = User
        fields = (
            'user_id', 'email', 'username', 'first_name', 'last_name',
            'created_at', 'updated_at', 'profile', 'settings'
        )
        read_only_fields = ('user_id', 'email', 'created_at', 'updated_at')


class UserStatsSerializer(serializers.Serializer):
    """用户统计信息序列化器"""
    total_goals = serializers.IntegerField(read_only=True)
    completed_goals = serializers.IntegerField(read_only=True)
    total_tasks = serializers.IntegerField(read_only=True)
    completed_tasks = serializers.IntegerField(read_only=True)
    journal_entries_count = serializers.IntegerField(read_only=True)
    total_tags = serializers.IntegerField(read_only=True)
    join_days = serializers.IntegerField(read_only=True)
