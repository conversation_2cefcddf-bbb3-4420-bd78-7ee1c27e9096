"""
用户应用的视图
处理用户注册、登录、个人信息管理等功能
"""
from rest_framework import status, generics, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.parsers import <PERSON><PERSON>art<PERSON>ars<PERSON>, FormParser
from rest_framework_simplejwt.tokens import RefreshToken
from django.contrib.auth import login
from django.utils import timezone
from django.db.models import Count, Q
from django.core.files.storage import default_storage
from django.core.files.base import ContentFile
from datetime import datetime
import os
import uuid
import logging

from .models import User, UserValue, UserProfile, UserSettings
from .serializers import (
    UserRegistrationSerializer,
    UserLoginSerializer,
    UserProfileSerializer,
    UserValueSerializer,
    PasswordChangeSerializer,
    UserStatsSerializer,
    UserProfileDetailSerializer,
    UserSettingsSerializer,
    UserCompleteProfileSerializer
)
from apps.common.file_validators import validate_uploaded_file, SecureFileHandler
from apps.common.decorators import api_error_handler

logger = logging.getLogger(__name__)


class UserRegistrationView(APIView):
    """用户注册视图"""
    permission_classes = [permissions.AllowAny]
    
    def post(self, request):
        serializer = UserRegistrationSerializer(data=request.data)
        if serializer.is_valid():
            user = serializer.save()
            
            # 生成JWT令牌
            refresh = RefreshToken.for_user(user)
            
            return Response({
                'message': '注册成功',
                'user': UserProfileSerializer(user).data,
                'tokens': {
                    'refresh': str(refresh),
                    'access': str(refresh.access_token),
                }
            }, status=status.HTTP_201_CREATED)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class UserLoginView(APIView):
    """用户登录视图"""
    permission_classes = [permissions.AllowAny]
    
    def post(self, request):
        serializer = UserLoginSerializer(
            data=request.data,
            context={'request': request}
        )
        
        if serializer.is_valid():
            user = serializer.validated_data['user']
            login(request, user)
            
            # 生成JWT令牌
            refresh = RefreshToken.for_user(user)
            
            return Response({
                'message': '登录成功',
                'user': UserProfileSerializer(user).data,
                'tokens': {
                    'refresh': str(refresh),
                    'access': str(refresh.access_token),
                }
            }, status=status.HTTP_200_OK)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class UserProfileView(generics.RetrieveUpdateAPIView):
    """用户个人资料视图"""
    serializer_class = UserProfileSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_object(self):
        return self.request.user


class PasswordChangeView(APIView):
    """密码修改视图"""
    permission_classes = [permissions.IsAuthenticated]
    
    def post(self, request):
        serializer = PasswordChangeSerializer(
            data=request.data,
            context={'request': request}
        )
        
        if serializer.is_valid():
            serializer.save()
            return Response({
                'message': '密码修改成功'
            }, status=status.HTTP_200_OK)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class UserValueListCreateView(generics.ListCreateAPIView):
    """用户价值观列表和创建视图"""
    serializer_class = UserValueSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        return UserValue.objects.filter(user=self.request.user)


class UserValueDetailView(generics.RetrieveUpdateDestroyAPIView):
    """用户价值观详情视图"""
    serializer_class = UserValueSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        return UserValue.objects.filter(user=self.request.user)


class UserStatsView(APIView):
    """用户统计信息视图"""
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request):
        user = request.user
        
        # 获取成长项统计
        growth_stats = user.growth_items.aggregate(
            total_goals=Count('item_id', filter=Q(item_type='goal')),
            completed_goals=Count('item_id', filter=Q(item_type='goal', status='completed')),
            total_tasks=Count('item_id', filter=Q(item_type='task')),
            completed_tasks=Count('item_id', filter=Q(item_type='task', status='completed')),
        )
        
        # 获取日记统计
        journal_count = user.journal_entries.count()
        
        # 获取标签统计
        tag_count = user.growth_items.filter(
            status='completed'
        ).values('tags').distinct().count()
        
        # 计算加入天数
        join_days = (timezone.now().date() - user.created_at.date()).days
        
        stats_data = {
            'total_goals': growth_stats['total_goals'] or 0,
            'completed_goals': growth_stats['completed_goals'] or 0,
            'total_tasks': growth_stats['total_tasks'] or 0,
            'completed_tasks': growth_stats['completed_tasks'] or 0,
            'journal_entries_count': journal_count,
            'total_tags': tag_count,
            'join_days': join_days,
        }
        
        serializer = UserStatsSerializer(stats_data)
        return Response(serializer.data)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def logout_view(request):
    """用户登出视图"""
    try:
        refresh_token = request.data.get('refresh_token')
        if refresh_token:
            token = RefreshToken(refresh_token)
            token.blacklist()
        
        return Response({
            'message': '登出成功'
        }, status=status.HTTP_200_OK)
    except Exception as e:
        return Response({
            'error': '登出失败'
        }, status=status.HTTP_400_BAD_REQUEST)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def user_encryption_salt_view(request):
    """获取用户加密盐值"""
    return Response({
        'encryption_key_salt': request.user.encryption_key_salt
    })


class UserProfileDetailView(generics.RetrieveUpdateAPIView):
    """用户详细资料视图"""
    serializer_class = UserProfileDetailSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_object(self):
        """获取或创建用户资料"""
        profile, created = UserProfile.objects.get_or_create(
            user=self.request.user
        )
        return profile


class UserSettingsView(generics.RetrieveUpdateAPIView):
    """用户设置视图"""
    serializer_class = UserSettingsSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_object(self):
        """获取或创建用户设置"""
        settings, created = UserSettings.objects.get_or_create(
            user=self.request.user
        )
        return settings


class UserCompleteProfileView(generics.RetrieveAPIView):
    """用户完整资料视图（包含基本信息、详细资料和设置）"""
    serializer_class = UserCompleteProfileSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_object(self):
        """
        获取用户完整信息，优化数据库查询
        使用 select_related 预加载相关对象，减少数据库查询次数
        """
        user = self.request.user

        # 使用 select_related 优化查询
        user = User.objects.select_related(
            'profile', 'settings'
        ).get(user_id=user.user_id)

        # 确保用户有资料和设置记录
        UserProfile.objects.get_or_create(user=user)
        UserSettings.objects.get_or_create(user=user)

        return user


class BackgroundUploadView(APIView):
    """背景图片上传视图 - 增强安全版本"""
    permission_classes = [permissions.IsAuthenticated]
    parser_classes = [MultiPartParser, FormParser]

    @api_error_handler
    def post(self, request):
        """
        上传背景图片

        功能增强：
        - 多重文件安全验证（类型、内容、魔数检查）
        - 安全文件名生成
        - 图片尺寸和质量检查
        - 恶意文件检测
        """
        if 'background' not in request.FILES:
            return Response({
                'error': '请选择要上传的图片文件',
                'code': 'NO_FILE'
            }, status=status.HTTP_400_BAD_REQUEST)

        background_file = request.FILES['background']

        # 使用增强的文件验证系统
        is_valid, error_message = validate_uploaded_file(background_file, 'background')
        if not is_valid:
            return Response({
                'error': error_message,
                'code': 'VALIDATION_FAILED'
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            # 生成安全的文件名
            safe_filename = SecureFileHandler.generate_safe_filename(
                background_file.name,
                str(request.user.user_id)
            )

            # 生成安全的文件路径
            file_path = SecureFileHandler.get_safe_file_path(safe_filename, 'backgrounds')

            # 验证文件路径安全性
            if not SecureFileHandler.validate_file_path(file_path):
                return Response({
                    'error': '文件路径不安全',
                    'code': 'UNSAFE_PATH'
                }, status=status.HTTP_400_BAD_REQUEST)

            # 删除用户之前的背景图片（如果存在）
            self._cleanup_old_background(request.user)

            # 保存文件
            saved_path = default_storage.save(file_path, ContentFile(background_file.read()))
            file_url = default_storage.url(saved_path)

            # 更新用户设置
            settings, created = UserSettings.objects.get_or_create(user=request.user)
            settings.custom_background = file_url
            settings.save()

            logger.info(f"Background uploaded successfully for user {request.user.user_id}: {saved_path}")

            return Response({
                'message': '背景图片上传成功',
                'background_url': file_url,
                'file_info': {
                    'filename': safe_filename,
                    'size': background_file.size,
                    'content_type': background_file.content_type
                }
            }, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Background upload failed for user {request.user.user_id}: {str(e)}")
            return Response({
                'error': '上传失败，请稍后重试',
                'code': 'UPLOAD_FAILED'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @api_error_handler
    def delete(self, request):
        """删除背景图片 - 增强安全版本"""
        try:
            settings = UserSettings.objects.get(user=request.user)

            # 删除文件
            if settings.custom_background:
                self._cleanup_old_background(request.user)
                settings.custom_background = None
                settings.save()

                logger.info(f"Background deleted successfully for user {request.user.user_id}")

            return Response({
                'message': '背景图片删除成功'
            }, status=status.HTTP_200_OK)

        except UserSettings.DoesNotExist:
            return Response({
                'error': '用户设置不存在',
                'code': 'SETTINGS_NOT_FOUND'
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            logger.error(f"Background deletion failed for user {request.user.user_id}: {str(e)}")
            return Response({
                'error': '删除失败，请稍后重试',
                'code': 'DELETE_FAILED'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def _cleanup_old_background(self, user):
        """
        清理用户的旧背景图片

        参数:
        - user: 用户对象
        """
        try:
            settings = UserSettings.objects.filter(user=user).first()
            if not settings or not settings.custom_background:
                return

            # 从URL中提取文件路径
            background_url = settings.custom_background

            # 处理相对路径和绝对路径
            if background_url.startswith('/media/'):
                file_path = background_url[7:]  # 移除 '/media/' 前缀
            elif '/media/' in background_url:
                file_path = background_url.split('/media/')[-1]
            else:
                # 如果是相对路径，直接使用
                file_path = background_url

            # 验证文件路径安全性
            if SecureFileHandler.validate_file_path(file_path) and default_storage.exists(file_path):
                default_storage.delete(file_path)
                logger.info(f"Deleted old background file: {file_path}")

        except Exception as e:
            logger.error(f"Failed to cleanup old background for user {user.user_id}: {str(e)}")
