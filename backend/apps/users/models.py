"""
用户模型
根据数据库文档中的 users 表设计
"""
import uuid
from django.contrib.auth.models import AbstractUser
from django.db import models
from django.utils import timezone as django_timezone
from .managers import UserManager


class User(AbstractUser):
    """
    自定义用户模型
    扩展Django默认用户模型以支持灵境项目的特殊需求
    """
    user_id = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False,
        verbose_name='用户ID'
    )
    
    email = models.EmailField(
        unique=True,
        verbose_name='邮箱地址',
        help_text='用户登录邮箱，必须加密存储'
    )

    encryption_key_salt = models.CharField(
        max_length=255,
        verbose_name='加密密钥盐值',
        help_text='用于在客户端派生数据加密密钥的盐值，服务端永不存储原始密钥'
    )
    
    created_at = models.DateTimeField(
        default=django_timezone.now,
        verbose_name='创建时间'
    )
    
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name='更新时间'
    )
    
    # 使用email作为登录字段
    USERNAME_FIELD = 'email'
    # 不强制要求username字段（可选昵称）
    REQUIRED_FIELDS = []

    class Meta:
        db_table = 'users'
        verbose_name = '用户'
        verbose_name_plural = '用户'
        ordering = ['-created_at']
    
    def __str__(self):
        return self.email or str(self.user_id)
    
    def save(self, *args, **kwargs):
        """
        保存用户时自动生成加密盐值
        """
        if not self.encryption_key_salt:
            import secrets
            self.encryption_key_salt = secrets.token_urlsafe(32)
        super().save(*args, **kwargs)

    @property
    def id(self):
        """为了兼容JWT和其他依赖id字段的库，提供id属性"""
        return self.user_id

    objects = UserManager()


class UserValue(models.Model):
    """
    用户价值观模型
    存储用户的核心价值观和人生信条
    """
    value_id = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False,
        verbose_name='价值观ID'
    )
    
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='values',
        verbose_name='用户'
    )
    
    title = models.CharField(
        max_length=200,
        verbose_name='价值观标题'
    )
    
    description = models.TextField(
        blank=True,
        verbose_name='价值观描述'
    )
    
    priority = models.PositiveIntegerField(
        default=1,
        verbose_name='优先级',
        help_text='数字越小优先级越高'
    )
    
    created_at = models.DateTimeField(
        default=django_timezone.now,
        verbose_name='创建时间'
    )
    
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name='更新时间'
    )
    
    class Meta:
        db_table = 'user_values'
        verbose_name = '用户价值观'
        verbose_name_plural = '用户价值观'
        ordering = ['priority', '-created_at']
        unique_together = ['user', 'title']
    
    def __str__(self):
        return f"{self.user.email} - {self.title}"


class UserProfile(models.Model):
    """
    用户资料模型
    存储用户的详细个人信息
    """
    profile_id = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False,
        verbose_name='资料ID'
    )

    user = models.OneToOneField(
        User,
        on_delete=models.CASCADE,
        related_name='profile',
        verbose_name='用户'
    )

    avatar = models.URLField(
        blank=True,
        null=True,
        verbose_name='头像URL',
        help_text='用户头像图片链接'
    )

    display_name = models.CharField(
        max_length=100,
        blank=True,
        verbose_name='显示昵称',
        help_text='用户自定义显示名称'
    )

    phone = models.CharField(
        max_length=20,
        blank=True,
        verbose_name='手机号码'
    )

    organization = models.CharField(
        max_length=200,
        blank=True,
        verbose_name='组织/公司'
    )

    bio = models.TextField(
        blank=True,
        verbose_name='个性签名',
        help_text='用户个人简介或座右铭'
    )

    location = models.CharField(
        max_length=100,
        blank=True,
        verbose_name='所在地'
    )

    website = models.URLField(
        blank=True,
        verbose_name='个人网站'
    )

    birth_date = models.DateField(
        blank=True,
        null=True,
        verbose_name='出生日期'
    )

    timezone = models.CharField(
        max_length=50,
        default='Asia/Shanghai',
        verbose_name='时区'
    )

    language = models.CharField(
        max_length=10,
        default='zh-CN',
        verbose_name='语言偏好'
    )

    created_at = models.DateTimeField(
        default=django_timezone.now,
        verbose_name='创建时间'
    )

    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name='更新时间'
    )

    class Meta:
        db_table = 'user_profiles'
        verbose_name = '用户资料'
        verbose_name_plural = '用户资料'

    def __str__(self):
        return f"{self.user.email} - Profile"


class UserSettings(models.Model):
    """
    用户设置模型
    存储用户的应用内设置偏好
    """
    settings_id = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False,
        verbose_name='设置ID'
    )

    user = models.OneToOneField(
        User,
        on_delete=models.CASCADE,
        related_name='settings',
        verbose_name='用户'
    )

    # 主题设置
    theme = models.CharField(
        max_length=20,
        choices=[
            ('light', '亮色模式'),
            ('dark', '暗色模式'),
            ('auto', '跟随系统'),
        ],
        default='auto',
        verbose_name='主题模式'
    )

    primary_color = models.CharField(
        max_length=20,
        default='teal',
        verbose_name='主题色',
        help_text='主题色彩方案'
    )

    # 通知设置
    email_notifications = models.BooleanField(
        default=True,
        verbose_name='邮件通知'
    )

    push_notifications = models.BooleanField(
        default=True,
        verbose_name='推送通知'
    )

    weekly_summary = models.BooleanField(
        default=True,
        verbose_name='周报总结'
    )

    # 隐私设置
    profile_visibility = models.CharField(
        max_length=20,
        choices=[
            ('public', '公开'),
            ('private', '私密'),
        ],
        default='private',
        verbose_name='资料可见性'
    )

    # 功能设置
    auto_save_interval = models.PositiveIntegerField(
        default=30,
        verbose_name='自动保存间隔(秒)'
    )

    default_view = models.CharField(
        max_length=20,
        choices=[
            ('dashboard', '概览'),
            ('growth', '成长引擎'),
            ('compass', '价值罗盘'),
            ('blog', '私人博客'),
        ],
        default='dashboard',
        verbose_name='默认首页'
    )

    # 背景设置
    custom_background = models.URLField(
        blank=True,
        null=True,
        verbose_name='自定义背景图片',
        help_text='用户上传的背景图片URL'
    )

    background_opacity = models.FloatField(
        default=0.3,
        verbose_name='背景透明度',
        help_text='背景图片的透明度 (0.0-1.0)'
    )

    background_blur = models.IntegerField(
        default=0,
        verbose_name='背景模糊度',
        help_text='背景图片的模糊程度 (0-20px)'
    )

    created_at = models.DateTimeField(
        default=django_timezone.now,
        verbose_name='创建时间'
    )

    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name='更新时间'
    )

    class Meta:
        db_table = 'user_settings'
        verbose_name = '用户设置'
        verbose_name_plural = '用户设置'

    def __str__(self):
        return f"{self.user.email} - Settings"
