# Generated by Django 5.0.7 on 2025-08-28 08:23

import django.utils.timezone
import pgvector.django.vector
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="AIConversation",
            fields=[
                (
                    "conversation_id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        verbose_name="对话ID",
                    ),
                ),
                (
                    "title",
                    models.CharField(
                        blank=True,
                        help_text="AI自动生成或用户自定义的对话标题",
                        max_length=200,
                        verbose_name="对话标题",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, verbose_name="创建时间"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
            ],
            options={
                "verbose_name": "AI对话",
                "verbose_name_plural": "AI对话",
                "db_table": "ai_conversations",
                "ordering": ["-updated_at"],
            },
        ),
        migrations.CreateModel(
            name="AIInsight",
            fields=[
                (
                    "insight_id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        verbose_name="洞察ID",
                    ),
                ),
                (
                    "insight_type",
                    models.CharField(
                        choices=[
                            ("skill_analysis", "技能分析"),
                            ("growth_pattern", "成长模式"),
                            ("value_alignment", "价值观一致性"),
                            ("emotional_trend", "情绪趋势"),
                            ("goal_suggestion", "目标建议"),
                        ],
                        max_length=50,
                        verbose_name="洞察类型",
                    ),
                ),
                ("title", models.CharField(max_length=200, verbose_name="洞察标题")),
                ("content", models.TextField(verbose_name="洞察内容")),
                (
                    "confidence_score",
                    models.FloatField(
                        default=0.0,
                        help_text="AI对这个洞察的置信度 (0-1)",
                        verbose_name="置信度",
                    ),
                ),
                (
                    "is_read",
                    models.BooleanField(default=False, verbose_name="是否已读"),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, verbose_name="创建时间"
                    ),
                ),
            ],
            options={
                "verbose_name": "AI洞察",
                "verbose_name_plural": "AI洞察",
                "db_table": "ai_insights",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="AIMessage",
            fields=[
                (
                    "message_id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        verbose_name="消息ID",
                    ),
                ),
                (
                    "role",
                    models.CharField(
                        choices=[
                            ("user", "用户"),
                            ("assistant", "AI助手"),
                            ("system", "系统"),
                        ],
                        max_length=20,
                        verbose_name="角色",
                    ),
                ),
                ("content", models.TextField(verbose_name="消息内容")),
                (
                    "metadata",
                    models.JSONField(
                        blank=True,
                        default=dict,
                        help_text="包含模型参数、token使用量等信息",
                        verbose_name="元数据",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, verbose_name="创建时间"
                    ),
                ),
            ],
            options={
                "verbose_name": "AI消息",
                "verbose_name_plural": "AI消息",
                "db_table": "ai_messages",
                "ordering": ["created_at"],
            },
        ),
        migrations.CreateModel(
            name="VectorEmbedding",
            fields=[
                (
                    "embedding_id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        verbose_name="向量ID",
                    ),
                ),
                (
                    "source_id",
                    models.UUIDField(
                        help_text="来源数据的ID (如 item_id, entry_id)",
                        verbose_name="来源数据ID",
                    ),
                ),
                (
                    "source_type",
                    models.CharField(
                        choices=[
                            ("growth_item", "成长项"),
                            ("journal_entry", "日记条目"),
                            ("user_value", "用户价值观"),
                        ],
                        max_length=50,
                        verbose_name="来源类型",
                    ),
                ),
                (
                    "embedding",
                    pgvector.django.vector.VectorField(
                        dimensions=768,
                        help_text="文本内容的向量表示，维度取决于嵌入模型",
                        verbose_name="向量嵌入",
                    ),
                ),
                (
                    "source_text_hash",
                    models.CharField(
                        help_text="来源文本的SHA-256哈希，用于检测内容变更",
                        max_length=64,
                        verbose_name="来源文本哈希",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, verbose_name="创建时间"
                    ),
                ),
            ],
            options={
                "verbose_name": "向量嵌入",
                "verbose_name_plural": "向量嵌入",
                "db_table": "vector_embeddings",
                "ordering": ["-created_at"],
            },
        ),
    ]
