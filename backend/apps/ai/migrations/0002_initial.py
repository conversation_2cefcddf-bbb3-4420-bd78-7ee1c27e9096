# Generated by Django 5.0.7 on 2025-08-28 08:23

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("ai", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name="aiconversation",
            name="user",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="ai_conversations",
                to=settings.AUTH_USER_MODEL,
                verbose_name="用户",
            ),
        ),
        migrations.AddField(
            model_name="aiinsight",
            name="user",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="ai_insights",
                to=settings.AUTH_USER_MODEL,
                verbose_name="用户",
            ),
        ),
        migrations.AddField(
            model_name="aimessage",
            name="conversation",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="messages",
                to="ai.aiconversation",
                verbose_name="对话",
            ),
        ),
        migrations.AddField(
            model_name="vectorembedding",
            name="user",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="vector_embeddings",
                to=settings.AUTH_USER_MODEL,
                verbose_name="用户",
            ),
        ),
        migrations.AddIndex(
            model_name="vectorembedding",
            index=models.Index(fields=["user"], name="vector_embe_user_id_ddc0e8_idx"),
        ),
        migrations.AddIndex(
            model_name="vectorembedding",
            index=models.Index(
                fields=["source_type"], name="vector_embe_source__999c1d_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="vectorembedding",
            index=models.Index(
                fields=["source_id", "source_type"],
                name="vector_embe_source__df889e_idx",
            ),
        ),
        migrations.AlterUniqueTogether(
            name="vectorembedding",
            unique_together={("source_id", "source_type")},
        ),
    ]
