"""
AI功能应用的URL配置
"""
from django.urls import path
from . import views

app_name = 'ai'

urlpatterns = [
    # 阶段一：轻量级AI能力注入

    # 任务1.1: AI驱动的开始 - 子任务生成
    path(
        'generate-subtasks/',
        views.SubtaskGenerationView.as_view(),
        name='generate_subtasks'
    ),

    # 任务1.2: AI辅助标题与摘要
    path(
        'generate-title-summary/',
        views.TitleSummaryGenerationView.as_view(),
        name='generate_title_summary'
    ),

    # 任务2.1: AI驱动的复盘
    path(
        'generate-review/',
        views.ProjectReviewView.as_view(),
        name='generate_review'
    ),

    # 任务2.2: 技能标签提取与图谱生成
    path(
        'extract-skills/',
        views.SkillExtractionView.as_view(),
        name='extract_skills'
    ),

    # AI对话管理
    path(
        'conversations/',
        views.AIConversationListView.as_view(),
        name='conversation_list'
    ),
    path(
        'conversations/<uuid:conversation_id>/',
        views.AIConversationDetailView.as_view(),
        name='conversation_detail'
    ),

    # AI洞察管理
    path(
        'insights/',
        views.AIInsightListView.as_view(),
        name='insight_list'
    ),
    path(
        'insights/<uuid:insight_id>/read/',
        views.mark_insight_read,
        name='mark_insight_read'
    ),

    # 阶段三：数字镜像 (The Digital Self)

    # 任务3.1: 个人数据向量化
    path(
        'vectorize-journal/',
        views.vectorize_journal_entry,
        name='vectorize_journal'
    ),
    path(
        'batch-vectorize/',
        views.batch_vectorize_user_data,
        name='batch_vectorize'
    ),

    # 任务3.2: AI伴侣对话系统
    path(
        'search-memories/',
        views.search_memories,
        name='search_memories'
    ),
    path(
        'chat/',
        views.ChatView.as_view(),
        name='chat'
    ),
]
