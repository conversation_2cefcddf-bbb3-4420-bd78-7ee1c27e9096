"""
AI应用测试 - 灵境(Mentia)后端

文件功能：
    为AI应用提供全面的单元测试和集成测试

测试覆盖：
    1. AI对话功能
    2. 向量嵌入
    3. AI洞察生成
    4. 子任务生成
    5. 技能提取

运行方式：
    python manage.py test apps.ai
    python manage.py test apps.ai.tests.AIConversationTestCase

作者: Mentia后端团队
创建时间: 2025-09-05
版本: v1.0
"""

from unittest.mock import patch, MagicMock
from django.test import TestCase
from django.contrib.auth import get_user_model
from rest_framework.test import APITestCase
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken

from .models import AIConversation, AIMessage, VectorEmbedding, AIInsight
from .services import ai_service
from apps.growth.models import GrowthItem

User = get_user_model()


class AIModelTestCase(TestCase):
    """AI模型测试"""
    
    def setUp(self):
        """测试前准备"""
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            username='ai_testuser'
        )
    
    def test_create_ai_conversation(self):
        """测试创建AI对话"""
        conversation = AIConversation.objects.create(
            user=self.user,
            title='测试对话',
            context_type='general'
        )
        
        self.assertEqual(conversation.user, self.user)
        self.assertEqual(conversation.title, '测试对话')
        self.assertEqual(conversation.context_type, 'general')
        self.assertTrue(conversation.is_active)
    
    def test_create_ai_message(self):
        """测试创建AI消息"""
        conversation = AIConversation.objects.create(
            user=self.user,
            title='测试对话'
        )
        
        message = AIMessage.objects.create(
            conversation=conversation,
            role='user',
            content='你好，AI助手'
        )
        
        self.assertEqual(message.conversation, conversation)
        self.assertEqual(message.role, 'user')
        self.assertEqual(message.content, '你好，AI助手')
    
    def test_create_vector_embedding(self):
        """测试创建向量嵌入"""
        # 创建一个成长项作为向量源
        growth_item = GrowthItem.objects.create(
            user=self.user,
            title='学习Python',
            description='掌握Python编程语言',
            item_type='goal',
            status='in_progress'
        )
        
        # 创建向量嵌入
        embedding = VectorEmbedding.objects.create(
            user=self.user,
            source_id=growth_item.item_id,
            source_type='growth_item',
            embedding=[0.1] * 1024,  # 模拟1024维向量
            content_summary='学习Python编程'
        )
        
        self.assertEqual(embedding.user, self.user)
        self.assertEqual(embedding.source_id, growth_item.item_id)
        self.assertEqual(embedding.source_type, 'growth_item')
        self.assertEqual(len(embedding.embedding), 1024)
    
    def test_create_ai_insight(self):
        """测试创建AI洞察"""
        insight = AIInsight.objects.create(
            user=self.user,
            insight_type='skill_development',
            title='Python学习进展',
            content='您在Python学习方面表现出色',
            confidence_score=0.85,
            source_data={'skills': ['Python', '编程']}
        )
        
        self.assertEqual(insight.user, self.user)
        self.assertEqual(insight.insight_type, 'skill_development')
        self.assertEqual(insight.confidence_score, 0.85)
        self.assertFalse(insight.is_read)


class AuthenticatedAITestCase(APITestCase):
    """需要认证的AI API测试基类"""
    
    def setUp(self):
        """测试前准备"""
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            username='ai_api_testuser'
        )
        
        # 生成JWT令牌
        refresh = RefreshToken.for_user(self.user)
        self.access_token = str(refresh.access_token)
        
        # 设置认证头
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.access_token}')


class SubtaskGenerationTestCase(AuthenticatedAITestCase):
    """子任务生成API测试"""
    
    @patch('apps.ai.services.ai_service.generate_subtasks')
    def test_subtask_generation_success(self, mock_generate):
        """测试子任务生成成功"""
        # 模拟AI服务返回
        mock_generate.return_value = {
            'subtasks': [
                {'title': '学习Python基础语法', 'description': '掌握变量、函数等基础概念'},
                {'title': '练习编程题目', 'description': '通过实践巩固知识'}
            ]
        }
        
        data = {
            'title': '学习Python编程',
            'description': '从零开始学习Python编程语言',
            'difficulty': 'beginner'
        }
        
        response = self.client.post('/api/ai/generate-subtasks/', data)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('subtasks', response.data)
        self.assertEqual(len(response.data['subtasks']), 2)
        mock_generate.assert_called_once()
    
    def test_subtask_generation_invalid_data(self):
        """测试子任务生成无效数据"""
        data = {
            'title': '',  # 空标题
            'description': '描述'
        }
        
        response = self.client.post('/api/ai/generate-subtasks/', data)
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)


class ChatAPITestCase(AuthenticatedAITestCase):
    """AI聊天API测试"""
    
    @patch('apps.ai.services.ai_service.chat_with_context')
    def test_chat_success(self, mock_chat):
        """测试AI聊天成功"""
        # 模拟AI服务返回
        mock_chat.return_value = {
            'response': '你好！我是你的AI助手，很高兴为你服务。',
            'conversation_id': 'test-conversation-id'
        }
        
        data = {
            'message': '你好，AI助手',
            'context_type': 'general'
        }
        
        response = self.client.post('/api/ai/chat/', data)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('response', response.data)
        self.assertIn('conversation_id', response.data)
        mock_chat.assert_called_once()
    
    def test_chat_empty_message(self):
        """测试空消息聊天"""
        data = {
            'message': '',
            'context_type': 'general'
        }
        
        response = self.client.post('/api/ai/chat/', data)
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)


class AIConversationAPITestCase(AuthenticatedAITestCase):
    """AI对话API测试"""
    
    def setUp(self):
        """测试前准备"""
        super().setUp()
        self.conversation = AIConversation.objects.create(
            user=self.user,
            title='测试对话',
            context_type='general'
        )
        
        # 添加一些消息
        AIMessage.objects.create(
            conversation=self.conversation,
            role='user',
            content='你好'
        )
        AIMessage.objects.create(
            conversation=self.conversation,
            role='assistant',
            content='你好！有什么可以帮助你的吗？'
        )
    
    def test_get_conversation_list(self):
        """测试获取对话列表"""
        response = self.client.get('/api/ai/conversations/')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]['title'], '测试对话')
    
    def test_get_conversation_detail(self):
        """测试获取对话详情"""
        url = f'/api/ai/conversations/{self.conversation.conversation_id}/'
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['title'], '测试对话')
        self.assertEqual(len(response.data['messages']), 2)
    
    def test_get_nonexistent_conversation(self):
        """测试获取不存在的对话"""
        url = '/api/ai/conversations/nonexistent-id/'
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)


class VectorEmbeddingTestCase(AuthenticatedAITestCase):
    """向量嵌入测试"""
    
    def setUp(self):
        """测试前准备"""
        super().setUp()
        self.growth_item = GrowthItem.objects.create(
            user=self.user,
            title='学习机器学习',
            description='深入学习机器学习算法和应用',
            item_type='goal',
            status='in_progress'
        )
    
    @patch('apps.ai.services.ai_service.vectorize_content')
    def test_vectorize_growth_item(self, mock_vectorize):
        """测试向量化成长项"""
        # 模拟向量化服务返回
        mock_vectorize.return_value = {
            'embedding': [0.1] * 1024,
            'content_summary': '学习机器学习算法'
        }
        
        data = {
            'source_id': str(self.growth_item.item_id),
            'source_type': 'growth_item'
        }
        
        response = self.client.post('/api/ai/vectorize/', data)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('embedding_id', response.data)
        mock_vectorize.assert_called_once()
    
    def test_vectorize_invalid_source(self):
        """测试向量化无效源"""
        data = {
            'source_id': 'invalid-id',
            'source_type': 'growth_item'
        }
        
        response = self.client.post('/api/ai/vectorize/', data)
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)


class AIServiceTestCase(TestCase):
    """AI服务测试"""
    
    def setUp(self):
        """测试前准备"""
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            username='service_testuser'
        )
    
    @patch('openai.ChatCompletion.create')
    def test_ai_service_chat(self, mock_openai):
        """测试AI服务聊天功能"""
        # 模拟OpenAI API返回
        mock_openai.return_value = MagicMock()
        mock_openai.return_value.choices = [
            MagicMock(message=MagicMock(content='这是AI的回复'))
        ]
        
        result = ai_service.simple_chat('你好，AI')
        
        self.assertIn('response', result)
        mock_openai.assert_called_once()
    
    def test_ai_service_validate_input(self):
        """测试AI服务输入验证"""
        # 测试空输入
        with self.assertRaises(ValueError):
            ai_service.simple_chat('')
        
        # 测试过长输入
        long_input = 'a' * 10000
        with self.assertRaises(ValueError):
            ai_service.simple_chat(long_input)
    
    @patch('apps.ai.services.ai_service.get_embedding')
    def test_vectorize_content(self, mock_embedding):
        """测试内容向量化"""
        # 模拟嵌入API返回
        mock_embedding.return_value = [0.1] * 1024
        
        result = ai_service.vectorize_content('测试内容')
        
        self.assertIn('embedding', result)
        self.assertEqual(len(result['embedding']), 1024)
        mock_embedding.assert_called_once()


class AIInsightTestCase(AuthenticatedAITestCase):
    """AI洞察测试"""
    
    def setUp(self):
        """测试前准备"""
        super().setUp()
        self.insight = AIInsight.objects.create(
            user=self.user,
            insight_type='skill_development',
            title='学习进展洞察',
            content='您在编程技能方面有显著提升',
            confidence_score=0.9
        )
    
    def test_get_insights_list(self):
        """测试获取洞察列表"""
        response = self.client.get('/api/ai/insights/')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]['title'], '学习进展洞察')
    
    def test_mark_insight_as_read(self):
        """测试标记洞察为已读"""
        url = f'/api/ai/insights/{self.insight.insight_id}/read/'
        response = self.client.post(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # 验证洞察已标记为已读
        self.insight.refresh_from_db()
        self.assertTrue(self.insight.is_read)


class AIIntegrationTestCase(AuthenticatedAITestCase):
    """AI功能集成测试"""
    
    def setUp(self):
        """测试前准备"""
        super().setUp()
        
        # 创建一些测试数据
        self.growth_item = GrowthItem.objects.create(
            user=self.user,
            title='完成Python项目',
            description='开发一个完整的Python Web应用',
            item_type='task',
            status='completed'
        )
    
    @patch('apps.ai.services.ai_service.generate_project_review')
    def test_project_review_generation(self, mock_review):
        """测试项目复盘生成"""
        # 模拟AI服务返回
        mock_review.return_value = {
            'review': '项目完成得很好，学到了很多新技能',
            'achievements': ['掌握了Django框架', '学会了数据库设计'],
            'improvements': ['可以加强测试覆盖', '优化代码结构']
        }
        
        data = {
            'project_id': str(self.growth_item.item_id),
            'include_subtasks': True
        }
        
        response = self.client.post('/api/ai/generate-project-review/', data)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('review', response.data)
        self.assertIn('achievements', response.data)
        mock_review.assert_called_once()
    
    @patch('apps.ai.services.ai_service.extract_skills')
    def test_skill_extraction(self, mock_extract):
        """测试技能提取"""
        # 模拟AI服务返回
        mock_extract.return_value = {
            'skills': [
                {'name': 'Python', 'proficiency': 'intermediate'},
                {'name': 'Django', 'proficiency': 'beginner'},
                {'name': 'Web开发', 'proficiency': 'intermediate'}
            ]
        }
        
        data = {
            'content': '我完成了一个Python Django项目，学会了Web开发',
            'content_type': 'project_review'
        }
        
        response = self.client.post('/api/ai/extract-skills/', data)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('skills', response.data)
        self.assertEqual(len(response.data['skills']), 3)
        mock_extract.assert_called_once()
