"""
管理命令：批量向量化用户数据
用于初始化或更新用户的向量数据库
"""
from django.core.management.base import BaseCommand, CommandError
from django.contrib.auth import get_user_model
from apps.ai.services import ai_service
from apps.growth.models import GrowthItem
from apps.journal.models import JournalEntry
import logging

logger = logging.getLogger(__name__)
User = get_user_model()


class Command(BaseCommand):
    help = '批量向量化用户数据，构建个人数字镜像'

    def add_arguments(self, parser):
        parser.add_argument(
            '--user-id',
            type=str,
            help='指定用户ID，如果不提供则处理所有用户'
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='强制重新向量化所有数据（即使已存在向量）'
        )
        parser.add_argument(
            '--type',
            choices=['growth', 'journal', 'all'],
            default='all',
            help='指定要处理的数据类型'
        )

    def handle(self, *args, **options):
        user_id = options.get('user_id')
        force = options.get('force')
        data_type = options.get('type')

        self.stdout.write(
            self.style.SUCCESS('开始批量向量化用户数据...')
        )

        try:
            if user_id:
                users = [User.objects.get(user_id=user_id)]
                self.stdout.write(f'处理指定用户: {user_id}')
            else:
                users = User.objects.all()
                self.stdout.write(f'处理所有用户，共 {users.count()} 个用户')

            total_stats = {
                'users_processed': 0,
                'growth_items_success': 0,
                'growth_items_failed': 0,
                'journal_entries_processed': 0,
                'total_vectors_created': 0
            }

            for user in users:
                self.stdout.write(f'\n处理用户: {user.email} ({user.user_id})')
                
                user_stats = {
                    'growth_items_success': 0,
                    'growth_items_failed': 0,
                    'journal_entries_processed': 0
                }

                # 处理成长项数据
                if data_type in ['growth', 'all']:
                    completed_items = GrowthItem.objects.filter(
                        user=user,
                        status='completed'
                    )
                    
                    self.stdout.write(f'  找到 {completed_items.count()} 个已完成的成长项')
                    
                    for item in completed_items:
                        try:
                            # 如果强制模式，先删除现有向量
                            if force:
                                from apps.ai.models import VectorEmbedding
                                VectorEmbedding.objects.filter(
                                    source_id=item.item_id,
                                    source_type='growth_item'
                                ).delete()
                            
                            success = ai_service.vectorize_growth_item(item)
                            if success:
                                user_stats['growth_items_success'] += 1
                                self.stdout.write(f'    ✓ 向量化成功: {item.title[:50]}...')
                            else:
                                user_stats['growth_items_failed'] += 1
                                self.stdout.write(
                                    self.style.WARNING(f'    ✗ 向量化失败: {item.title[:50]}...')
                                )
                        except Exception as e:
                            user_stats['growth_items_failed'] += 1
                            self.stdout.write(
                                self.style.ERROR(f'    ✗ 处理错误: {item.title[:50]}... - {str(e)}')
                            )

                # 处理日记数据（注意：日记是加密的，这里只是统计）
                if data_type in ['journal', 'all']:
                    journal_entries = JournalEntry.objects.filter(user=user)
                    user_stats['journal_entries_processed'] = journal_entries.count()
                    
                    if journal_entries.exists():
                        self.stdout.write(
                            self.style.WARNING(
                                f'  找到 {journal_entries.count()} 个日记条目，'
                                f'需要通过API端点进行向量化（因为内容已加密）'
                            )
                        )

                # 更新总统计
                total_stats['users_processed'] += 1
                total_stats['growth_items_success'] += user_stats['growth_items_success']
                total_stats['growth_items_failed'] += user_stats['growth_items_failed']
                total_stats['journal_entries_processed'] += user_stats['journal_entries_processed']
                total_stats['total_vectors_created'] += user_stats['growth_items_success']

                self.stdout.write(
                    f'  用户处理完成: 成功 {user_stats["growth_items_success"]} 个，'
                    f'失败 {user_stats["growth_items_failed"]} 个'
                )

            # 输出最终统计
            self.stdout.write('\n' + '='*50)
            self.stdout.write(self.style.SUCCESS('批量向量化完成！'))
            self.stdout.write(f'处理用户数: {total_stats["users_processed"]}')
            self.stdout.write(f'成功向量化成长项: {total_stats["growth_items_success"]}')
            self.stdout.write(f'失败的成长项: {total_stats["growth_items_failed"]}')
            self.stdout.write(f'发现的日记条目: {total_stats["journal_entries_processed"]}')
            self.stdout.write(f'总计创建向量: {total_stats["total_vectors_created"]}')
            
            if total_stats['journal_entries_processed'] > 0:
                self.stdout.write(
                    self.style.WARNING(
                        '\n注意: 日记条目需要通过前端API进行向量化，'
                        '因为内容在客户端加密。'
                    )
                )

        except User.DoesNotExist:
            raise CommandError(f'用户不存在: {user_id}')
        except Exception as e:
            raise CommandError(f'批量向量化失败: {str(e)}')
