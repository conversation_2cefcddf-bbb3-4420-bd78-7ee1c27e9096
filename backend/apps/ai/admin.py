"""
AI功能应用的Django Admin配置
"""
from django.contrib import admin
from .models import VectorEmbedding, AIConversation, AIMessage, AIInsight


@admin.register(VectorEmbedding)
class VectorEmbeddingAdmin(admin.ModelAdmin):
    """向量嵌入管理界面"""
    list_display = ('user', 'source_type', 'source_id', 'created_at')
    list_filter = ('source_type', 'created_at')
    search_fields = ('user__email', 'source_id')
    ordering = ('-created_at',)
    
    fieldsets = (
        ('基本信息', {
            'fields': ('user', 'source_id', 'source_type')
        }),
        ('向量数据', {
            'fields': ('source_text_hash',),
            'description': '向量嵌入数据过大，不在此显示'
        }),
        ('时间信息', {
            'fields': ('created_at',),
            'classes': ('collapse',)
        }),
    )
    
    readonly_fields = ('embedding_id', 'created_at')


class AIMessageInline(admin.TabularInline):
    """AI消息内联编辑"""
    model = AIMessage
    extra = 0
    readonly_fields = ('message_id', 'created_at')
    fields = ('role', 'content', 'created_at')


@admin.register(AIConversation)
class AIConversationAdmin(admin.ModelAdmin):
    """AI对话管理界面"""
    list_display = ('user', 'title', 'message_count', 'created_at', 'updated_at')
    list_filter = ('created_at', 'updated_at')
    search_fields = ('user__email', 'title')
    ordering = ('-updated_at',)
    
    fieldsets = (
        ('基本信息', {
            'fields': ('user', 'title')
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    readonly_fields = ('conversation_id', 'created_at', 'updated_at')
    inlines = [AIMessageInline]
    
    def message_count(self, obj):
        """显示消息数量"""
        return obj.messages.count()
    message_count.short_description = '消息数量'


@admin.register(AIMessage)
class AIMessageAdmin(admin.ModelAdmin):
    """AI消息管理界面"""
    list_display = ('conversation', 'role', 'content_short', 'created_at')
    list_filter = ('role', 'created_at')
    search_fields = ('conversation__user__email', 'content')
    ordering = ('-created_at',)
    
    fieldsets = (
        ('基本信息', {
            'fields': ('conversation', 'role', 'content')
        }),
        ('元数据', {
            'fields': ('metadata',),
            'classes': ('collapse',)
        }),
        ('时间信息', {
            'fields': ('created_at',),
            'classes': ('collapse',)
        }),
    )
    
    readonly_fields = ('message_id', 'created_at')
    
    def content_short(self, obj):
        """显示简短的消息内容"""
        return obj.content[:50] + '...' if len(obj.content) > 50 else obj.content
    content_short.short_description = '消息内容'


@admin.register(AIInsight)
class AIInsightAdmin(admin.ModelAdmin):
    """AI洞察管理界面"""
    list_display = ('user', 'insight_type', 'title', 'confidence_score', 'is_read', 'created_at')
    list_filter = ('insight_type', 'is_read', 'created_at')
    search_fields = ('user__email', 'title', 'content')
    ordering = ('-created_at',)
    
    fieldsets = (
        ('基本信息', {
            'fields': ('user', 'insight_type', 'title')
        }),
        ('洞察内容', {
            'fields': ('content',)
        }),
        ('设置', {
            'fields': ('confidence_score', 'is_read')
        }),
        ('时间信息', {
            'fields': ('created_at',),
            'classes': ('collapse',)
        }),
    )
    
    readonly_fields = ('insight_id', 'created_at')
    
    actions = ['mark_as_read', 'mark_as_unread']
    
    def mark_as_read(self, request, queryset):
        """批量标记为已读"""
        queryset.update(is_read=True)
        self.message_user(request, f'已将 {queryset.count()} 个洞察标记为已读')
    mark_as_read.short_description = '标记为已读'
    
    def mark_as_unread(self, request, queryset):
        """批量标记为未读"""
        queryset.update(is_read=False)
        self.message_user(request, f'已将 {queryset.count()} 个洞察标记为未读')
    mark_as_unread.short_description = '标记为未读'
