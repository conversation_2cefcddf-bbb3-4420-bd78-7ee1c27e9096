"""
AI功能模型
根据数据库文档中的 vector_embeddings 表设计
"""
import uuid
from django.db import models
from django.utils import timezone
from pgvector.django import VectorField


class VectorEmbedding(models.Model):
    """
    向量嵌入模型
    存储所有可被AI理解的数据向量，驱动"数字镜像"功能
    """
    SOURCE_TYPE_CHOICES = [
        ('growth_item', '成长项'),
        ('journal_entry', '日记条目'),
        ('user_value', '用户价值观'),
    ]
    
    embedding_id = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False,
        verbose_name='向量ID'
    )
    
    user = models.ForeignKey(
        'users.User',
        on_delete=models.CASCADE,
        related_name='vector_embeddings',
        verbose_name='用户'
    )
    
    source_id = models.UUIDField(
        verbose_name='来源数据ID',
        help_text='来源数据的ID (如 item_id, entry_id)'
    )
    
    source_type = models.CharField(
        max_length=50,
        choices=SOURCE_TYPE_CHOICES,
        verbose_name='来源类型'
    )
    
    embedding = VectorField(
        dimensions=1024,
        verbose_name='向量嵌入',
        help_text='文本内容的向量表示，维度取决于嵌入模型'
    )
    
    source_text_hash = models.CharField(
        max_length=64,
        verbose_name='来源文本哈希',
        help_text='来源文本的SHA-256哈希，用于检测内容变更'
    )
    
    created_at = models.DateTimeField(
        default=timezone.now,
        verbose_name='创建时间'
    )
    
    class Meta:
        db_table = 'vector_embeddings'
        verbose_name = '向量嵌入'
        verbose_name_plural = '向量嵌入'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user']),
            models.Index(fields=['source_type']),
            models.Index(fields=['source_id', 'source_type']),
        ]
        unique_together = ['source_id', 'source_type']
    
    def __str__(self):
        return f"{self.user.email} - {self.get_source_type_display()} - {self.source_id}"


class AIConversation(models.Model):
    """
    AI对话模型
    记录用户与AI伴侣的对话历史
    """
    conversation_id = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False,
        verbose_name='对话ID'
    )
    
    user = models.ForeignKey(
        'users.User',
        on_delete=models.CASCADE,
        related_name='ai_conversations',
        verbose_name='用户'
    )
    
    title = models.CharField(
        max_length=200,
        blank=True,
        verbose_name='对话标题',
        help_text='AI自动生成或用户自定义的对话标题'
    )
    
    created_at = models.DateTimeField(
        default=timezone.now,
        verbose_name='创建时间'
    )
    
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name='更新时间'
    )
    
    class Meta:
        db_table = 'ai_conversations'
        verbose_name = 'AI对话'
        verbose_name_plural = 'AI对话'
        ordering = ['-updated_at']
    
    def __str__(self):
        return f"{self.user.email} - {self.title or '未命名对话'}"


class AIMessage(models.Model):
    """
    AI消息模型
    记录对话中的每条消息
    """
    ROLE_CHOICES = [
        ('user', '用户'),
        ('assistant', 'AI助手'),
        ('system', '系统'),
    ]
    
    message_id = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False,
        verbose_name='消息ID'
    )
    
    conversation = models.ForeignKey(
        AIConversation,
        on_delete=models.CASCADE,
        related_name='messages',
        verbose_name='对话'
    )
    
    role = models.CharField(
        max_length=20,
        choices=ROLE_CHOICES,
        verbose_name='角色'
    )
    
    content = models.TextField(
        verbose_name='消息内容'
    )
    
    metadata = models.JSONField(
        default=dict,
        blank=True,
        verbose_name='元数据',
        help_text='包含模型参数、token使用量等信息'
    )
    
    created_at = models.DateTimeField(
        default=timezone.now,
        verbose_name='创建时间'
    )
    
    class Meta:
        db_table = 'ai_messages'
        verbose_name = 'AI消息'
        verbose_name_plural = 'AI消息'
        ordering = ['created_at']
    
    def __str__(self):
        return f"{self.get_role_display()} - {self.content[:50]}..."


class AIInsight(models.Model):
    """
    AI洞察模型
    存储AI对用户数据的分析洞察
    """
    INSIGHT_TYPE_CHOICES = [
        ('skill_analysis', '技能分析'),
        ('growth_pattern', '成长模式'),
        ('value_alignment', '价值观一致性'),
        ('emotional_trend', '情绪趋势'),
        ('goal_suggestion', '目标建议'),
    ]
    
    insight_id = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False,
        verbose_name='洞察ID'
    )
    
    user = models.ForeignKey(
        'users.User',
        on_delete=models.CASCADE,
        related_name='ai_insights',
        verbose_name='用户'
    )
    
    insight_type = models.CharField(
        max_length=50,
        choices=INSIGHT_TYPE_CHOICES,
        verbose_name='洞察类型'
    )
    
    title = models.CharField(
        max_length=200,
        verbose_name='洞察标题'
    )
    
    content = models.TextField(
        verbose_name='洞察内容'
    )
    
    confidence_score = models.FloatField(
        default=0.0,
        verbose_name='置信度',
        help_text='AI对这个洞察的置信度 (0-1)'
    )
    
    is_read = models.BooleanField(
        default=False,
        verbose_name='是否已读'
    )
    
    created_at = models.DateTimeField(
        default=timezone.now,
        verbose_name='创建时间'
    )
    
    class Meta:
        db_table = 'ai_insights'
        verbose_name = 'AI洞察'
        verbose_name_plural = 'AI洞察'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.user.email} - {self.get_insight_type_display()} - {self.title}"
