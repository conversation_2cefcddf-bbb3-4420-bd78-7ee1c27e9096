"""
AI功能应用视图层 - 灵境(Mentia)智能服务API接口

文件功能：
    提供AI相关功能的REST API接口，包括内容生成、智能分析、向量检索等服务

主要API端点：
    1. 项目管理AI
       - POST /ai/generate-subtasks/ - 生成项目子任务
       - POST /ai/generate-project-review/ - 生成项目复盘报告
       - POST /ai/extract-skills/ - 提取技能标签

    2. 内容生成AI
       - POST /ai/generate-title-summary/ - 生成标题和摘要
       - POST /ai/chat/ - AI伴侣对话接口

    3. 向量检索服务
       - POST /ai/vectorize-journal/ - 单个日记向量化
       - POST /ai/batch-vectorize/ - 批量内容向量化
       - POST /ai/search-memories/ - 基于向量的记忆搜索

    4. 智能洞察
       - GET /ai/insights/ - 获取AI生成的个人洞察
       - POST /ai/insights/ - 创建新的洞察记录

技术特性：
    - 异步处理：支持长时间运行的AI任务
    - 智能模型选择：根据任务复杂度自动选择最适合的AI模型
    - 错误处理：完善的异常处理和用户友好的错误响应
    - API文档：集成drf-spectacular自动生成API文档
    - 权限控制：基于JWT的用户认证和权限验证

性能优化：
    - 向量缓存：避免重复计算相同内容的向量
    - 批量处理：支持批量向量化提高效率
    - 异步调用：非阻塞的AI服务调用

作者: Mentia后端团队
创建时间: 2024
最后更新: 2025-09-05
版本: v2.0 - 新增智能模型选择和批量处理功能

TODO: 添加API调用频率限制
TODO: 实现AI服务健康检查接口
TODO: 添加AI模型性能监控
"""
import asyncio
from rest_framework import status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.views import APIView
from drf_spectacular.utils import extend_schema, OpenApiResponse
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt

from .services import ai_service
from apps.common.decorators import api_error_handler
from apps.common.query_optimizers import apply_common_optimizations, monitor_db_queries
from .serializers import (
    SubtaskGenerationSerializer,
    SubtaskGenerationResponseSerializer,
    TitleSummaryGenerationSerializer,
    TitleSummaryResponseSerializer,
    ProjectReviewSerializer,
    ProjectReviewResponseSerializer,
    SkillExtractionSerializer,
    SkillExtractionResponseSerializer,
    AIConversationSerializer,
    AIConversationListSerializer,
    AIInsightSerializer,
    AIInsightListSerializer,
    VectorizeJournalSerializer,
    BatchVectorizeSerializer,
    BatchVectorizeResponseSerializer,
    MemorySearchSerializer,
    MemorySearchResponseSerializer,
    ChatRequestSerializer,
    ChatResponseSerializer
)
from .models import AIConversation, AIInsight
from apps.growth.models import GrowthItem, Tag, UserItemTag


class SubtaskGenerationView(APIView):
    """
    任务1.1: AI驱动的开始 - 子任务生成API
    """
    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(
        summary="生成项目子任务",
        description="根据项目标题和描述，使用AI生成可执行的子任务清单",
        request=SubtaskGenerationSerializer,
        responses={
            200: OpenApiResponse(
                response=SubtaskGenerationResponseSerializer,
                description="子任务生成成功"
            ),
            400: OpenApiResponse(description="请求参数错误"),
            500: OpenApiResponse(description="AI服务错误")
        },
        tags=["AI功能"]
    )
    def post(self, request):
        serializer = SubtaskGenerationSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(
                {"error": "请求参数错误", "details": serializer.errors},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            # 获取参数
            project_title = serializer.validated_data['project_title']
            project_description = serializer.validated_data.get('project_description', '')
            parent_item_id = serializer.validated_data.get('parent_item_id')

            # 如果提供了父任务ID，先更新父任务状态
            parent_item = None
            if parent_item_id:
                try:
                    parent_item = GrowthItem.objects.get(
                        item_id=parent_item_id,
                        user=request.user
                    )
                    # 将父任务状态更新为进行中
                    parent_item.status = 'in_progress'
                    parent_item.save()
                except GrowthItem.DoesNotExist:
                    return Response(
                        {"error": "父任务不存在"},
                        status=status.HTTP_404_NOT_FOUND
                    )

            # 异步调用AI服务生成子任务
            subtasks = asyncio.run(
                ai_service.generate_subtasks(project_title, project_description)
            )

            # 如果有父任务，创建子任务记录
            created_subtasks = []
            if parent_item:
                for subtask in subtasks:
                    child_item = GrowthItem.objects.create(
                        user=request.user,
                        parent=parent_item,
                        title=subtask['title'],
                        description=subtask.get('description', ''),
                        status='future_plan',  # 子任务默认为未来计划
                        item_type='task',
                        created_by='ai'
                    )
                    created_subtasks.append({
                        'item_id': str(child_item.item_id),
                        'title': child_item.title,
                        'description': child_item.description,
                        'status': child_item.status
                    })

            response_data = {
                "subtasks": subtasks,
                "created_subtasks": created_subtasks,
                "parent_updated": parent_item is not None,
                "message": "子任务生成成功"
            }

            return Response(response_data, status=status.HTTP_200_OK)

        except Exception as e:
            return Response(
                {"error": "AI服务暂时不可用", "details": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class TitleSummaryGenerationView(APIView):
    """
    任务1.2: AI辅助标题与摘要生成API
    """
    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(
        summary="生成文章标题和摘要",
        description="根据文章内容，使用AI生成合适的标题和摘要",
        request=TitleSummaryGenerationSerializer,
        responses={
            200: OpenApiResponse(
                response=TitleSummaryResponseSerializer,
                description="标题和摘要生成成功"
            ),
            400: OpenApiResponse(description="请求参数错误"),
            500: OpenApiResponse(description="AI服务错误")
        },
        tags=["AI功能"]
    )
    def post(self, request):
        serializer = TitleSummaryGenerationSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(
                {"error": "请求参数错误", "details": serializer.errors},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            # 异步调用AI服务
            content = serializer.validated_data['content']
            content_type = serializer.validated_data['content_type']

            # 由于Django视图不支持async，我们使用asyncio.run
            result = asyncio.run(
                ai_service.generate_title_and_summary(content, content_type)
            )

            response_data = {
                "title": result["title"],
                "summary": result["summary"],
                "message": "标题和摘要生成成功"
            }

            return Response(response_data, status=status.HTTP_200_OK)

        except Exception as e:
            return Response(
                {"error": "AI服务暂时不可用", "details": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class ProjectReviewView(APIView):
    """
    任务2.1: AI驱动的复盘 - 项目复盘API
    """
    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(
        summary="生成项目复盘报告",
        description="当用户完成一个项目后，基于项目和子任务信息生成AI复盘报告",
        request=ProjectReviewSerializer,
        responses={
            200: OpenApiResponse(
                response=ProjectReviewResponseSerializer,
                description="复盘报告生成成功"
            ),
            400: OpenApiResponse(description="请求参数错误"),
            404: OpenApiResponse(description="项目不存在"),
            500: OpenApiResponse(description="AI服务错误")
        },
        tags=["AI功能"]
    )
    def post(self, request):
        serializer = ProjectReviewSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(
                {"error": "请求参数错误", "details": serializer.errors},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            project_id = serializer.validated_data['project_id']

            # 获取项目信息
            try:
                project = GrowthItem.objects.get(
                    item_id=project_id,
                    user=request.user,
                    item_type='goal',
                    status='completed'
                )
            except GrowthItem.DoesNotExist:
                return Response(
                    {"error": "项目不存在或未完成"},
                    status=status.HTTP_404_NOT_FOUND
                )

            # 获取所有子任务
            subtasks = project.children.filter(status='completed').values(
                'title', 'description'
            )

            # 构建项目数据
            project_data = {
                'title': project.title,
                'description': project.description,
                'subtasks': list(subtasks)
            }

            # 异步调用AI服务生成复盘报告
            result = asyncio.run(
                ai_service.generate_project_review(project_data)
            )

            # 更新项目的AI总结
            project.ai_summary = result['review_summary']
            project.save()

            response_data = {
                "review_summary": result["review_summary"],
                "key_achievements": result["key_achievements"],
                "skills_gained": result["skills_gained"],
                "improvement_areas": result["improvement_areas"],
                "message": "项目复盘报告生成成功"
            }

            return Response(response_data, status=status.HTTP_200_OK)

        except Exception as e:
            return Response(
                {"error": "AI服务暂时不可用", "details": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class SkillExtractionView(APIView):
    """
    任务2.2: 技能标签提取与图谱生成 - 技能提取API
    """
    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(
        summary="从复盘报告中提取技能标签",
        description="分析复盘报告内容，提取技能标签并更新用户技能图谱",
        request=SkillExtractionSerializer,
        responses={
            200: OpenApiResponse(
                response=SkillExtractionResponseSerializer,
                description="技能提取成功"
            ),
            400: OpenApiResponse(description="请求参数错误"),
            500: OpenApiResponse(description="AI服务错误")
        },
        tags=["AI功能"]
    )
    def post(self, request):
        serializer = SkillExtractionSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(
                {"error": "请求参数错误", "details": serializer.errors},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            review_content = serializer.validated_data['review_content']
            project_title = serializer.validated_data.get('project_title', '')

            # 异步调用AI服务提取技能
            result = asyncio.run(
                ai_service.extract_skills_from_review(review_content, project_title)
            )

            extracted_skills = result.get('skills', [])
            updated_tags = []

            # 将提取的技能转换为标签并关联到用户
            for skill_name in extracted_skills:
                # 获取或创建标签
                tag, created = Tag.objects.get_or_create(
                    name=skill_name,
                    defaults={'category': '技能'}
                )

                # 记录标签信息
                tag_info = {
                    'tag_id': tag.tag_id,
                    'name': tag.name,
                    'category': tag.category,
                    'is_new': created
                }
                updated_tags.append(tag_info)

            response_data = {
                "extracted_skills": extracted_skills,
                "updated_tags": updated_tags,
                "message": f"成功提取 {len(extracted_skills)} 个技能标签"
            }

            return Response(response_data, status=status.HTTP_200_OK)

        except Exception as e:
            return Response(
                {"error": "AI服务暂时不可用", "details": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )



class ChatView(APIView):
    """
    AI聊天接口
    供前端AI伴侣页面调用
    """
    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(
        summary="AI聊天",
        description="基于用户上下文的AI聊天回复",
        request=ChatRequestSerializer,
        responses={200: ChatResponseSerializer},
        tags=["AI功能"]
    )
    def post(self, request):
        serializer = ChatRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return Response({"error": "请求参数错误", "details": serializer.errors}, status=status.HTTP_400_BAD_REQUEST)

        try:
            client = ai_service._get_client()
            if not client:
                return Response({"content": "抱歉，AI服务暂不可用。"}, status=status.HTTP_200_OK)

            messages = serializer.validated_data['messages']
            temperature = serializer.validated_data.get('temperature', 0.7)
            max_tokens = serializer.validated_data.get('max_tokens', 500)

            # 智能选择模型 - 聊天任务使用plus模型平衡性能和成本
            message_content_length = sum(len(msg.get('content', '')) for msg in messages)
            selected_model = ai_service._select_model_by_complexity('chat', message_content_length, max_tokens)

            response = client.chat.completions.create(
                model=selected_model,
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens
            )

            content = response.choices[0].message.content
            return Response({"content": content}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"content": f"抱歉，AI服务异常：{str(e)}"}, status=status.HTTP_200_OK)


class AIConversationListView(APIView):
    """AI对话列表视图"""
    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(
        summary="获取AI对话列表",
        description="获取用户的AI对话历史列表",
        responses={200: AIConversationListSerializer(many=True)},
        tags=["AI功能"]
    )
    @monitor_db_queries
    def get(self, request):
        """获取用户的AI对话列表 - 优化数据库查询"""
        # 使用查询优化减少数据库查询次数
        conversations = AIConversation.objects.filter(
            user=request.user
        ).select_related('user').prefetch_related('messages').order_by('-updated_at')

        serializer = AIConversationListSerializer(conversations, many=True)
        return Response(serializer.data)


class AIConversationDetailView(APIView):
    """AI对话详情视图"""
    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(
        summary="获取AI对话详情",
        description="获取指定AI对话的详细信息，包括所有消息",
        responses={200: AIConversationSerializer},
        tags=["AI功能"]
    )
    @monitor_db_queries
    def get(self, request, conversation_id):
        """获取AI对话详情 - 优化数据库查询"""
        try:
            # 使用查询优化预加载相关数据
            conversation = AIConversation.objects.select_related('user').prefetch_related(
                'messages'
            ).get(
                conversation_id=conversation_id,
                user=request.user
            )
            serializer = AIConversationSerializer(conversation)
            return Response(serializer.data)
        except AIConversation.DoesNotExist:
            return Response(
                {"error": "对话不存在"},
                status=status.HTTP_404_NOT_FOUND
            )


class AIInsightListView(APIView):
    """AI洞察列表视图"""
    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(
        summary="获取AI洞察列表",
        description="获取用户的AI洞察列表",
        responses={200: AIInsightListSerializer(many=True)},
        tags=["AI功能"]
    )
    def get(self, request):
        insights = AIInsight.objects.filter(
            user=request.user
        ).order_by('-created_at')

        # 支持按类型过滤
        insight_type = request.query_params.get('type')
        if insight_type:
            insights = insights.filter(insight_type=insight_type)

        # 支持按已读状态过滤
        is_read = request.query_params.get('is_read')
        if is_read is not None:
            insights = insights.filter(is_read=is_read.lower() == 'true')

        serializer = AIInsightListSerializer(insights, many=True)
        return Response(serializer.data)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
@api_error_handler
@extend_schema(
    summary="标记洞察为已读",
    description="将指定的AI洞察标记为已读状态",
    responses={200: {"message": "标记成功"}},
    tags=["AI功能"]
)
def mark_insight_read(request, insight_id):
    """标记AI洞察为已读"""
    try:
        insight = AIInsight.objects.get(
            insight_id=insight_id,
            user=request.user
        )
        insight.is_read = True
        insight.save(update_fields=['is_read'])

        return Response({"message": "标记成功"})
    except AIInsight.DoesNotExist:
        return Response(
            {"error": "洞察不存在"},
            status=status.HTTP_404_NOT_FOUND
        )


# ==================== 向量化相关API ====================

@extend_schema(
    operation_id="vectorize_journal_entry",
    summary="向量化日记条目",
    description="将解密后的日记内容进行向量化处理，用于AI伴侣功能",
    request=VectorizeJournalSerializer,
    responses={
        200: OpenApiResponse(description="向量化成功"),
        400: OpenApiResponse(description="请求参数错误"),
        404: OpenApiResponse(description="日记条目不存在"),
        500: OpenApiResponse(description="向量化失败")
    },
    tags=["AI - 向量化"]
)
@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def vectorize_journal_entry(request):
    """向量化日记条目"""
    serializer = VectorizeJournalSerializer(data=request.data)
    if not serializer.is_valid():
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    try:
        from apps.journal.models import JournalEntry

        entry_id = serializer.validated_data['entry_id']
        decrypted_content = serializer.validated_data['decrypted_content']

        # 验证日记条目存在且属于当前用户
        entry = JournalEntry.objects.get(
            entry_id=entry_id,
            user=request.user
        )

        # 执行向量化
        success = ai_service.vectorize_journal_entry(entry, decrypted_content)

        if success:
            return Response({
                "message": "日记向量化成功",
                "entry_id": str(entry_id)
            })
        else:
            return Response(
                {"error": "向量化失败，请稍后重试"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    except JournalEntry.DoesNotExist:
        return Response(
            {"error": "日记条目不存在"},
            status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        return Response(
            {"error": f"处理失败: {str(e)}"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@extend_schema(
    operation_id="batch_vectorize_user_data",
    summary="批量向量化用户数据",
    description="批量向量化用户的所有已完成项目数据，用于构建个人数字镜像",
    request=BatchVectorizeSerializer,
    responses={
        200: BatchVectorizeResponseSerializer,
        400: OpenApiResponse(description="请求参数错误"),
        500: OpenApiResponse(description="批量向量化失败")
    },
    tags=["AI - 向量化"]
)
@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def batch_vectorize_user_data(request):
    """批量向量化用户数据"""
    serializer = BatchVectorizeSerializer(data=request.data)
    if not serializer.is_valid():
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    try:
        # 使用当前用户ID
        user_id = str(request.user.user_id)

        # 执行批量向量化
        results = ai_service.batch_vectorize_user_data(user_id)

        if 'error' in results:
            return Response(
                {"error": results['error']},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

        response_data = {
            **results,
            "message": "批量向量化完成"
        }

        return Response(response_data)

    except Exception as e:
        return Response(
            {"error": f"批量向量化失败: {str(e)}"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@extend_schema(
    operation_id="search_memories",
    summary="搜索相似记忆",
    description="基于向量相似性搜索用户的相关记忆片段，用于AI伴侣对话",
    request=MemorySearchSerializer,
    responses={
        200: MemorySearchResponseSerializer,
        400: OpenApiResponse(description="请求参数错误"),
        500: OpenApiResponse(description="搜索失败")
    },
    tags=["AI - 向量化"]
)
@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def search_memories(request):
    """搜索相似记忆"""
    serializer = MemorySearchSerializer(data=request.data)
    if not serializer.is_valid():
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    try:
        query = serializer.validated_data['query']
        limit = serializer.validated_data['limit']
        user_id = str(request.user.user_id)

        # 执行记忆搜索
        memories = ai_service.search_similar_memories(user_id, query, limit)

        response_data = {
            "memories": memories,
            "query": query,
            "total_found": len(memories),
            "message": "记忆搜索完成"
        }

        return Response(response_data)

    except Exception as e:
        return Response(
            {"error": f"记忆搜索失败: {str(e)}"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
