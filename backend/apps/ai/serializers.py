"""
AI功能应用的序列化器
"""
from rest_framework import serializers
from .models import VectorEmbedding, AIConversation, AIMessage, AIInsight


class SubtaskGenerationSerializer(serializers.Serializer):
    """子任务生成请求序列化器"""
    project_title = serializers.CharField(
        max_length=200,
        help_text="项目标题"
    )
    project_description = serializers.CharField(
        max_length=1000,
        required=False,
        allow_blank=True,
        help_text="项目描述（可选）"
    )
    parent_item_id = serializers.UUIDField(
        required=False,
        help_text="父任务ID（可选，如果提供则会更新父任务状态并创建子任务）"
    )


class SubtaskSerializer(serializers.Serializer):
    """子任务序列化器"""
    title = serializers.CharField(max_length=200)
    description = serializers.CharField(max_length=500, allow_blank=True)


class SubtaskGenerationResponseSerializer(serializers.Serializer):
    """子任务生成响应序列化器"""
    subtasks = SubtaskSerializer(many=True)
    message = serializers.CharField(default="子任务生成成功")


class TitleSummaryGenerationSerializer(serializers.Serializer):
    """标题摘要生成请求序列化器"""
    content = serializers.CharField(
        max_length=10000,
        help_text="文章内容"
    )
    content_type = serializers.ChoiceField(
        choices=[('blog', '博客'), ('journal', '日记'), ('note', '笔记')],
        default='blog',
        help_text="内容类型"
    )


class TitleSummaryResponseSerializer(serializers.Serializer):
    """标题摘要生成响应序列化器"""
    title = serializers.CharField(max_length=200)
    summary = serializers.CharField(max_length=1000)
    message = serializers.CharField(default="标题和摘要生成成功")



class ChatMessageSerializer(serializers.Serializer):
    """AI聊天消息项序列化器"""
    role = serializers.ChoiceField(choices=['system', 'user', 'assistant'])
    content = serializers.CharField(max_length=4000)


class ChatRequestSerializer(serializers.Serializer):
    """AI聊天请求序列化器"""
    messages = ChatMessageSerializer(many=True)
    temperature = serializers.FloatField(required=False, default=0.7)
    max_tokens = serializers.IntegerField(required=False, default=500)


class ChatResponseSerializer(serializers.Serializer):
    """AI聊天响应序列化器"""
    content = serializers.CharField()


class VectorEmbeddingSerializer(serializers.ModelSerializer):
    """向量嵌入序列化器"""
    class Meta:
        model = VectorEmbedding
        fields = [
            'embedding_id', 'source_id', 'source_type',
            'source_text_hash', 'created_at'
        ]
        read_only_fields = ['embedding_id', 'created_at']


class AIMessageSerializer(serializers.ModelSerializer):
    """AI消息序列化器"""
    class Meta:
        model = AIMessage
        fields = [
            'message_id', 'role', 'content', 'metadata', 'created_at'
        ]
        read_only_fields = ['message_id', 'created_at']


class AIConversationSerializer(serializers.ModelSerializer):
    """AI对话序列化器"""
    messages = AIMessageSerializer(many=True, read_only=True)
    message_count = serializers.SerializerMethodField()

    class Meta:
        model = AIConversation
        fields = [
            'conversation_id', 'title', 'message_count',
            'messages', 'created_at', 'updated_at'
        ]
        read_only_fields = ['conversation_id', 'created_at', 'updated_at']

    def get_message_count(self, obj):
        return obj.messages.count()


class AIConversationListSerializer(serializers.ModelSerializer):
    """AI对话列表序列化器（不包含消息详情）"""
    message_count = serializers.SerializerMethodField()
    last_message_at = serializers.SerializerMethodField()

    class Meta:
        model = AIConversation
        fields = [
            'conversation_id', 'title', 'message_count',
            'last_message_at', 'created_at', 'updated_at'
        ]
        read_only_fields = ['conversation_id', 'created_at', 'updated_at']

    def get_message_count(self, obj):
        return obj.messages.count()

    def get_last_message_at(self, obj):
        last_message = obj.messages.last()
        return last_message.created_at if last_message else obj.created_at


class AIInsightSerializer(serializers.ModelSerializer):
    """AI洞察序列化器"""
    insight_type_display = serializers.CharField(
        source='get_insight_type_display',
        read_only=True
    )

    class Meta:
        model = AIInsight
        fields = [
            'insight_id', 'insight_type', 'insight_type_display',
            'title', 'content', 'confidence_score', 'is_read', 'created_at'
        ]
        read_only_fields = ['insight_id', 'created_at']


class AIInsightListSerializer(serializers.ModelSerializer):
    """AI洞察列表序列化器（简化版）"""
    insight_type_display = serializers.CharField(
        source='get_insight_type_display',
        read_only=True
    )

    class Meta:
        model = AIInsight
        fields = [
            'insight_id', 'insight_type', 'insight_type_display',
            'title', 'confidence_score', 'is_read', 'created_at'
        ]
        read_only_fields = ['insight_id', 'created_at']


class ProjectReviewSerializer(serializers.Serializer):
    """项目复盘请求序列化器"""
    project_id = serializers.UUIDField(
        help_text="项目ID（目标的item_id）"
    )


class ProjectReviewResponseSerializer(serializers.Serializer):
    """项目复盘响应序列化器"""
    review_summary = serializers.CharField(help_text="复盘总结")
    key_achievements = serializers.ListField(
        child=serializers.CharField(),
        help_text="关键成果"
    )
    skills_gained = serializers.ListField(
        child=serializers.CharField(),
        help_text="获得的技能"
    )
    improvement_areas = serializers.ListField(
        child=serializers.CharField(),
        help_text="改进建议"
    )
    message = serializers.CharField(help_text="响应消息")


class SkillExtractionSerializer(serializers.Serializer):
    """技能提取请求序列化器"""
    review_content = serializers.CharField(
        max_length=5000,
        help_text="复盘报告内容"
    )
    project_title = serializers.CharField(
        max_length=200,
        required=False,
        help_text="项目标题（可选，用于上下文）"
    )


class SkillExtractionResponseSerializer(serializers.Serializer):
    """技能提取响应序列化器"""
    extracted_skills = serializers.ListField(
        child=serializers.CharField(),
        help_text="提取的技能标签"
    )
    updated_tags = serializers.ListField(
        child=serializers.DictField(),
        help_text="更新的标签信息"
    )
    message = serializers.CharField(help_text="响应消息")


# ==================== 向量化相关序列化器 ====================

class VectorizeJournalSerializer(serializers.Serializer):
    """日记向量化请求序列化器"""
    entry_id = serializers.UUIDField(help_text="日记条目ID")
    decrypted_content = serializers.CharField(
        max_length=50000,
        help_text="解密后的日记内容"
    )


class BatchVectorizeSerializer(serializers.Serializer):
    """批量向量化请求序列化器"""
    user_id = serializers.UUIDField(
        required=False,
        help_text="用户ID（可选，默认为当前用户）"
    )


class BatchVectorizeResponseSerializer(serializers.Serializer):
    """批量向量化响应序列化器"""
    growth_items = serializers.DictField(help_text="成长项处理结果")
    journal_entries = serializers.DictField(help_text="日记条目处理结果")
    total_processed = serializers.IntegerField(help_text="总处理数量")
    message = serializers.CharField(default="批量向量化完成")


class MemorySearchSerializer(serializers.Serializer):
    """记忆搜索请求序列化器"""
    query = serializers.CharField(
        max_length=1000,
        help_text="搜索查询文本"
    )
    limit = serializers.IntegerField(
        default=5,
        min_value=1,
        max_value=20,
        help_text="返回结果数量限制"
    )


class MemoryItemSerializer(serializers.Serializer):
    """记忆项序列化器"""
    type = serializers.CharField(help_text="记忆类型")
    id = serializers.CharField(help_text="记忆ID")
    title = serializers.CharField(required=False, help_text="标题")
    description = serializers.CharField(required=False, help_text="描述")
    ai_summary = serializers.CharField(required=False, help_text="AI总结")
    date = serializers.CharField(required=False, help_text="日期")
    completed_at = serializers.CharField(required=False, help_text="完成时间")
    created_at = serializers.CharField(required=False, help_text="创建时间")
    similarity = serializers.FloatField(help_text="相似度分数")
    note = serializers.CharField(required=False, help_text="备注")


class MemorySearchResponseSerializer(serializers.Serializer):
    """记忆搜索响应序列化器"""
    memories = MemoryItemSerializer(many=True, help_text="搜索到的记忆片段")
    query = serializers.CharField(help_text="搜索查询")
    total_found = serializers.IntegerField(help_text="找到的记忆数量")
    message = serializers.CharField(default="记忆搜索完成")
