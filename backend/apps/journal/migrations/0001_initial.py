# Generated by Django 5.0.7 on 2025-08-28 08:23

import django.utils.timezone
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="JournalEntry",
            fields=[
                (
                    "entry_id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        verbose_name="日记ID",
                    ),
                ),
                (
                    "entry_date",
                    models.DateField(
                        default=django.utils.timezone.now,
                        help_text="日记对应的日期",
                        verbose_name="日记日期",
                    ),
                ),
                (
                    "title_encrypted",
                    models.TextField(
                        help_text="客户端加密后的标题密文 (Base64)",
                        verbose_name="加密标题",
                    ),
                ),
                (
                    "content_encrypted",
                    models.TextField(
                        help_text="客户端加密后的正文密文 (Base64)",
                        verbose_name="加密内容",
                    ),
                ),
                (
                    "metadata",
                    models.JSONField(
                        blank=True,
                        default=dict,
                        help_text="可选的元数据，如地点、天气、心情等",
                        verbose_name="元数据",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, verbose_name="创建时间"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
            ],
            options={
                "verbose_name": "日记条目",
                "verbose_name_plural": "日记条目",
                "db_table": "journal_entries",
                "ordering": ["-entry_date", "-created_at"],
            },
        ),
        migrations.CreateModel(
            name="JournalPrompt",
            fields=[
                (
                    "prompt_id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        verbose_name="提示ID",
                    ),
                ),
                (
                    "category",
                    models.CharField(
                        help_text='如 "自我反思", "目标回顾", "情绪管理" 等',
                        max_length=100,
                        verbose_name="提示分类",
                    ),
                ),
                ("prompt_text", models.TextField(verbose_name="提示内容")),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="是否激活"),
                ),
                (
                    "weight",
                    models.PositiveIntegerField(
                        default=1,
                        help_text="用于随机选择提示时的权重",
                        verbose_name="权重",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, verbose_name="创建时间"
                    ),
                ),
            ],
            options={
                "verbose_name": "日记提示",
                "verbose_name_plural": "日记提示",
                "db_table": "journal_prompts",
                "ordering": ["category", "-created_at"],
            },
        ),
        migrations.CreateModel(
            name="JournalTemplate",
            fields=[
                (
                    "template_id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        verbose_name="模板ID",
                    ),
                ),
                ("name", models.CharField(max_length=200, verbose_name="模板名称")),
                ("description", models.TextField(blank=True, verbose_name="模板描述")),
                (
                    "template_content",
                    models.TextField(
                        help_text="包含提示问题和格式的模板内容",
                        verbose_name="模板内容",
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="是否激活"),
                ),
                (
                    "is_system_template",
                    models.BooleanField(default=False, verbose_name="是否为系统模板"),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, verbose_name="创建时间"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
            ],
            options={
                "verbose_name": "日记模板",
                "verbose_name_plural": "日记模板",
                "db_table": "journal_templates",
                "ordering": ["is_system_template", "-created_at"],
            },
        ),
    ]
