# Generated by Django 5.0.7 on 2025-08-28 08:23

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("journal", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name="journalentry",
            name="user",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="journal_entries",
                to=settings.AUTH_USER_MODEL,
                verbose_name="用户",
            ),
        ),
        migrations.AddField(
            model_name="journaltemplate",
            name="user",
            field=models.ForeignKey(
                blank=True,
                help_text="NULL表示系统默认模板",
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="journal_templates",
                to=settings.AUTH_USER_MODEL,
                verbose_name="用户",
            ),
        ),
        migrations.AddIndex(
            model_name="journalentry",
            index=models.Index(
                fields=["user", "entry_date"], name="journal_ent_user_id_a328f3_idx"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="journalentry",
            unique_together={("user", "entry_date")},
        ),
    ]
