"""
心灵私域模型
根据数据库文档中的 journal_entries 表设计
"""
import uuid
from django.db import models
from django.utils import timezone


class JournalEntry(models.Model):
    """
    日记条目模型
    存储用户的私密思考和反思，采用客户端加密
    """
    entry_id = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False,
        verbose_name='日记ID'
    )
    
    user = models.ForeignKey(
        'users.User',
        on_delete=models.CASCADE,
        related_name='journal_entries',
        verbose_name='用户'
    )
    
    entry_date = models.DateField(
        default=timezone.now,
        verbose_name='日记日期',
        help_text='日记对应的日期'
    )
    
    title_encrypted = models.TextField(
        verbose_name='加密标题',
        help_text='客户端加密后的标题密文 (Base64)'
    )
    
    content_encrypted = models.TextField(
        verbose_name='加密内容',
        help_text='客户端加密后的正文密文 (Base64)'
    )
    
    metadata = models.JSONField(
        default=dict,
        blank=True,
        verbose_name='元数据',
        help_text='可选的元数据，如地点、天气、心情等'
    )
    
    created_at = models.DateTimeField(
        default=timezone.now,
        verbose_name='创建时间'
    )
    
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name='更新时间'
    )
    
    class Meta:
        db_table = 'journal_entries'
        verbose_name = '日记条目'
        verbose_name_plural = '日记条目'
        ordering = ['-entry_date', '-created_at']
        indexes = [
            models.Index(fields=['user', 'entry_date']),
        ]
        unique_together = ['user', 'entry_date']  # 每个用户每天只能有一篇日记
    
    def __str__(self):
        return f"{self.user.email} - {self.entry_date}"
    
    @property
    def has_content(self):
        """检查是否有内容"""
        return bool(self.title_encrypted and self.content_encrypted)
    
    def get_metadata_value(self, key, default=None):
        """获取元数据中的特定值"""
        return self.metadata.get(key, default)
    
    def set_metadata_value(self, key, value):
        """设置元数据中的特定值"""
        if not isinstance(self.metadata, dict):
            self.metadata = {}
        self.metadata[key] = value


class JournalTemplate(models.Model):
    """
    日记模板模型
    为用户提供日记写作的引导和模板
    """
    template_id = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False,
        verbose_name='模板ID'
    )
    
    user = models.ForeignKey(
        'users.User',
        on_delete=models.CASCADE,
        related_name='journal_templates',
        verbose_name='用户',
        null=True,
        blank=True,
        help_text='NULL表示系统默认模板'
    )
    
    name = models.CharField(
        max_length=200,
        verbose_name='模板名称'
    )
    
    description = models.TextField(
        blank=True,
        verbose_name='模板描述'
    )
    
    template_content = models.TextField(
        verbose_name='模板内容',
        help_text='包含提示问题和格式的模板内容'
    )
    
    is_active = models.BooleanField(
        default=True,
        verbose_name='是否激活'
    )
    
    is_system_template = models.BooleanField(
        default=False,
        verbose_name='是否为系统模板'
    )
    
    created_at = models.DateTimeField(
        default=timezone.now,
        verbose_name='创建时间'
    )
    
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name='更新时间'
    )
    
    class Meta:
        db_table = 'journal_templates'
        verbose_name = '日记模板'
        verbose_name_plural = '日记模板'
        ordering = ['is_system_template', '-created_at']
    
    def __str__(self):
        prefix = "系统" if self.is_system_template else "用户"
        return f"{prefix}模板 - {self.name}"


class JournalPrompt(models.Model):
    """
    日记提示模型
    为用户提供每日的反思提示
    """
    prompt_id = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False,
        verbose_name='提示ID'
    )
    
    category = models.CharField(
        max_length=100,
        verbose_name='提示分类',
        help_text='如 "自我反思", "目标回顾", "情绪管理" 等'
    )
    
    prompt_text = models.TextField(
        verbose_name='提示内容'
    )
    
    is_active = models.BooleanField(
        default=True,
        verbose_name='是否激活'
    )
    
    weight = models.PositiveIntegerField(
        default=1,
        verbose_name='权重',
        help_text='用于随机选择提示时的权重'
    )
    
    created_at = models.DateTimeField(
        default=timezone.now,
        verbose_name='创建时间'
    )
    
    class Meta:
        db_table = 'journal_prompts'
        verbose_name = '日记提示'
        verbose_name_plural = '日记提示'
        ordering = ['category', '-created_at']
    
    def __str__(self):
        return f"{self.category} - {self.prompt_text[:50]}..."
