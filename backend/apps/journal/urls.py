"""
心灵私域应用的URL配置
"""
from django.urls import path

app_name = 'journal'

urlpatterns = [
    # 日记相关API将在后续版本中实现
    # path('entries/', views.JournalEntryListView.as_view(), name='entry_list_create'),
    # path('entries/<uuid:pk>/', views.JournalEntryDetailView.as_view(), name='entry_detail'),
    # path('templates/', views.JournalTemplateListView.as_view(), name='template_list'),
    # path('prompts/', views.JournalPromptListView.as_view(), name='prompt_list'),
]
