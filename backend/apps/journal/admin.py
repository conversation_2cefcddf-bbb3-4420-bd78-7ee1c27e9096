"""
心灵私域应用的Django Admin配置
"""
from django.contrib import admin
from .models import JournalEntry, JournalTemplate, JournalPrompt


@admin.register(JournalEntry)
class JournalEntryAdmin(admin.ModelAdmin):
    """日记条目管理界面"""
    list_display = ('user', 'entry_date', 'has_content', 'created_at')
    list_filter = ('entry_date', 'created_at')
    search_fields = ('user__email',)
    ordering = ('-entry_date', '-created_at')
    
    fieldsets = (
        ('基本信息', {
            'fields': ('user', 'entry_date')
        }),
        ('加密内容', {
            'fields': ('title_encrypted', 'content_encrypted'),
            'description': '注意：这些是加密后的内容，无法直接阅读'
        }),
        ('元数据', {
            'fields': ('metadata',),
            'classes': ('collapse',)
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    readonly_fields = ('entry_id', 'created_at', 'updated_at')
    
    def has_content(self, obj):
        """显示是否有内容"""
        return obj.has_content
    has_content.boolean = True
    has_content.short_description = '有内容'


@admin.register(JournalTemplate)
class JournalTemplateAdmin(admin.ModelAdmin):
    """日记模板管理界面"""
    list_display = ('name', 'user', 'is_system_template', 'is_active', 'created_at')
    list_filter = ('is_system_template', 'is_active', 'created_at')
    search_fields = ('name', 'description', 'user__email')
    ordering = ('is_system_template', '-created_at')
    
    fieldsets = (
        ('基本信息', {
            'fields': ('user', 'name', 'description')
        }),
        ('模板内容', {
            'fields': ('template_content',)
        }),
        ('设置', {
            'fields': ('is_active', 'is_system_template')
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    readonly_fields = ('template_id', 'created_at', 'updated_at')


@admin.register(JournalPrompt)
class JournalPromptAdmin(admin.ModelAdmin):
    """日记提示管理界面"""
    list_display = ('category', 'prompt_text_short', 'weight', 'is_active', 'created_at')
    list_filter = ('category', 'is_active', 'created_at')
    search_fields = ('category', 'prompt_text')
    ordering = ('category', '-created_at')
    
    fieldsets = (
        ('基本信息', {
            'fields': ('category', 'prompt_text')
        }),
        ('设置', {
            'fields': ('weight', 'is_active')
        }),
        ('时间信息', {
            'fields': ('created_at',),
            'classes': ('collapse',)
        }),
    )
    
    readonly_fields = ('prompt_id', 'created_at')
    
    def prompt_text_short(self, obj):
        """显示简短的提示内容"""
        return obj.prompt_text[:50] + '...' if len(obj.prompt_text) > 50 else obj.prompt_text
    prompt_text_short.short_description = '提示内容'
