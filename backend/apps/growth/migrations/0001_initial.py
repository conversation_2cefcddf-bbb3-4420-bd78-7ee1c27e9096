# Generated by Django 5.0.7 on 2025-08-28 08:23

import django.db.models.deletion
import django.utils.timezone
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="Tag",
            fields=[
                (
                    "tag_id",
                    models.BigAutoField(
                        primary_key=True, serialize=False, verbose_name="标签ID"
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        help_text='如 "Python", "项目管理" 等',
                        max_length=100,
                        unique=True,
                        verbose_name="标签名称",
                    ),
                ),
                (
                    "category",
                    models.CharField(
                        help_text='如 "技能", "知识领域", "软技能" 等',
                        max_length=100,
                        verbose_name="标签分类",
                    ),
                ),
            ],
            options={
                "verbose_name": "标签",
                "verbose_name_plural": "标签",
                "db_table": "tags",
                "ordering": ["category", "name"],
            },
        ),
        migrations.CreateModel(
            name="UserItemTag",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
            ],
            options={
                "verbose_name": "成长项标签",
                "verbose_name_plural": "成长项标签",
                "db_table": "user_item_tags",
            },
        ),
        migrations.CreateModel(
            name="GrowthItem",
            fields=[
                (
                    "item_id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        verbose_name="成长项ID",
                    ),
                ),
                ("title", models.CharField(max_length=500, verbose_name="标题")),
                ("description", models.TextField(blank=True, verbose_name="详细描述")),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("future_plan", "未来计划"),
                            ("in_progress", "正在进行"),
                            ("completed", "已完成"),
                        ],
                        max_length=50,
                        verbose_name="状态",
                    ),
                ),
                (
                    "item_type",
                    models.CharField(
                        choices=[("goal", "目标"), ("task", "任务")],
                        max_length=50,
                        verbose_name="类型",
                    ),
                ),
                (
                    "created_by",
                    models.CharField(
                        choices=[("user", "用户创建"), ("ai", "AI创建")],
                        default="user",
                        max_length=50,
                        verbose_name="创建者",
                    ),
                ),
                (
                    "ai_summary",
                    models.TextField(
                        blank=True,
                        help_text="目标完成时，由AI生成的成果总结",
                        verbose_name="AI总结",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, verbose_name="创建时间"
                    ),
                ),
                (
                    "started_at",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="开始时间"
                    ),
                ),
                (
                    "completed_at",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="完成时间"
                    ),
                ),
                (
                    "parent",
                    models.ForeignKey(
                        blank=True,
                        help_text="NULL表示为顶级目标",
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="children",
                        to="growth.growthitem",
                        verbose_name="父项",
                    ),
                ),
            ],
            options={
                "verbose_name": "成长项",
                "verbose_name_plural": "成长项",
                "db_table": "growth_items",
                "ordering": ["-created_at"],
            },
        ),
    ]
