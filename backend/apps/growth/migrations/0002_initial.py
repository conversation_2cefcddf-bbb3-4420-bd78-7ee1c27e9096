# Generated by Django 5.0.7 on 2025-08-28 08:23

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("growth", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name="growthitem",
            name="user",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="growth_items",
                to=settings.AUTH_USER_MODEL,
                verbose_name="用户",
            ),
        ),
        migrations.AddField(
            model_name="useritemtag",
            name="item",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                to="growth.growthitem",
                verbose_name="成长项",
            ),
        ),
        migrations.AddField(
            model_name="useritemtag",
            name="tag",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                to="growth.tag",
                verbose_name="标签",
            ),
        ),
        migrations.AddField(
            model_name="growthitem",
            name="tags",
            field=models.ManyToManyField(
                related_name="growth_items",
                through="growth.UserItemTag",
                to="growth.tag",
                verbose_name="标签",
            ),
        ),
        migrations.AlterUniqueTogether(
            name="useritemtag",
            unique_together={("item", "tag")},
        ),
        migrations.AddIndex(
            model_name="growthitem",
            index=models.Index(
                fields=["user", "status"], name="growth_item_user_id_0c6ff7_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="growthitem",
            index=models.Index(
                fields=["parent"], name="growth_item_parent__e4b3a7_idx"
            ),
        ),
    ]
