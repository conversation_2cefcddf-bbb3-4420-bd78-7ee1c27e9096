"""
成长引擎应用的URL配置
"""
from django.urls import path
from . import views

app_name = 'growth'

urlpatterns = [
    # 成长项相关
    path('items/', views.GrowthItemListView.as_view(), name='item_list_create'),
    path('items/<uuid:pk>/', views.GrowthItemDetailView.as_view(), name='item_detail'),
    path('items/<uuid:pk>/status/', views.GrowthItemStatusUpdateView.as_view(), name='item_status_update'),
    path('items/bulk-status-update/', views.bulk_status_update, name='bulk_status_update'),
    
    # 标签相关
    path('tags/', views.TagListCreateView.as_view(), name='tag_list_create'),
    path('tags/<int:pk>/', views.TagDetailView.as_view(), name='tag_detail'),
    path('tags/categories/', views.tag_categories, name='tag_categories'),
    
    # 统计和时间线
    path('stats/', views.GrowthStatsView.as_view(), name='stats'),
    path('timeline/', views.growth_timeline, name='timeline'),
]
