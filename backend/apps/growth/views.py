"""
成长引擎应用的视图
处理成长项、标签的CRUD操作和统计功能
"""
from rest_framework import generics, permissions, status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.views import APIView
from django.db.models import Count, Q
from django.utils import timezone

from .models import GrowthItem, Tag, UserItemTag
from .serializers import (
    GrowthItemListSerializer,
    GrowthItemDetailSerializer,
    GrowthItemCreateSerializer,
    GrowthItemStatusUpdateSerializer,
    TagSerializer,
    TagCreateSerializer,
    GrowthStatsSerializer
)


class GrowthItemListView(generics.ListCreateAPIView):
    """成长项列表视图"""
    permission_classes = [permissions.IsAuthenticated]
    
    def get_serializer_class(self):
        if self.request.method == 'POST':
            return GrowthItemCreateSerializer
        return GrowthItemListSerializer
    
    def get_queryset(self):
        """
        获取成长项查询集，优化数据库查询性能
        使用 select_related 和 prefetch_related 减少数据库查询次数
        """
        queryset = GrowthItem.objects.filter(user=self.request.user).select_related(
            'user', 'parent'
        ).prefetch_related(
            'tags', 'children'
        ).order_by('-created_at')

        # 过滤参数
        status_filter = self.request.query_params.get('status')
        item_type = self.request.query_params.get('type')
        parent_id = self.request.query_params.get('parent')

        if status_filter:
            queryset = queryset.filter(status=status_filter)

        if item_type:
            queryset = queryset.filter(item_type=item_type)

        if parent_id:
            if parent_id == 'null':
                queryset = queryset.filter(parent__isnull=True)
            else:
                queryset = queryset.filter(parent_id=parent_id)
        
        return queryset.select_related('parent').prefetch_related('tags').order_by('-created_at')


class GrowthItemDetailView(generics.RetrieveUpdateDestroyAPIView):
    """成长项详情视图"""
    serializer_class = GrowthItemDetailSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        return GrowthItem.objects.filter(user=self.request.user).select_related('parent').prefetch_related('tags', 'children')


class GrowthItemStatusUpdateView(generics.UpdateAPIView):
    """成长项状态更新视图"""
    serializer_class = GrowthItemStatusUpdateSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        return GrowthItem.objects.filter(user=self.request.user)


class TagListCreateView(generics.ListCreateAPIView):
    """标签列表和创建视图"""
    permission_classes = [permissions.IsAuthenticated]
    
    def get_serializer_class(self):
        if self.request.method == 'POST':
            return TagCreateSerializer
        return TagSerializer
    
    def get_queryset(self):
        queryset = Tag.objects.all()
        
        # 过滤参数
        category = self.request.query_params.get('category')
        search = self.request.query_params.get('search')
        
        if category:
            queryset = queryset.filter(category=category)
        
        if search:
            queryset = queryset.filter(name__icontains=search)
        
        return queryset.order_by('category', 'name')


class TagDetailView(generics.RetrieveUpdateDestroyAPIView):
    """标签详情视图"""
    serializer_class = TagSerializer
    permission_classes = [permissions.IsAuthenticated]
    queryset = Tag.objects.all()


class GrowthStatsView(APIView):
    """成长统计视图"""
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request):
        user = request.user
        
        # 基础统计
        total_items = user.growth_items.count()
        status_stats = user.growth_items.aggregate(
            future_plans=Count('item_id', filter=Q(status='future_plan')),
            in_progress=Count('item_id', filter=Q(status='in_progress')),
            completed=Count('item_id', filter=Q(status='completed'))
        )
        
        # 类型统计
        type_stats = user.growth_items.aggregate(
            total_goals=Count('item_id', filter=Q(item_type='goal')),
            completed_goals=Count('item_id', filter=Q(item_type='goal', status='completed')),
            total_tasks=Count('item_id', filter=Q(item_type='task')),
            completed_tasks=Count('item_id', filter=Q(item_type='task', status='completed'))
        )
        
        # 完成率计算
        completion_rate = 0
        if total_items > 0:
            completion_rate = (status_stats['completed'] / total_items) * 100
        
        # 最常用标签（通过中间表统计，避免反向关联命名差异）
        most_used_tags_qs = (
            UserItemTag.objects
            .filter(item__user=user, item__status='completed')
            .values('tag__name', 'tag__category')
            .annotate(usage_count=Count('id'))
            .order_by('-usage_count')[:5]
        )
        most_used_tags = [
            {
                'name': row['tag__name'],
                'category': row['tag__category'],
                'usage_count': row['usage_count'],
            }
            for row in most_used_tags_qs
        ]

        stats_data = {
            'total_items': total_items,
            'future_plans': status_stats['future_plans'] or 0,
            'in_progress': status_stats['in_progress'] or 0,
            'completed': status_stats['completed'] or 0,
            'total_goals': type_stats['total_goals'] or 0,
            'completed_goals': type_stats['completed_goals'] or 0,
            'total_tasks': type_stats['total_tasks'] or 0,
            'completed_tasks': type_stats['completed_tasks'] or 0,
            'completion_rate': round(completion_rate, 2),
            'most_used_tags': list(most_used_tags)
        }
        
        serializer = GrowthStatsSerializer(stats_data)
        return Response(serializer.data)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def bulk_status_update(request):
    """批量更新成长项状态"""
    item_ids = request.data.get('item_ids', [])
    new_status = request.data.get('status')
    
    if not item_ids or not new_status:
        return Response({
            'error': '缺少必要参数'
        }, status=status.HTTP_400_BAD_REQUEST)
    
    # 验证状态值
    valid_statuses = ['future_plan', 'in_progress', 'completed']
    if new_status not in valid_statuses:
        return Response({
            'error': '无效的状态值'
        }, status=status.HTTP_400_BAD_REQUEST)
    
    # 更新状态
    items = GrowthItem.objects.filter(
        item_id__in=item_ids,
        user=request.user
    )
    
    updated_count = 0
    for item in items:
        item.status = new_status
        if new_status == 'completed' and not item.completed_at:
            item.completed_at = timezone.now()
        elif new_status == 'in_progress' and not item.started_at:
            item.started_at = timezone.now()
        item.save()
        updated_count += 1
    
    return Response({
        'message': f'成功更新 {updated_count} 个成长项的状态',
        'updated_count': updated_count
    })


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def growth_timeline(request):
    """获取成长时间线"""
    user = request.user
    
    # 获取最近完成的成长项
    completed_items = (
        user.growth_items
        .filter(status='completed', completed_at__isnull=False)
        .order_by('-completed_at')[:20]
        .values('item_id', 'title', 'item_type', 'completed_at', 'ai_summary')
    )
    
    # 获取正在进行的成长项
    in_progress_items = (
        user.growth_items
        .filter(status='in_progress')
        .order_by('-started_at')[:10]
        .values('item_id', 'title', 'item_type', 'started_at')
    )
    
    return Response({
        'completed_items': list(completed_items),
        'in_progress_items': list(in_progress_items)
    })


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def tag_categories(request):
    """获取标签分类列表"""
    categories = Tag.objects.values_list('category', flat=True).distinct().order_by('category')
    return Response({
        'categories': list(categories)
    })
