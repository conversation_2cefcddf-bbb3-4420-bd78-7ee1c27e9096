"""
成长引擎模型
根据数据库文档中的 growth_items, tags, user_item_tags 表设计
"""
import uuid
from django.db import models
from django.utils import timezone
from django.core.exceptions import ValidationError


class Tag(models.Model):
    """
    标签模型
    用于标记技能、知识领域等
    """
    tag_id = models.BigAutoField(
        primary_key=True,
        verbose_name='标签ID'
    )
    
    name = models.CharField(
        max_length=100,
        unique=True,
        verbose_name='标签名称',
        help_text='如 "Python", "项目管理" 等'
    )
    
    category = models.CharField(
        max_length=100,
        verbose_name='标签分类',
        help_text='如 "技能", "知识领域", "软技能" 等'
    )
    
    class Meta:
        db_table = 'tags'
        verbose_name = '标签'
        verbose_name_plural = '标签'
        ordering = ['category', 'name']
    
    def __str__(self):
        return f"{self.category} - {self.name}"


class GrowthItem(models.Model):
    """
    成长项模型
    统一管理目标、任务、子任务
    """
    STATUS_CHOICES = [
        ('future_plan', '未来计划'),
        ('in_progress', '正在进行'),
        ('completed', '已完成'),
    ]
    
    TYPE_CHOICES = [
        ('goal', '目标'),
        ('task', '任务'),
    ]
    
    CREATED_BY_CHOICES = [
        ('user', '用户创建'),
        ('ai', 'AI创建'),
    ]
    
    item_id = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False,
        verbose_name='成长项ID'
    )
    
    user = models.ForeignKey(
        'users.User',
        on_delete=models.CASCADE,
        related_name='growth_items',
        verbose_name='用户'
    )
    
    parent = models.ForeignKey(
        'self',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='children',
        verbose_name='父项',
        help_text='NULL表示为顶级目标'
    )
    
    title = models.CharField(
        max_length=500,
        verbose_name='标题'
    )
    
    description = models.TextField(
        blank=True,
        verbose_name='详细描述'
    )
    
    status = models.CharField(
        max_length=50,
        choices=STATUS_CHOICES,
        verbose_name='状态'
    )
    
    item_type = models.CharField(
        max_length=50,
        choices=TYPE_CHOICES,
        verbose_name='类型'
    )
    
    created_by = models.CharField(
        max_length=50,
        choices=CREATED_BY_CHOICES,
        default='user',
        verbose_name='创建者'
    )
    
    ai_summary = models.TextField(
        blank=True,
        verbose_name='AI总结',
        help_text='目标完成时，由AI生成的成果总结'
    )
    
    created_at = models.DateTimeField(
        default=timezone.now,
        verbose_name='创建时间'
    )
    
    started_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name='开始时间'
    )
    
    completed_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name='完成时间'
    )
    
    # 多对多关系：成长项可以有多个标签
    tags = models.ManyToManyField(
        Tag,
        through='UserItemTag',
        related_name='growth_items',
        verbose_name='标签'
    )
    
    class Meta:
        db_table = 'growth_items'
        verbose_name = '成长项'
        verbose_name_plural = '成长项'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'status']),
            models.Index(fields=['parent']),
        ]
    
    def __str__(self):
        return f"{self.get_item_type_display()} - {self.title}"
    
    def clean(self):
        """
        模型验证
        """
        # 验证父子关系的合理性
        if self.parent and self.parent.user != self.user:
            raise ValidationError('父项必须属于同一用户')
        
        # 验证状态转换的合理性
        if self.status == 'completed' and not self.completed_at:
            self.completed_at = timezone.now()
        elif self.status == 'in_progress' and not self.started_at:
            self.started_at = timezone.now()
    
    def save(self, *args, **kwargs):
        self.clean()
        super().save(*args, **kwargs)
    
    @property
    def is_goal(self):
        """是否为目标"""
        return self.item_type == 'goal'
    
    @property
    def is_task(self):
        """是否为任务"""
        return self.item_type == 'task'
    
    @property
    def is_completed(self):
        """是否已完成"""
        return self.status == 'completed'
    
    def get_all_children(self):
        """获取所有子项（递归）"""
        children = []
        for child in self.children.all():
            children.append(child)
            children.extend(child.get_all_children())
        return children
    
    def get_completion_rate(self):
        """获取完成率（基于子任务）"""
        children = self.children.all()
        if not children:
            return 100 if self.is_completed else 0
        
        completed_count = children.filter(status='completed').count()
        return (completed_count / children.count()) * 100


class UserItemTag(models.Model):
    """
    用户成长项标签关联表
    """
    item = models.ForeignKey(
        GrowthItem,
        on_delete=models.CASCADE,
        verbose_name='成长项'
    )
    
    tag = models.ForeignKey(
        Tag,
        on_delete=models.CASCADE,
        verbose_name='标签'
    )
    
    class Meta:
        db_table = 'user_item_tags'
        verbose_name = '成长项标签'
        verbose_name_plural = '成长项标签'
        unique_together = ['item', 'tag']
    
    def __str__(self):
        return f"{self.item.title} - {self.tag.name}"
