"""
成长引擎应用的Django Admin配置
"""
from django.contrib import admin
from .models import Tag, GrowthItem, UserItemTag


@admin.register(Tag)
class TagAdmin(admin.ModelAdmin):
    """标签管理界面"""
    list_display = ('name', 'category', 'tag_id')
    list_filter = ('category',)
    search_fields = ('name', 'category')
    ordering = ('category', 'name')


class UserItemTagInline(admin.TabularInline):
    """成长项标签内联编辑"""
    model = UserItemTag
    extra = 1


@admin.register(GrowthItem)
class GrowthItemAdmin(admin.ModelAdmin):
    """成长项管理界面"""
    list_display = ('title', 'user', 'item_type', 'status', 'created_by', 'created_at')
    list_filter = ('item_type', 'status', 'created_by', 'created_at')
    search_fields = ('title', 'description', 'user__email')
    ordering = ('-created_at',)
    
    fieldsets = (
        ('基本信息', {
            'fields': ('user', 'parent', 'title', 'description')
        }),
        ('分类和状态', {
            'fields': ('item_type', 'status', 'created_by')
        }),
        ('AI总结', {
            'fields': ('ai_summary',),
            'classes': ('collapse',)
        }),
        ('时间信息', {
            'fields': ('created_at', 'started_at', 'completed_at'),
            'classes': ('collapse',)
        }),
    )
    
    readonly_fields = ('item_id', 'created_at')
    inlines = [UserItemTagInline]
    
    def get_queryset(self, request):
        """优化查询性能"""
        return super().get_queryset(request).select_related('user', 'parent')


@admin.register(UserItemTag)
class UserItemTagAdmin(admin.ModelAdmin):
    """用户成长项标签管理界面"""
    list_display = ('item', 'tag')
    list_filter = ('tag__category',)
    search_fields = ('item__title', 'tag__name')
    
    def get_queryset(self, request):
        """优化查询性能"""
        return super().get_queryset(request).select_related('item', 'tag', 'item__user')
