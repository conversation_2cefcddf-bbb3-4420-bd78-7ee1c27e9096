"""
成长引擎应用的序列化器
处理成长项、标签等数据序列化
"""
from rest_framework import serializers
from django.utils import timezone
from .models import GrowthItem, Tag, UserItemTag


class TagSerializer(serializers.ModelSerializer):
    """标签序列化器"""
    class Meta:
        model = Tag
        fields = ('tag_id', 'name', 'category')


class GrowthItemListSerializer(serializers.ModelSerializer):
    """成长项列表序列化器（包含子任务）"""
    tags = TagSerializer(many=True, read_only=True)
    children_count = serializers.SerializerMethodField()
    completion_rate = serializers.SerializerMethodField()
    children = serializers.SerializerMethodField()

    class Meta:
        model = GrowthItem
        fields = (
            'item_id', 'title', 'description', 'item_type', 'status', 'created_by',
            'created_at', 'started_at', 'completed_at', 'tags', 'parent',
            'children_count', 'completion_rate', 'children'
        )

    def get_children_count(self, obj):
        """获取子项数量"""
        return obj.children.count()

    def get_completion_rate(self, obj):
        """获取完成率"""
        return obj.get_completion_rate()

    def get_children(self, obj):
        """获取子项列表（递归）"""
        children = obj.children.all().order_by('-created_at')
        # 使用自身序列化器来支持递归子任务
        return GrowthItemListSerializer(children, many=True, context=self.context).data


class GrowthItemDetailSerializer(serializers.ModelSerializer):
    """成长项详情序列化器"""
    tags = TagSerializer(many=True, read_only=True)
    tag_ids = serializers.ListField(
        child=serializers.IntegerField(),
        write_only=True,
        required=False
    )
    children = serializers.SerializerMethodField()
    parent_info = serializers.SerializerMethodField()
    completion_rate = serializers.SerializerMethodField()
    
    class Meta:
        model = GrowthItem
        fields = (
            'item_id', 'title', 'description', 'item_type', 'status',
            'created_by', 'ai_summary', 'parent', 'created_at',
            'started_at', 'completed_at', 'tags', 'tag_ids',
            'children', 'parent_info', 'completion_rate'
        )
        read_only_fields = ('item_id', 'created_at', 'ai_summary')
    
    def get_children(self, obj):
        """获取子项列表"""
        children = obj.children.all().order_by('-created_at')
        return GrowthItemListSerializer(children, many=True).data
    
    def get_parent_info(self, obj):
        """获取父项信息"""
        if obj.parent:
            return {
                'item_id': obj.parent.item_id,
                'title': obj.parent.title,
                'item_type': obj.parent.item_type
            }
        return None
    
    def get_completion_rate(self, obj):
        """获取完成率"""
        return obj.get_completion_rate()
    
    def validate(self, attrs):
        """验证数据"""
        # 验证父子关系
        parent = attrs.get('parent')
        if parent and parent.user != self.context['request'].user:
            raise serializers.ValidationError('父项必须属于当前用户')
        
        # 验证状态转换
        status = attrs.get('status')
        if status == 'completed' and not attrs.get('completed_at'):
            attrs['completed_at'] = timezone.now()
        elif status == 'in_progress' and not attrs.get('started_at'):
            attrs['started_at'] = timezone.now()
        
        return attrs
    
    def create(self, validated_data):
        """创建成长项"""
        tag_ids = validated_data.pop('tag_ids', [])
        validated_data['user'] = self.context['request'].user
        
        growth_item = GrowthItem.objects.create(**validated_data)
        
        # 添加标签
        if tag_ids:
            tags = Tag.objects.filter(tag_id__in=tag_ids)
            for tag in tags:
                UserItemTag.objects.create(item=growth_item, tag=tag)
        
        return growth_item
    
    def update(self, instance, validated_data):
        """更新成长项"""
        tag_ids = validated_data.pop('tag_ids', None)
        
        # 更新基本信息
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        
        # 更新标签
        if tag_ids is not None:
            # 删除现有标签关联
            UserItemTag.objects.filter(item=instance).delete()
            # 添加新标签
            tags = Tag.objects.filter(tag_id__in=tag_ids)
            for tag in tags:
                UserItemTag.objects.create(item=instance, tag=tag)
        
        return instance


class GrowthItemCreateSerializer(serializers.ModelSerializer):
    """成长项创建序列化器"""
    tag_ids = serializers.ListField(
        child=serializers.IntegerField(),
        required=False,
        allow_empty=True
    )
    
    class Meta:
        model = GrowthItem
        fields = (
            'title', 'description', 'item_type', 'status',
            'parent', 'tag_ids'
        )
    
    def validate_parent(self, value):
        """验证父项"""
        if value and value.user != self.context['request'].user:
            raise serializers.ValidationError('父项必须属于当前用户')
        return value
    
    def create(self, validated_data):
        """创建成长项"""
        tag_ids = validated_data.pop('tag_ids', [])
        validated_data['user'] = self.context['request'].user
        
        growth_item = GrowthItem.objects.create(**validated_data)
        
        # 添加标签
        if tag_ids:
            tags = Tag.objects.filter(tag_id__in=tag_ids)
            for tag in tags:
                UserItemTag.objects.create(item=growth_item, tag=tag)
        
        return growth_item


class GrowthItemStatusUpdateSerializer(serializers.ModelSerializer):
    """成长项状态更新序列化器"""
    class Meta:
        model = GrowthItem
        fields = ('status',)
    
    def update(self, instance, validated_data):
        """更新状态"""
        new_status = validated_data.get('status')
        
        if new_status == 'completed' and not instance.completed_at:
            instance.completed_at = timezone.now()
        elif new_status == 'in_progress' and not instance.started_at:
            instance.started_at = timezone.now()
        
        instance.status = new_status
        instance.save()
        return instance


class GrowthStatsSerializer(serializers.Serializer):
    """成长统计序列化器"""
    total_items = serializers.IntegerField()
    future_plans = serializers.IntegerField()
    in_progress = serializers.IntegerField()
    completed = serializers.IntegerField()
    total_goals = serializers.IntegerField()
    completed_goals = serializers.IntegerField()
    total_tasks = serializers.IntegerField()
    completed_tasks = serializers.IntegerField()
    completion_rate = serializers.FloatField()
    most_used_tags = serializers.ListField()


class TagCreateSerializer(serializers.ModelSerializer):
    """标签创建序列化器"""
    class Meta:
        model = Tag
        fields = ('name', 'category')
    
    def validate_name(self, value):
        """验证标签名称唯一性"""
        if Tag.objects.filter(name=value).exists():
            raise serializers.ValidationError('该标签名称已存在')
        return value
