"""
API响应缓存工具 - 灵境(Mentia)后端

文件功能：
    提供API响应缓存的工具类和装饰器，提高API响应速度

主要功能：
    1. API响应缓存装饰器
    2. 智能缓存键生成
    3. 缓存失效策略
    4. 缓存统计和监控

使用方式：
    from apps.common.cache_utils import cache_api_response, CacheManager
    
    @cache_api_response(timeout=300)
    def my_api_view(request):
        return Response(data)

作者: Mentia后端团队
创建时间: 2025-09-05
版本: v1.0
"""

import hashlib
import json
import logging
from functools import wraps
from typing import Any, Dict, Optional, Union, List
from django.core.cache import cache
from django.http import JsonResponse
from django.conf import settings
from rest_framework.response import Response
from rest_framework import status

logger = logging.getLogger(__name__)


class CacheManager:
    """
    缓存管理器
    
    功能：
    - 统一缓存键管理
    - 缓存失效策略
    - 缓存统计
    """
    
    def __init__(self, prefix: str = 'mentia_api'):
        self.prefix = prefix
        self.stats = {
            'hits': 0,
            'misses': 0,
            'sets': 0,
            'deletes': 0
        }
    
    def generate_cache_key(
        self,
        view_name: str,
        user_id: Optional[str] = None,
        params: Optional[Dict[str, Any]] = None,
        query_params: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        生成缓存键
        
        参数:
        - view_name: 视图名称
        - user_id: 用户ID（用于用户相关缓存）
        - params: URL参数
        - query_params: 查询参数
        """
        key_parts = [self.prefix, view_name]
        
        if user_id:
            key_parts.append(f"user_{user_id}")
        
        if params:
            # 对参数进行排序以确保一致性
            sorted_params = sorted(params.items())
            params_str = '_'.join([f"{k}_{v}" for k, v in sorted_params])
            key_parts.append(f"params_{params_str}")
        
        if query_params:
            # 过滤掉不影响结果的参数（如时间戳）
            filtered_params = {
                k: v for k, v in query_params.items()
                if k not in ['_', 'timestamp', 'cache_bust']
            }
            if filtered_params:
                sorted_query = sorted(filtered_params.items())
                query_str = '_'.join([f"{k}_{v}" for k, v in sorted_query])
                key_parts.append(f"query_{query_str}")
        
        # 生成最终的缓存键
        cache_key = '_'.join(key_parts)
        
        # 如果键太长，使用哈希
        if len(cache_key) > 200:
            cache_key = f"{self.prefix}_{hashlib.md5(cache_key.encode()).hexdigest()}"
        
        return cache_key
    
    def get(self, key: str) -> Any:
        """获取缓存"""
        result = cache.get(key)
        if result is not None:
            self.stats['hits'] += 1
            logger.debug(f"Cache hit: {key}")
        else:
            self.stats['misses'] += 1
            logger.debug(f"Cache miss: {key}")
        return result
    
    def set(self, key: str, value: Any, timeout: int = 300) -> None:
        """设置缓存"""
        cache.set(key, value, timeout)
        self.stats['sets'] += 1
        logger.debug(f"Cache set: {key} (timeout: {timeout}s)")
    
    def delete(self, key: str) -> None:
        """删除缓存"""
        cache.delete(key)
        self.stats['deletes'] += 1
        logger.debug(f"Cache delete: {key}")
    
    def delete_pattern(self, pattern: str) -> None:
        """根据模式删除缓存"""
        # 注意：需要Redis支持
        if hasattr(cache, 'delete_pattern'):
            cache.delete_pattern(f"{self.prefix}_{pattern}*")
            self.stats['deletes'] += 1
            logger.debug(f"Cache delete pattern: {pattern}")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        total_requests = self.stats['hits'] + self.stats['misses']
        hit_rate = (self.stats['hits'] / total_requests * 100) if total_requests > 0 else 0
        
        return {
            'hits': self.stats['hits'],
            'misses': self.stats['misses'],
            'sets': self.stats['sets'],
            'deletes': self.stats['deletes'],
            'hit_rate': f"{hit_rate:.2f}%",
            'total_requests': total_requests
        }


# 全局缓存管理器实例
cache_manager = CacheManager()


def cache_api_response(
    timeout: int = 300,
    key_prefix: Optional[str] = None,
    vary_on_user: bool = True,
    vary_on_params: bool = True,
    vary_on_query: bool = True,
    cache_empty_results: bool = False,
    condition: Optional[callable] = None
):
    """
    API响应缓存装饰器
    
    参数:
    - timeout: 缓存超时时间（秒）
    - key_prefix: 缓存键前缀
    - vary_on_user: 是否根据用户区分缓存
    - vary_on_params: 是否根据URL参数区分缓存
    - vary_on_query: 是否根据查询参数区分缓存
    - cache_empty_results: 是否缓存空结果
    - condition: 缓存条件函数
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(*args, **kwargs):
            # 获取request对象
            request = None
            if args and hasattr(args[0], 'user'):
                request = args[0]
            elif args and hasattr(args[1], 'user'):
                request = args[1]
            
            if not request:
                # 无法获取request，直接执行原函数
                return view_func(*args, **kwargs)
            
            # 检查缓存条件
            if condition and not condition(request):
                return view_func(*args, **kwargs)
            
            # 生成缓存键
            view_name = key_prefix or view_func.__name__
            user_id = str(request.user.user_id) if vary_on_user and hasattr(request.user, 'user_id') else None
            params = kwargs if vary_on_params else None
            query_params = dict(request.GET) if vary_on_query else None
            
            cache_key = cache_manager.generate_cache_key(
                view_name=view_name,
                user_id=user_id,
                params=params,
                query_params=query_params
            )
            
            # 尝试从缓存获取结果
            cached_result = cache_manager.get(cache_key)
            if cached_result is not None:
                # 重构Response对象
                if isinstance(cached_result, dict) and 'data' in cached_result:
                    return Response(
                        data=cached_result['data'],
                        status=cached_result.get('status', status.HTTP_200_OK)
                    )
                return cached_result
            
            # 缓存未命中，执行原函数
            result = view_func(*args, **kwargs)
            
            # 检查是否应该缓存结果
            should_cache = True
            
            if isinstance(result, Response):
                # 只缓存成功的响应
                if result.status_code >= 400:
                    should_cache = False
                
                # 检查是否缓存空结果
                if not cache_empty_results and not result.data:
                    should_cache = False
                
                if should_cache:
                    # 缓存Response数据
                    cache_data = {
                        'data': result.data,
                        'status': result.status_code
                    }
                    cache_manager.set(cache_key, cache_data, timeout)
            
            elif isinstance(result, JsonResponse):
                if result.status_code < 400 and should_cache:
                    cache_manager.set(cache_key, result, timeout)
            
            return result
        
        return wrapper
    return decorator


def invalidate_cache_for_user(user_id: str, pattern: Optional[str] = None):
    """
    清除用户相关的缓存
    
    参数:
    - user_id: 用户ID
    - pattern: 额外的模式匹配
    """
    if pattern:
        cache_pattern = f"user_{user_id}_{pattern}"
    else:
        cache_pattern = f"user_{user_id}"
    
    cache_manager.delete_pattern(cache_pattern)
    logger.info(f"Invalidated cache for user {user_id} with pattern {pattern}")


def invalidate_cache_for_model(model_name: str, user_id: Optional[str] = None):
    """
    清除模型相关的缓存
    
    参数:
    - model_name: 模型名称
    - user_id: 可选的用户ID
    """
    if user_id:
        cache_pattern = f"user_{user_id}_{model_name}"
    else:
        cache_pattern = model_name
    
    cache_manager.delete_pattern(cache_pattern)
    logger.info(f"Invalidated cache for model {model_name}")


class CacheInvalidationMixin:
    """
    缓存失效混入类
    
    为模型提供自动缓存失效功能
    """
    cache_related_models = []  # 相关模型列表
    cache_invalidation_patterns = []  # 失效模式列表
    
    def invalidate_related_cache(self):
        """失效相关缓存"""
        user_id = getattr(self, 'user_id', None) or getattr(self.user, 'user_id', None)
        
        # 失效模型相关缓存
        model_name = self.__class__.__name__.lower()
        invalidate_cache_for_model(model_name, user_id)
        
        # 失效相关模型缓存
        for related_model in self.cache_related_models:
            invalidate_cache_for_model(related_model, user_id)
        
        # 失效自定义模式缓存
        for pattern in self.cache_invalidation_patterns:
            if user_id:
                cache_manager.delete_pattern(f"user_{user_id}_{pattern}")
            else:
                cache_manager.delete_pattern(pattern)
    
    def save(self, *args, **kwargs):
        """保存时失效缓存"""
        super().save(*args, **kwargs)
        self.invalidate_related_cache()
    
    def delete(self, *args, **kwargs):
        """删除时失效缓存"""
        self.invalidate_related_cache()
        super().delete(*args, **kwargs)


# 常用缓存配置
CACHE_CONFIGS = {
    'user_profile': {
        'timeout': 1800,  # 30分钟
        'vary_on_user': True,
        'vary_on_params': False,
        'vary_on_query': False
    },
    'blog_posts': {
        'timeout': 600,   # 10分钟
        'vary_on_user': True,
        'vary_on_params': True,
        'vary_on_query': True
    },
    'growth_items': {
        'timeout': 300,   # 5分钟
        'vary_on_user': True,
        'vary_on_params': True,
        'vary_on_query': True
    },
    'ai_conversations': {
        'timeout': 180,   # 3分钟
        'vary_on_user': True,
        'vary_on_params': True,
        'vary_on_query': False
    },
    'statistics': {
        'timeout': 900,   # 15分钟
        'vary_on_user': True,
        'vary_on_params': False,
        'vary_on_query': False
    }
}


def get_cache_config(config_name: str) -> Dict[str, Any]:
    """获取缓存配置"""
    return CACHE_CONFIGS.get(config_name, {
        'timeout': 300,
        'vary_on_user': True,
        'vary_on_params': True,
        'vary_on_query': True
    })


def cache_with_config(config_name: str):
    """使用预定义配置的缓存装饰器"""
    config = get_cache_config(config_name)
    return cache_api_response(**config)


# 缓存统计视图装饰器
def cache_stats_required(view_func):
    """需要缓存统计的装饰器"""
    @wraps(view_func)
    def wrapper(*args, **kwargs):
        result = view_func(*args, **kwargs)
        
        # 在响应中添加缓存统计信息（仅在DEBUG模式下）
        if settings.DEBUG and isinstance(result, Response):
            stats = cache_manager.get_stats()
            if isinstance(result.data, dict):
                result.data['_cache_stats'] = stats
        
        return result
    return wrapper
