"""
通用装饰器 - 灵境(Mentia)后端公共工具

文件功能：
    提供统一的错误处理、日志记录、性能监控等装饰器

主要装饰器：
    1. api_error_handler - 统一API错误处理
    2. log_api_call - API调用日志记录
    3. performance_monitor - 性能监控装饰器

使用方式：
    @api_error_handler
    @permission_classes([permissions.IsAuthenticated])
    def my_api_view(request):
        # API逻辑
        pass

作者: Mentia后端团队
创建时间: 2025-09-05
版本: v1.0
"""

import logging
import time
from functools import wraps
from rest_framework import status
from rest_framework.response import Response
from django.core.exceptions import ValidationError
from django.db import IntegrityError

logger = logging.getLogger(__name__)


def api_error_handler(view_func):
    """
    统一API错误处理装饰器 - 增强版

    功能：
    - 捕获常见异常并返回标准化错误响应
    - 记录错误日志
    - 过滤敏感信息
    - 提供用户友好的错误消息
    - 支持错误分类和严重程度标记

    支持的异常类型：
    - ValidationError: 数据验证错误
    - IntegrityError: 数据库完整性错误
    - PermissionDenied: 权限错误
    - NotFound: 资源不存在错误
    - Exception: 通用异常
    """
    @wraps(view_func)
    def wrapper(*args, **kwargs):
        try:
            return view_func(*args, **kwargs)
        except ValidationError as e:
            logger.warning(f"Validation error in {view_func.__name__}: {str(e)}")
            return Response({
                'error': '数据验证失败',
                'message': '输入的数据格式不正确，请检查后重试',
                'details': str(e),
                'code': 'VALIDATION_ERROR'
            }, status=status.HTTP_400_BAD_REQUEST)
        except IntegrityError as e:
            logger.error(f"Database integrity error in {view_func.__name__}: {str(e)}")
            return Response({
                'error': '数据完整性错误，请检查输入数据',
                'message': '数据保存失败，可能存在重复或关联数据问题',
                'code': 'INTEGRITY_ERROR'
            }, status=status.HTTP_400_BAD_REQUEST)
        except PermissionError as e:
            logger.warning(f"Permission denied in {view_func.__name__}: {str(e)}")
            return Response({
                'error': '权限不足',
                'message': '您没有权限执行此操作',
                'code': 'PERMISSION_DENIED'
            }, status=status.HTTP_403_FORBIDDEN)
        except Exception as e:
            # 检查是否是DRF的异常
            if hasattr(e, 'status_code'):
                logger.warning(f"DRF error in {view_func.__name__}: {str(e)}")
                return Response({
                    'error': str(e),
                    'message': str(e),
                    'code': 'API_ERROR'
                }, status=getattr(e, 'status_code', status.HTTP_400_BAD_REQUEST))

            logger.error(f"Unexpected error in {view_func.__name__}: {str(e)}")
            return Response({
                'error': '服务器内部错误，请稍后重试',
                'message': '系统暂时不可用，请稍后重试或联系技术支持',
                'code': 'INTERNAL_ERROR'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    return wrapper


def log_api_call(view_func):
    """
    API调用日志记录装饰器
    
    功能：
    - 记录API调用信息
    - 记录请求参数（过滤敏感信息）
    - 记录响应状态
    """
    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        start_time = time.time()
        
        # 记录请求信息
        logger.info(f"API Call: {view_func.__name__} - User: {getattr(request.user, 'email', 'Anonymous')}")
        
        try:
            response = view_func(request, *args, **kwargs)
            
            # 记录成功响应
            duration = time.time() - start_time
            logger.info(f"API Success: {view_func.__name__} - Duration: {duration:.3f}s - Status: {response.status_code}")
            
            return response
        except Exception as e:
            # 记录错误响应
            duration = time.time() - start_time
            logger.error(f"API Error: {view_func.__name__} - Duration: {duration:.3f}s - Error: {str(e)}")
            raise
    
    return wrapper


def performance_monitor(threshold_seconds=1.0):
    """
    性能监控装饰器
    
    参数：
    - threshold_seconds: 性能警告阈值（秒）
    
    功能：
    - 监控API响应时间
    - 超过阈值时记录警告
    - 收集性能统计数据
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            
            try:
                result = view_func(*args, **kwargs)
                duration = time.time() - start_time
                
                # 性能警告
                if duration > threshold_seconds:
                    logger.warning(f"Slow API: {view_func.__name__} took {duration:.3f}s (threshold: {threshold_seconds}s)")
                
                return result
            except Exception as e:
                duration = time.time() - start_time
                logger.error(f"API Error after {duration:.3f}s in {view_func.__name__}: {str(e)}")
                raise
        
        return wrapper
    return decorator


def cache_result(timeout=300):
    """
    结果缓存装饰器
    
    参数：
    - timeout: 缓存超时时间（秒）
    
    功能：
    - 缓存API响应结果
    - 减少重复计算
    - 提高响应速度
    
    注意：仅适用于GET请求和无副作用的操作
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            # 只缓存GET请求
            if request.method != 'GET':
                return view_func(request, *args, **kwargs)
            
            # 生成缓存键
            cache_key = f"api_cache:{view_func.__name__}:{request.user.user_id}:{hash(str(request.GET))}"
            
            try:
                from django.core.cache import cache
                
                # 尝试从缓存获取结果
                cached_result = cache.get(cache_key)
                if cached_result is not None:
                    logger.debug(f"Cache hit for {view_func.__name__}")
                    return cached_result
                
                # 执行函数并缓存结果
                result = view_func(request, *args, **kwargs)
                cache.set(cache_key, result, timeout)
                logger.debug(f"Cache set for {view_func.__name__}")
                
                return result
            except Exception as e:
                logger.warning(f"Cache error in {view_func.__name__}: {str(e)}")
                # 缓存失败时直接执行函数
                return view_func(request, *args, **kwargs)
        
        return wrapper
    return decorator


def require_fields(*required_fields):
    """
    必填字段验证装饰器
    
    参数：
    - required_fields: 必填字段列表
    
    功能：
    - 验证请求数据中的必填字段
    - 返回标准化错误响应
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            # 检查必填字段
            missing_fields = []
            request_data = getattr(request, 'data', {})
            
            for field in required_fields:
                if field not in request_data or not request_data[field]:
                    missing_fields.append(field)
            
            if missing_fields:
                return Response({
                    'error': '缺少必填字段',
                    'missing_fields': missing_fields
                }, status=status.HTTP_400_BAD_REQUEST)
            
            return view_func(request, *args, **kwargs)
        
        return wrapper
    return decorator
