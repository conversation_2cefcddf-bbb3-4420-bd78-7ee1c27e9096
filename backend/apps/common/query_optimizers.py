"""
数据库查询优化工具 - 灵境(Mentia)后端

文件功能：
    提供数据库查询优化的工具类和装饰器，减少N+1查询问题

主要功能：
    1. 查询优化装饰器
    2. 常用查询优化器
    3. 缓存查询结果
    4. 查询性能监控

使用方式：
    from apps.common.query_optimizers import optimize_queryset, QueryOptimizer
    
    # 在视图中使用
    @optimize_queryset(['user', 'category'], ['tags', 'comments'])
    def get_queryset(self):
        return Post.objects.all()

作者: Mentia后端团队
创建时间: 2025-09-05
版本: v1.0
"""

import logging
import time
from functools import wraps
from typing import List, Optional, Dict, Any
from django.core.cache import cache
from django.db import models, connection
from django.db.models import QuerySet, Prefetch
from django.conf import settings

logger = logging.getLogger(__name__)


class QueryOptimizer:
    """
    查询优化器类
    
    功能：
    - 自动添加select_related和prefetch_related
    - 监控查询性能
    - 缓存查询结果
    - 提供查询统计
    """
    
    def __init__(self, model_class: models.Model):
        self.model_class = model_class
        self.query_stats = {}
    
    def optimize_queryset(
        self,
        queryset: QuerySet,
        select_related: Optional[List[str]] = None,
        prefetch_related: Optional[List[str]] = None,
        only_fields: Optional[List[str]] = None,
        defer_fields: Optional[List[str]] = None
    ) -> QuerySet:
        """
        优化查询集
        
        参数:
        - queryset: 原始查询集
        - select_related: 需要join的外键字段
        - prefetch_related: 需要预取的多对多或反向外键字段
        - only_fields: 只查询指定字段
        - defer_fields: 延迟加载的字段
        """
        optimized_queryset = queryset
        
        # 添加select_related优化
        if select_related:
            optimized_queryset = optimized_queryset.select_related(*select_related)
        
        # 添加prefetch_related优化
        if prefetch_related:
            optimized_queryset = optimized_queryset.prefetch_related(*prefetch_related)
        
        # 只查询需要的字段
        if only_fields:
            optimized_queryset = optimized_queryset.only(*only_fields)
        
        # 延迟加载不需要的字段
        if defer_fields:
            optimized_queryset = optimized_queryset.defer(*defer_fields)
        
        return optimized_queryset
    
    def get_optimized_queryset(
        self,
        filters: Optional[Dict[str, Any]] = None,
        select_related: Optional[List[str]] = None,
        prefetch_related: Optional[List[str]] = None,
        order_by: Optional[List[str]] = None
    ) -> QuerySet:
        """
        获取优化后的查询集
        """
        queryset = self.model_class.objects.all()
        
        # 应用过滤条件
        if filters:
            queryset = queryset.filter(**filters)
        
        # 应用查询优化
        queryset = self.optimize_queryset(
            queryset,
            select_related=select_related,
            prefetch_related=prefetch_related
        )
        
        # 应用排序
        if order_by:
            queryset = queryset.order_by(*order_by)
        
        return queryset
    
    def monitor_query_performance(self, query_name: str):
        """
        监控查询性能装饰器
        """
        def decorator(func):
            @wraps(func)
            def wrapper(*args, **kwargs):
                start_time = time.time()
                initial_queries = len(connection.queries)
                
                result = func(*args, **kwargs)
                
                end_time = time.time()
                final_queries = len(connection.queries)
                
                duration = end_time - start_time
                query_count = final_queries - initial_queries
                
                # 记录性能统计
                self.query_stats[query_name] = {
                    'duration': duration,
                    'query_count': query_count,
                    'timestamp': end_time
                }
                
                # 性能警告
                if duration > 1.0:
                    logger.warning(f"Slow query '{query_name}': {duration:.3f}s, {query_count} queries")
                elif query_count > 10:
                    logger.warning(f"High query count '{query_name}': {query_count} queries in {duration:.3f}s")
                
                return result
            return wrapper
        return decorator


def optimize_queryset(
    select_related: Optional[List[str]] = None,
    prefetch_related: Optional[List[str]] = None,
    only_fields: Optional[List[str]] = None,
    defer_fields: Optional[List[str]] = None
):
    """
    查询优化装饰器
    
    用法:
    @optimize_queryset(['user', 'category'], ['tags'])
    def get_queryset(self):
        return Post.objects.all()
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            queryset = func(*args, **kwargs)
            
            if not isinstance(queryset, QuerySet):
                return queryset
            
            # 应用优化
            if select_related:
                queryset = queryset.select_related(*select_related)
            
            if prefetch_related:
                queryset = queryset.prefetch_related(*prefetch_related)
            
            if only_fields:
                queryset = queryset.only(*only_fields)
            
            if defer_fields:
                queryset = queryset.defer(*defer_fields)
            
            return queryset
        return wrapper
    return decorator


class CachedQueryMixin:
    """
    缓存查询混入类
    
    为视图类提供查询结果缓存功能
    """
    cache_timeout = 300  # 5分钟缓存
    cache_key_prefix = 'query_cache'
    
    def get_cache_key(self, *args, **kwargs) -> str:
        """
        生成缓存键
        """
        user_id = getattr(self.request.user, 'user_id', 'anonymous')
        view_name = self.__class__.__name__
        params = '_'.join([str(arg) for arg in args] + [f"{k}_{v}" for k, v in kwargs.items()])
        return f"{self.cache_key_prefix}_{view_name}_{user_id}_{params}"
    
    def get_cached_queryset(self, cache_key: str, queryset_func, *args, **kwargs):
        """
        获取缓存的查询结果
        """
        # 尝试从缓存获取
        cached_result = cache.get(cache_key)
        if cached_result is not None:
            logger.debug(f"Cache hit for key: {cache_key}")
            return cached_result
        
        # 缓存未命中，执行查询
        logger.debug(f"Cache miss for key: {cache_key}")
        result = queryset_func(*args, **kwargs)
        
        # 缓存结果
        cache.set(cache_key, result, self.cache_timeout)
        return result
    
    def invalidate_cache_pattern(self, pattern: str):
        """
        根据模式清除缓存
        """
        # 注意：这需要Redis支持，Django默认缓存不支持模式删除
        if hasattr(cache, 'delete_pattern'):
            cache.delete_pattern(f"{self.cache_key_prefix}_{pattern}*")


# 常用查询优化配置
COMMON_OPTIMIZATIONS = {
    'growth_items': {
        'select_related': ['user', 'parent'],
        'prefetch_related': ['tags', 'children']
    },
    'blog_posts': {
        'select_related': ['author', 'category'],
        'prefetch_related': ['tags', 'comments']
    },
    'journal_entries': {
        'select_related': ['user'],
        'prefetch_related': []
    },
    'ai_conversations': {
        'select_related': ['user'],
        'prefetch_related': ['messages']
    },
    'ai_messages': {
        'select_related': ['conversation', 'conversation__user'],
        'prefetch_related': []
    },
    'users': {
        'select_related': ['userprofile', 'usersettings'],
        'prefetch_related': ['growth_items', 'journal_entries', 'values']
    }
}


def get_optimization_config(model_name: str) -> Dict[str, List[str]]:
    """
    获取模型的优化配置
    """
    return COMMON_OPTIMIZATIONS.get(model_name, {})


def apply_common_optimizations(queryset: QuerySet, model_name: str) -> QuerySet:
    """
    应用常用的查询优化
    """
    config = get_optimization_config(model_name)
    
    if config.get('select_related'):
        queryset = queryset.select_related(*config['select_related'])
    
    if config.get('prefetch_related'):
        queryset = queryset.prefetch_related(*config['prefetch_related'])
    
    return queryset


class QueryPerformanceMonitor:
    """
    查询性能监控器
    """
    
    @staticmethod
    def log_slow_queries(threshold_ms: float = 100.0):
        """
        记录慢查询
        """
        if not settings.DEBUG:
            return
        
        for query in connection.queries:
            time_ms = float(query['time']) * 1000
            if time_ms > threshold_ms:
                logger.warning(f"Slow query ({time_ms:.2f}ms): {query['sql'][:200]}...")
    
    @staticmethod
    def get_query_stats() -> Dict[str, Any]:
        """
        获取查询统计信息
        """
        if not settings.DEBUG:
            return {'message': 'Query stats only available in DEBUG mode'}
        
        queries = connection.queries
        total_time = sum(float(q['time']) for q in queries)
        
        return {
            'total_queries': len(queries),
            'total_time': f"{total_time:.3f}s",
            'average_time': f"{total_time / len(queries):.3f}s" if queries else "0s",
            'slowest_query': max(queries, key=lambda q: float(q['time']))['time'] if queries else "0s"
        }


# 装饰器：监控视图的查询性能
def monitor_db_queries(view_func):
    """
    监控数据库查询的装饰器
    """
    @wraps(view_func)
    def wrapper(*args, **kwargs):
        if not settings.DEBUG:
            return view_func(*args, **kwargs)
        
        initial_queries = len(connection.queries)
        start_time = time.time()
        
        result = view_func(*args, **kwargs)
        
        end_time = time.time()
        final_queries = len(connection.queries)
        
        duration = end_time - start_time
        query_count = final_queries - initial_queries
        
        # 记录性能信息
        logger.info(f"View {view_func.__name__}: {query_count} queries in {duration:.3f}s")
        
        # 警告慢查询或高查询数量
        if duration > 1.0 or query_count > 10:
            logger.warning(f"Performance issue in {view_func.__name__}: "
                         f"{query_count} queries, {duration:.3f}s")
        
        return result
    return wrapper


# 批量操作优化器
class BulkOperationOptimizer:
    """
    批量操作优化器
    """
    
    @staticmethod
    def bulk_create_optimized(model_class, objects, batch_size=1000):
        """
        优化的批量创建
        """
        return model_class.objects.bulk_create(objects, batch_size=batch_size)
    
    @staticmethod
    def bulk_update_optimized(objects, fields, batch_size=1000):
        """
        优化的批量更新
        """
        model_class = objects[0].__class__
        return model_class.objects.bulk_update(objects, fields, batch_size=batch_size)
    
    @staticmethod
    def bulk_delete_optimized(queryset, batch_size=1000):
        """
        优化的批量删除
        """
        # 分批删除以避免长时间锁定
        total_deleted = 0
        while True:
            batch = list(queryset[:batch_size])
            if not batch:
                break
            
            batch_queryset = queryset.model.objects.filter(
                pk__in=[obj.pk for obj in batch]
            )
            deleted_count, _ = batch_queryset.delete()
            total_deleted += deleted_count
            
            if deleted_count < batch_size:
                break
        
        return total_deleted
