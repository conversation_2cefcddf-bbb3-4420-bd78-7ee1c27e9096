"""
文件上传安全验证工具 - 灵境(Mentia)后端

文件功能：
    提供文件上传的安全验证，包括文件类型、内容、大小等多重检查

主要功能：
    1. 文件类型验证（MIME类型和文件扩展名双重检查）
    2. 文件内容验证（魔数检查）
    3. 图片文件安全检查
    4. 文件大小和尺寸限制
    5. 恶意文件检测

使用方式：
    from apps.common.file_validators import FileValidator, ImageValidator
    
    validator = ImageValidator()
    if validator.validate(uploaded_file):
        # 文件安全，可以处理
        pass

作者: Mentia后端团队
创建时间: 2025-09-05
版本: v1.0
"""

import os
import mimetypes
from PIL import Image, ImageFile
from io import BytesIO
from typing import Dict, List, Tuple, Optional, Any
from django.core.files.uploadedfile import UploadedFile
from django.conf import settings
import logging

# 尝试导入magic库，如果不存在则设为None
try:
    import magic
    HAS_MAGIC = True
except ImportError:
    magic = None
    HAS_MAGIC = False

logger = logging.getLogger(__name__)

# 启用PIL的截断图像支持
ImageFile.LOAD_TRUNCATED_IMAGES = True


class FileValidationError(Exception):
    """文件验证错误异常"""
    pass


class FileValidator:
    """
    基础文件验证器
    
    功能：
    - 文件类型验证
    - 文件大小验证
    - 文件名安全检查
    - 恶意文件检测
    """
    
    # 危险文件扩展名黑名单
    DANGEROUS_EXTENSIONS = {
        '.exe', '.bat', '.cmd', '.com', '.pif', '.scr', '.vbs', '.js', '.jar',
        '.php', '.php3', '.php4', '.php5', '.phtml', '.asp', '.aspx', '.jsp',
        '.py', '.pl', '.rb', '.sh', '.bash', '.ps1', '.msi', '.deb', '.rpm'
    }
    
    # 危险MIME类型黑名单
    DANGEROUS_MIME_TYPES = {
        'application/x-executable',
        'application/x-msdownload',
        'application/x-msdos-program',
        'application/x-msi',
        'application/x-bat',
        'application/x-sh',
        'application/x-php',
        'text/x-php',
        'application/x-httpd-php',
        'text/x-python',
        'application/x-python-code',
        'text/x-script.python'
    }
    
    def __init__(self, max_size: int = 10 * 1024 * 1024):  # 默认10MB
        self.max_size = max_size
    
    def validate(self, file: UploadedFile) -> bool:
        """
        验证文件安全性
        
        参数:
        - file: 上传的文件对象
        
        返回:
        - bool: 验证是否通过
        
        抛出:
        - FileValidationError: 验证失败时抛出异常
        """
        try:
            # 基础验证
            self._validate_file_size(file)
            self._validate_file_name(file.name)
            self._validate_mime_type(file)
            self._validate_file_content(file)
            
            return True
        except FileValidationError:
            raise
        except Exception as e:
            logger.error(f"File validation error: {str(e)}")
            raise FileValidationError(f"文件验证失败: {str(e)}")
    
    def _validate_file_size(self, file: UploadedFile) -> None:
        """验证文件大小"""
        if file.size > self.max_size:
            size_mb = self.max_size / (1024 * 1024)
            raise FileValidationError(f"文件大小不能超过 {size_mb:.1f}MB")
    
    def _validate_file_name(self, filename: str) -> None:
        """验证文件名安全性"""
        if not filename:
            raise FileValidationError("文件名不能为空")
        
        # 检查文件名长度
        if len(filename) > 255:
            raise FileValidationError("文件名过长")
        
        # 检查危险字符
        dangerous_chars = ['..', '/', '\\', ':', '*', '?', '"', '<', '>', '|']
        for char in dangerous_chars:
            if char in filename:
                raise FileValidationError(f"文件名包含非法字符: {char}")
        
        # 检查危险扩展名
        _, ext = os.path.splitext(filename.lower())
        if ext in self.DANGEROUS_EXTENSIONS:
            raise FileValidationError(f"不允许的文件类型: {ext}")
    
    def _validate_mime_type(self, file: UploadedFile) -> None:
        """验证MIME类型"""
        mime_type = file.content_type
        
        if mime_type in self.DANGEROUS_MIME_TYPES:
            raise FileValidationError(f"不允许的文件类型: {mime_type}")
        
        # 验证MIME类型与文件扩展名是否匹配
        filename = file.name
        if filename:
            guessed_type, _ = mimetypes.guess_type(filename)
            if guessed_type and guessed_type != mime_type:
                logger.warning(f"MIME type mismatch: {mime_type} vs {guessed_type} for {filename}")
    
    def _validate_file_content(self, file: UploadedFile) -> None:
        """验证文件内容（魔数检查）"""
        try:
            # 读取文件头部用于魔数检查
            file.seek(0)
            file_header = file.read(1024)
            file.seek(0)  # 重置文件指针

            # 使用python-magic检查真实文件类型（如果可用）
            if HAS_MAGIC and hasattr(magic, 'from_buffer'):
                detected_mime = magic.from_buffer(file_header, mime=True)

                # 检查检测到的类型是否在危险列表中
                if detected_mime in self.DANGEROUS_MIME_TYPES:
                    raise FileValidationError(f"检测到危险文件类型: {detected_mime}")

                # 验证声明的MIME类型与检测到的类型是否匹配
                if file.content_type and detected_mime != file.content_type:
                    logger.warning(f"Content type mismatch: declared={file.content_type}, detected={detected_mime}")
            else:
                # 如果没有magic库，进行基础的文件头检查
                self._basic_file_header_check(file_header, file.content_type)

        except Exception as e:
            logger.error(f"File content validation error: {str(e)}")
            # 不抛出异常，因为magic库可能不可用

    def _basic_file_header_check(self, file_header: bytes, content_type: str) -> None:
        """基础文件头检查（不依赖magic库）"""
        # 检查常见的危险文件签名
        dangerous_signatures = [
            b'MZ',  # Windows executable
            b'\x7fELF',  # Linux executable
            b'#!/bin/sh',  # Shell script
            b'#!/bin/bash',  # Bash script
            b'<?php',  # PHP script
            b'<script',  # JavaScript
        ]

        for signature in dangerous_signatures:
            if file_header.startswith(signature):
                raise FileValidationError(f"检测到可执行文件或脚本")

        # 基础图片文件头检查
        if content_type and content_type.startswith('image/'):
            image_signatures = {
                b'\xff\xd8\xff': 'image/jpeg',
                b'\x89PNG\r\n\x1a\n': 'image/png',
                b'RIFF': 'image/webp',
                b'GIF87a': 'image/gif',
                b'GIF89a': 'image/gif'
            }

            is_valid_image = False
            for signature in image_signatures:
                if file_header.startswith(signature):
                    is_valid_image = True
                    break

            if not is_valid_image:
                raise FileValidationError("文件头不匹配声明的图片格式")


class ImageValidator(FileValidator):
    """
    图片文件验证器
    
    功能：
    - 继承基础文件验证
    - 图片格式验证
    - 图片尺寸检查
    - 图片内容安全检查
    """
    
    # 允许的图片MIME类型
    ALLOWED_IMAGE_TYPES = {
        'image/jpeg',
        'image/png',
        'image/webp',
        'image/gif'
    }
    
    # 图片文件魔数
    IMAGE_SIGNATURES = {
        b'\xff\xd8\xff': 'image/jpeg',
        b'\x89PNG\r\n\x1a\n': 'image/png',
        b'RIFF': 'image/webp',  # WebP文件以RIFF开头
        b'GIF87a': 'image/gif',
        b'GIF89a': 'image/gif'
    }
    
    def __init__(self, max_size: int = 10 * 1024 * 1024, max_width: int = 4096, max_height: int = 4096):
        super().__init__(max_size)
        self.max_width = max_width
        self.max_height = max_height
    
    def validate(self, file: UploadedFile) -> bool:
        """验证图片文件"""
        # 先执行基础验证
        super().validate(file)
        
        # 图片特定验证
        self._validate_image_type(file)
        self._validate_image_content(file)
        self._validate_image_dimensions(file)
        
        return True
    
    def _validate_image_type(self, file: UploadedFile) -> None:
        """验证图片类型"""
        if file.content_type not in self.ALLOWED_IMAGE_TYPES:
            allowed_types = ', '.join(self.ALLOWED_IMAGE_TYPES)
            raise FileValidationError(f"不支持的图片格式，支持的格式: {allowed_types}")
    
    def _validate_image_content(self, file: UploadedFile) -> None:
        """验证图片内容和魔数"""
        file.seek(0)
        file_header = file.read(16)
        file.seek(0)
        
        # 检查图片魔数
        is_valid_image = False
        for signature, mime_type in self.IMAGE_SIGNATURES.items():
            if file_header.startswith(signature):
                is_valid_image = True
                # 验证魔数与声明的MIME类型是否匹配
                if mime_type != file.content_type and not (
                    signature == b'RIFF' and file.content_type == 'image/webp'
                ):
                    logger.warning(f"Image signature mismatch: {mime_type} vs {file.content_type}")
                break
        
        if not is_valid_image:
            raise FileValidationError("无效的图片文件格式")
    
    def _validate_image_dimensions(self, file: UploadedFile) -> None:
        """验证图片尺寸"""
        try:
            file.seek(0)
            with Image.open(file) as img:
                width, height = img.size
                
                if width > self.max_width or height > self.max_height:
                    raise FileValidationError(
                        f"图片尺寸过大，最大支持 {self.max_width}x{self.max_height}，"
                        f"当前尺寸 {width}x{height}"
                    )
                
                # 检查图片是否损坏
                img.verify()
                
        except FileValidationError:
            raise
        except Exception as e:
            raise FileValidationError(f"图片文件损坏或格式错误: {str(e)}")
        finally:
            file.seek(0)
    
    def get_image_info(self, file: UploadedFile) -> Dict[str, Any]:
        """获取图片信息"""
        try:
            file.seek(0)
            with Image.open(file) as img:
                return {
                    'format': img.format,
                    'mode': img.mode,
                    'size': img.size,
                    'width': img.size[0],
                    'height': img.size[1],
                    'has_transparency': img.mode in ('RGBA', 'LA') or 'transparency' in img.info
                }
        except Exception as e:
            logger.error(f"Failed to get image info: {str(e)}")
            return {}
        finally:
            file.seek(0)


class SecureFileHandler:
    """
    安全文件处理器
    
    功能：
    - 安全的文件名生成
    - 文件路径处理
    - 文件存储安全检查
    """
    
    @staticmethod
    def generate_safe_filename(original_filename: str, user_id: str = None) -> str:
        """
        生成安全的文件名
        
        参数:
        - original_filename: 原始文件名
        - user_id: 用户ID（可选）
        
        返回:
        - str: 安全的文件名
        """
        import uuid
        import time
        
        # 获取文件扩展名
        _, ext = os.path.splitext(original_filename)
        ext = ext.lower()
        
        # 生成唯一标识符
        timestamp = int(time.time())
        unique_id = str(uuid.uuid4())[:8]
        
        # 构建安全文件名
        if user_id:
            safe_filename = f"{user_id}_{timestamp}_{unique_id}{ext}"
        else:
            safe_filename = f"{timestamp}_{unique_id}{ext}"
        
        return safe_filename
    
    @staticmethod
    def get_safe_file_path(filename: str, subfolder: str = 'uploads') -> str:
        """
        获取安全的文件路径
        
        参数:
        - filename: 文件名
        - subfolder: 子文件夹名称
        
        返回:
        - str: 安全的文件路径
        """
        # 确保子文件夹名称安全
        subfolder = subfolder.replace('..', '').replace('/', '').replace('\\', '')
        
        # 按日期组织文件夹
        from datetime import datetime
        date_folder = datetime.now().strftime('%Y/%m/%d')
        
        return os.path.join(subfolder, date_folder, filename)
    
    @staticmethod
    def validate_file_path(file_path: str) -> bool:
        """
        验证文件路径安全性
        
        参数:
        - file_path: 文件路径
        
        返回:
        - bool: 路径是否安全
        """
        # 检查路径遍历攻击
        if '..' in file_path or file_path.startswith('/'):
            return False
        
        # 检查绝对路径
        if os.path.isabs(file_path):
            return False
        
        return True


# 预定义的验证器实例
background_image_validator = ImageValidator(
    max_size=10 * 1024 * 1024,  # 10MB
    max_width=4096,
    max_height=4096
)

avatar_image_validator = ImageValidator(
    max_size=2 * 1024 * 1024,   # 2MB
    max_width=1024,
    max_height=1024
)

document_validator = FileValidator(
    max_size=50 * 1024 * 1024   # 50MB
)


def validate_uploaded_file(file: UploadedFile, validator_type: str = 'image') -> Tuple[bool, str]:
    """
    验证上传的文件
    
    参数:
    - file: 上传的文件
    - validator_type: 验证器类型 ('image', 'background', 'avatar', 'document')
    
    返回:
    - Tuple[bool, str]: (是否通过验证, 错误消息)
    """
    try:
        if validator_type == 'background':
            background_image_validator.validate(file)
        elif validator_type == 'avatar':
            avatar_image_validator.validate(file)
        elif validator_type == 'document':
            document_validator.validate(file)
        elif validator_type == 'image':
            ImageValidator().validate(file)
        else:
            FileValidator().validate(file)
        
        return True, ""
    
    except FileValidationError as e:
        return False, str(e)
    except Exception as e:
        logger.error(f"Unexpected validation error: {str(e)}")
        return False, "文件验证失败，请重试"
