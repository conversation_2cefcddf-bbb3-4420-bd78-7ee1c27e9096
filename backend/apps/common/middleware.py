"""
统一错误处理中间件 - 灵境(Mentia)后端

文件功能：
    提供全局错误处理中间件，统一处理未捕获的异常

主要功能：
    1. 全局异常捕获和处理
    2. 错误日志记录和分类
    3. 用户友好的错误响应
    4. 错误统计和监控

使用方式：
    在settings.py的MIDDLEWARE中添加：
    'apps.common.middleware.ErrorHandlingMiddleware'

作者: Mentia后端团队
创建时间: 2025-09-05
版本: v1.0
"""

import logging
import json
import traceback
from django.http import JsonResponse
from django.core.exceptions import ValidationError, PermissionDenied
from django.db import IntegrityError
from rest_framework import status
from rest_framework.exceptions import APIException
from django.conf import settings

logger = logging.getLogger(__name__)


class ErrorHandlingMiddleware:
    """
    全局错误处理中间件
    
    功能：
    - 捕获所有未处理的异常
    - 返回标准化的JSON错误响应
    - 记录详细的错误日志
    - 在生产环境中隐藏敏感信息
    """
    
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        response = self.get_response(request)
        return response

    def process_exception(self, request, exception):
        """
        处理未捕获的异常
        """
        # 获取请求信息
        user_info = 'Anonymous'
        if hasattr(request, 'user') and request.user.is_authenticated:
            user_info = request.user.email

        request_info = {
            'method': request.method,
            'path': request.path,
            'user': user_info,
            'ip': self.get_client_ip(request)
        }

        # 根据异常类型进行分类处理
        if isinstance(exception, ValidationError):
            return self.handle_validation_error(exception, request_info)
        elif isinstance(exception, IntegrityError):
            return self.handle_integrity_error(exception, request_info)
        elif isinstance(exception, PermissionDenied):
            return self.handle_permission_error(exception, request_info)
        elif isinstance(exception, APIException):
            return self.handle_api_exception(exception, request_info)
        else:
            return self.handle_generic_error(exception, request_info)

    def handle_validation_error(self, exception, request_info):
        """处理数据验证错误"""
        logger.warning(f"Validation error: {str(exception)} | Request: {request_info}")
        
        return JsonResponse({
            'error': '数据验证失败',
            'message': '输入的数据格式不正确，请检查后重试',
            'details': str(exception) if settings.DEBUG else None,
            'code': 'VALIDATION_ERROR'
        }, status=400)

    def handle_integrity_error(self, exception, request_info):
        """处理数据库完整性错误"""
        logger.error(f"Database integrity error: {str(exception)} | Request: {request_info}")
        
        return JsonResponse({
            'error': '数据完整性错误',
            'message': '数据保存失败，可能存在重复或关联数据问题',
            'details': str(exception) if settings.DEBUG else None,
            'code': 'INTEGRITY_ERROR'
        }, status=400)

    def handle_permission_error(self, exception, request_info):
        """处理权限错误"""
        logger.warning(f"Permission denied: {str(exception)} | Request: {request_info}")
        
        return JsonResponse({
            'error': '权限不足',
            'message': '您没有权限执行此操作',
            'code': 'PERMISSION_DENIED'
        }, status=403)

    def handle_api_exception(self, exception, request_info):
        """处理DRF API异常"""
        logger.warning(f"API exception: {str(exception)} | Request: {request_info}")
        
        return JsonResponse({
            'error': str(exception),
            'message': str(exception),
            'details': getattr(exception, 'detail', None) if settings.DEBUG else None,
            'code': 'API_ERROR'
        }, status=getattr(exception, 'status_code', 400))

    def handle_generic_error(self, exception, request_info):
        """处理通用错误"""
        # 记录完整的错误信息
        error_details = {
            'exception_type': type(exception).__name__,
            'exception_message': str(exception),
            'traceback': traceback.format_exc(),
            'request_info': request_info
        }
        
        logger.error(f"Unhandled exception: {json.dumps(error_details, indent=2)}")
        
        # 根据环境返回不同的错误信息
        if settings.DEBUG:
            return JsonResponse({
                'error': '服务器内部错误',
                'message': str(exception),
                'details': {
                    'type': type(exception).__name__,
                    'traceback': traceback.format_exc()
                },
                'code': 'INTERNAL_ERROR'
            }, status=500)
        else:
            return JsonResponse({
                'error': '服务器内部错误，请稍后重试',
                'message': '系统暂时不可用，请稍后重试或联系技术支持',
                'code': 'INTERNAL_ERROR'
            }, status=500)

    def get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class RequestLoggingMiddleware:
    """
    请求日志记录中间件
    
    功能：
    - 记录所有API请求的详细信息
    - 监控请求响应时间
    - 统计API使用情况
    """
    
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        import time
        
        # 记录请求开始时间
        start_time = time.time()
        
        # 记录请求信息
        if request.path.startswith('/api/'):
            user_info = 'Anonymous'
            if hasattr(request, 'user') and request.user.is_authenticated:
                user_info = request.user.email
            
            logger.info(f"API Request: {request.method} {request.path} | User: {user_info}")
        
        # 处理请求
        response = self.get_response(request)
        
        # 记录响应信息
        if request.path.startswith('/api/'):
            duration = time.time() - start_time
            logger.info(f"API Response: {request.method} {request.path} | "
                       f"Status: {response.status_code} | Duration: {duration:.3f}s")
            
            # 慢请求警告
            if duration > 2.0:
                logger.warning(f"Slow API request: {request.method} {request.path} | "
                             f"Duration: {duration:.3f}s")
        
        return response


class CORSMiddleware:
    """
    CORS处理中间件
    
    功能：
    - 处理跨域请求
    - 设置CORS头部
    - 支持预检请求
    """
    
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        response = self.get_response(request)
        
        # 设置CORS头部
        response['Access-Control-Allow-Origin'] = '*'
        response['Access-Control-Allow-Methods'] = 'GET, POST, PUT, PATCH, DELETE, OPTIONS'
        response['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, X-Requested-With'
        response['Access-Control-Max-Age'] = '86400'
        
        return response

    def process_request(self, request):
        """处理预检请求"""
        if request.method == 'OPTIONS':
            from django.http import HttpResponse
            response = HttpResponse()
            response['Access-Control-Allow-Origin'] = '*'
            response['Access-Control-Allow-Methods'] = 'GET, POST, PUT, PATCH, DELETE, OPTIONS'
            response['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, X-Requested-With'
            response['Access-Control-Max-Age'] = '86400'
            return response
        return None


class SecurityHeadersMiddleware:
    """
    安全头部中间件

    功能：
    - 添加安全相关的HTTP头部
    - 防止常见的安全攻击
    - 提高应用安全性
    """

    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        response = self.get_response(request)

        # 添加安全头部
        response['X-Content-Type-Options'] = 'nosniff'
        response['X-Frame-Options'] = 'DENY'
        response['X-XSS-Protection'] = '1; mode=block'
        response['Referrer-Policy'] = 'strict-origin-when-cross-origin'

        # 在生产环境中添加HSTS头部
        if not settings.DEBUG:
            response['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains'

        return response


class FileUploadSecurityMiddleware:
    """
    文件上传安全中间件

    功能：
    - 限制文件上传请求的大小
    - 检查文件上传频率
    - 记录文件上传活动
    - 防止文件上传攻击
    """

    def __init__(self, get_response):
        self.get_response = get_response
        self.max_upload_size = 50 * 1024 * 1024  # 50MB
        self.upload_rate_limit = 10  # 每分钟最多10次上传

    def __call__(self, request):
        # 检查是否是文件上传请求
        if self.is_file_upload_request(request):
            # 验证上传大小
            if not self.validate_upload_size(request):
                return JsonResponse({
                    'error': '上传文件总大小超过限制',
                    'code': 'UPLOAD_SIZE_EXCEEDED'
                }, status=413)

            # 检查上传频率
            if not self.check_upload_rate_limit(request):
                return JsonResponse({
                    'error': '上传频率过高，请稍后重试',
                    'code': 'RATE_LIMIT_EXCEEDED'
                }, status=429)

            # 记录上传活动
            self.log_upload_activity(request)

        response = self.get_response(request)
        return response

    def is_file_upload_request(self, request):
        """检查是否是文件上传请求"""
        return (
            request.method in ['POST', 'PUT', 'PATCH'] and
            request.content_type and
            'multipart/form-data' in request.content_type
        )

    def validate_upload_size(self, request):
        """验证上传文件大小"""
        try:
            content_length = int(request.META.get('CONTENT_LENGTH', 0))
            return content_length <= self.max_upload_size
        except (ValueError, TypeError):
            return False

    def check_upload_rate_limit(self, request):
        """检查上传频率限制"""
        if not hasattr(request, 'user') or not request.user.is_authenticated:
            return True

        from django.core.cache import cache

        user_id = str(request.user.user_id)
        cache_key = f"upload_rate_limit_{user_id}"

        # 获取当前上传次数
        current_count = cache.get(cache_key, 0)

        if current_count >= self.upload_rate_limit:
            return False

        # 增加计数
        cache.set(cache_key, current_count + 1, 60)  # 1分钟过期
        return True

    def log_upload_activity(self, request):
        """记录上传活动"""
        user_info = 'Anonymous'
        if hasattr(request, 'user') and request.user.is_authenticated:
            user_info = request.user.email

        content_length = request.META.get('CONTENT_LENGTH', 0)

        logger.info(f"File upload activity: User={user_info}, "
                   f"Path={request.path}, Size={content_length} bytes, "
                   f"IP={self.get_client_ip(request)}")

    def get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip
