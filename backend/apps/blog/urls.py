"""
博客应用的URL配置
"""
from django.urls import path
from . import views

app_name = 'blog'

urlpatterns = [
    # 分类相关
    path('categories/', views.CategoryListCreateView.as_view(), name='category_list_create'),
    path('categories/<int:category_id>/', views.CategoryDetailView.as_view(), name='category_detail'),
    
    # 标签相关
    path('tags/', views.TagListCreateView.as_view(), name='tag_list_create'),
    path('tags/<int:tag_id>/', views.TagDetailView.as_view(), name='tag_detail'),
    
    # 文章相关
    path('posts/', views.PostListView.as_view(), name='post_list'),
    path('posts/create/', views.PostCreateView.as_view(), name='post_create'),
    path('posts/<uuid:post_id>/', views.PostDetailView.as_view(), name='post_detail'),
    path('posts/<uuid:post_id>/update/', views.PostUpdateView.as_view(), name='post_update'),
    path('posts/<uuid:post_id>/delete/', views.PostDeleteView.as_view(), name='post_delete'),
    path('posts/<uuid:post_id>/like/', views.like_post, name='post_like'),
    
    # 评论相关
    path('posts/<uuid:post_id>/comments/', views.PostCommentsView.as_view(), name='post_comments'),
    
    # 统计和数据
    path('stats/', views.blog_stats, name='blog_stats'),
    path('recent/', views.recent_posts, name='recent_posts'),
]
