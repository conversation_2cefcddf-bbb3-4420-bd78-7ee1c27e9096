"""
博客应用模型
"""
import uuid
from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone

User = get_user_model()


class Category(models.Model):
    """博客分类"""
    category_id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=100, verbose_name='分类名称')
    slug = models.SlugField(max_length=100, unique=True, verbose_name='分类别名')
    description = models.TextField(blank=True, verbose_name='分类描述')
    color = models.CharField(max_length=7, default='#6366f1', verbose_name='分类颜色')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        db_table = 'blog_categories'
        verbose_name = '博客分类'
        verbose_name_plural = '博客分类'
        ordering = ['name']

    def __str__(self):
        return self.name


class Tag(models.Model):
    """博客标签"""
    tag_id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=50, unique=True, verbose_name='标签名称')
    slug = models.SlugField(max_length=50, unique=True, verbose_name='标签别名')
    color = models.CharField(max_length=7, default='#10b981', verbose_name='标签颜色')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')

    class Meta:
        db_table = 'blog_tags'
        verbose_name = '博客标签'
        verbose_name_plural = '博客标签'
        ordering = ['name']

    def __str__(self):
        return self.name


class Post(models.Model):
    """博客文章"""
    STATUS_CHOICES = [
        ('draft', '草稿'),
        ('published', '已发布'),
        ('archived', '已归档'),
    ]

    post_id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    title = models.CharField(max_length=200, verbose_name='文章标题')
    slug = models.SlugField(max_length=200, unique=True, verbose_name='文章别名')
    content = models.TextField(verbose_name='文章内容')
    excerpt = models.TextField(max_length=500, blank=True, verbose_name='文章摘要')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft', verbose_name='状态')

    # 关联字段
    author = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='作者')
    category = models.ForeignKey(Category, on_delete=models.SET_NULL, null=True, blank=True, verbose_name='分类')
    tags = models.ManyToManyField(Tag, blank=True, verbose_name='标签')

    # 元数据
    featured_image = models.URLField(blank=True, verbose_name='特色图片')
    meta_description = models.CharField(max_length=160, blank=True, verbose_name='SEO描述')
    meta_keywords = models.CharField(max_length=200, blank=True, verbose_name='SEO关键词')

    # 统计字段
    view_count = models.PositiveIntegerField(default=0, verbose_name='浏览次数')
    like_count = models.PositiveIntegerField(default=0, verbose_name='点赞次数')

    # 时间字段
    published_at = models.DateTimeField(null=True, blank=True, verbose_name='发布时间')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        db_table = 'blog_posts'
        verbose_name = '博客文章'
        verbose_name_plural = '博客文章'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['status', '-published_at']),
            models.Index(fields=['author', '-created_at']),
            models.Index(fields=['category', '-published_at']),
        ]

    def __str__(self):
        return self.title

    def save(self, *args, **kwargs):
        # 自动设置发布时间
        if self.status == 'published' and not self.published_at:
            self.published_at = timezone.now()
        elif self.status != 'published':
            self.published_at = None

        # 自动生成摘要
        if not self.excerpt and self.content:
            self.excerpt = self.content[:200] + '...' if len(self.content) > 200 else self.content

        super().save(*args, **kwargs)

    @property
    def is_published(self):
        return self.status == 'published' and self.published_at is not None

    @property
    def reading_time(self):
        """估算阅读时间（分钟）"""
        word_count = len(self.content.split())
        return max(1, word_count // 200)  # 假设每分钟阅读200字


class Comment(models.Model):
    """博客评论"""
    comment_id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    post = models.ForeignKey(Post, on_delete=models.CASCADE, related_name='comments', verbose_name='文章')
    author = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='评论者')
    parent = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True,
                              related_name='replies', verbose_name='父评论')

    content = models.TextField(verbose_name='评论内容')
    is_approved = models.BooleanField(default=True, verbose_name='是否审核通过')

    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        db_table = 'blog_comments'
        verbose_name = '博客评论'
        verbose_name_plural = '博客评论'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['post', '-created_at']),
            models.Index(fields=['author', '-created_at']),
        ]

    def __str__(self):
        return f'{self.author.username} 在 {self.post.title} 的评论'
