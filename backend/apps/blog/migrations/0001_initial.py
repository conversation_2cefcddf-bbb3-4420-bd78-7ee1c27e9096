# Generated by Django 5.0.7 on 2025-08-31 16:48

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Category",
            fields=[
                ("category_id", models.AutoField(primary_key=True, serialize=False)),
                ("name", models.CharField(max_length=100, verbose_name="分类名称")),
                (
                    "slug",
                    models.SlugField(
                        max_length=100, unique=True, verbose_name="分类别名"
                    ),
                ),
                ("description", models.TextField(blank=True, verbose_name="分类描述")),
                (
                    "color",
                    models.CharField(
                        default="#6366f1", max_length=7, verbose_name="分类颜色"
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
            ],
            options={
                "verbose_name": "博客分类",
                "verbose_name_plural": "博客分类",
                "db_table": "blog_categories",
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="Tag",
            fields=[
                ("tag_id", models.AutoField(primary_key=True, serialize=False)),
                (
                    "name",
                    models.CharField(
                        max_length=50, unique=True, verbose_name="标签名称"
                    ),
                ),
                ("slug", models.SlugField(unique=True, verbose_name="标签别名")),
                (
                    "color",
                    models.CharField(
                        default="#10b981", max_length=7, verbose_name="标签颜色"
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
            ],
            options={
                "verbose_name": "博客标签",
                "verbose_name_plural": "博客标签",
                "db_table": "blog_tags",
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="Post",
            fields=[
                (
                    "post_id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("title", models.CharField(max_length=200, verbose_name="文章标题")),
                (
                    "slug",
                    models.SlugField(
                        max_length=200, unique=True, verbose_name="文章别名"
                    ),
                ),
                ("content", models.TextField(verbose_name="文章内容")),
                (
                    "excerpt",
                    models.TextField(
                        blank=True, max_length=500, verbose_name="文章摘要"
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("draft", "草稿"),
                            ("published", "已发布"),
                            ("archived", "已归档"),
                        ],
                        default="draft",
                        max_length=20,
                        verbose_name="状态",
                    ),
                ),
                (
                    "featured_image",
                    models.URLField(blank=True, verbose_name="特色图片"),
                ),
                (
                    "meta_description",
                    models.CharField(
                        blank=True, max_length=160, verbose_name="SEO描述"
                    ),
                ),
                (
                    "meta_keywords",
                    models.CharField(
                        blank=True, max_length=200, verbose_name="SEO关键词"
                    ),
                ),
                (
                    "view_count",
                    models.PositiveIntegerField(default=0, verbose_name="浏览次数"),
                ),
                (
                    "like_count",
                    models.PositiveIntegerField(default=0, verbose_name="点赞次数"),
                ),
                (
                    "published_at",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="发布时间"
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "author",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="作者",
                    ),
                ),
                (
                    "category",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="blog.category",
                        verbose_name="分类",
                    ),
                ),
                (
                    "tags",
                    models.ManyToManyField(
                        blank=True, to="blog.tag", verbose_name="标签"
                    ),
                ),
            ],
            options={
                "verbose_name": "博客文章",
                "verbose_name_plural": "博客文章",
                "db_table": "blog_posts",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="Comment",
            fields=[
                (
                    "comment_id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("content", models.TextField(verbose_name="评论内容")),
                (
                    "is_approved",
                    models.BooleanField(default=True, verbose_name="是否审核通过"),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "author",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="评论者",
                    ),
                ),
                (
                    "parent",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="replies",
                        to="blog.comment",
                        verbose_name="父评论",
                    ),
                ),
                (
                    "post",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="comments",
                        to="blog.post",
                        verbose_name="文章",
                    ),
                ),
            ],
            options={
                "verbose_name": "博客评论",
                "verbose_name_plural": "博客评论",
                "db_table": "blog_comments",
                "ordering": ["-created_at"],
                "indexes": [
                    models.Index(
                        fields=["post", "-created_at"],
                        name="blog_commen_post_id_06c2a0_idx",
                    ),
                    models.Index(
                        fields=["author", "-created_at"],
                        name="blog_commen_author__ad8a18_idx",
                    ),
                ],
            },
        ),
        migrations.AddIndex(
            model_name="post",
            index=models.Index(
                fields=["status", "-published_at"], name="blog_posts_status_d9ff7c_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="post",
            index=models.Index(
                fields=["author", "-created_at"], name="blog_posts_author__147634_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="post",
            index=models.Index(
                fields=["category", "-published_at"],
                name="blog_posts_categor_789cdb_idx",
            ),
        ),
    ]
