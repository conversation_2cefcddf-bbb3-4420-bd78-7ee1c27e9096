"""
博客应用视图
"""
from django.shortcuts import get_object_or_404
from django.db.models import Q, Count, Sum
from rest_framework import generics, permissions, status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.pagination import PageNumberPagination
from django.core.cache import cache
from django.utils import timezone

from .models import Category, Tag, Post, Comment
from .serializers import (
    CategorySerializer, TagSerializer, PostListSerializer,
    PostDetailSerializer, PostCreateUpdateSerializer,
    CommentSerializer, CommentCreateSerializer, PostStatsSerializer
)
from apps.common.cache_utils import cache_with_config, cache_manager
from apps.common.query_optimizers import monitor_db_queries


class BlogPagination(PageNumberPagination):
    """博客分页器"""
    page_size = 10
    page_size_query_param = 'page_size'
    max_page_size = 50


class CategoryListCreateView(generics.ListCreateAPIView):
    """分类列表和创建视图"""
    serializer_class = CategorySerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return Category.objects.annotate(
            post_count=Count('post', filter=Q(post__status='published'))
        ).order_by('name')


class CategoryDetailView(generics.RetrieveUpdateDestroyAPIView):
    """分类详情视图"""
    serializer_class = CategorySerializer
    permission_classes = [permissions.IsAuthenticated]
    lookup_field = 'category_id'

    def get_queryset(self):
        return Category.objects.annotate(
            post_count=Count('post', filter=Q(post__status='published'))
        )


class TagListCreateView(generics.ListCreateAPIView):
    """标签列表和创建视图"""
    serializer_class = TagSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return Tag.objects.annotate(
            post_count=Count('post', filter=Q(post__status='published'))
        ).order_by('name')


class TagDetailView(generics.RetrieveUpdateDestroyAPIView):
    """标签详情视图"""
    serializer_class = TagSerializer
    permission_classes = [permissions.IsAuthenticated]
    lookup_field = 'tag_id'

    def get_queryset(self):
        return Tag.objects.annotate(
            post_count=Count('post', filter=Q(post__status='published'))
        )


class PostListView(generics.ListAPIView):
    """文章列表视图"""
    serializer_class = PostListSerializer
    pagination_class = BlogPagination
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        queryset = Post.objects.filter(
            author=self.request.user
        ).select_related('author', 'category').prefetch_related('tags')

        # 状态过滤
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        # 分类过滤
        category_id = self.request.query_params.get('category')
        if category_id:
            queryset = queryset.filter(category_id=category_id)

        # 标签过滤
        tag_id = self.request.query_params.get('tag')
        if tag_id:
            queryset = queryset.filter(tags__tag_id=tag_id)

        # 搜索
        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                Q(title__icontains=search) |
                Q(content__icontains=search) |
                Q(excerpt__icontains=search)
            )

        return queryset.order_by('-created_at')


class PostCreateView(generics.CreateAPIView):
    """文章创建视图"""
    serializer_class = PostCreateUpdateSerializer
    permission_classes = [permissions.IsAuthenticated]

    def perform_create(self, serializer):
        serializer.save(author=self.request.user)


class PostDetailView(generics.RetrieveAPIView):
    """文章详情视图"""
    serializer_class = PostDetailSerializer
    permission_classes = [permissions.IsAuthenticated]
    lookup_field = 'post_id'

    def get_queryset(self):
        return Post.objects.filter(
            author=self.request.user
        ).select_related('author', 'category').prefetch_related('tags')

    def retrieve(self, request, *args, **kwargs):
        instance = self.get_object()

        # 只有在非编辑模式下才增加浏览次数
        # 通过查询参数 'no_count' 来控制是否计数
        should_count = request.query_params.get('no_count') != 'true'

        if should_count:
            # 防止短时间内重复计数（基于IP和文章ID）
            client_ip = self.get_client_ip(request)
            cache_key = f"blog_view_{instance.post_id}_{client_ip}"

            # 如果5分钟内同一IP访问同一文章，不重复计数
            if not cache.get(cache_key):
                instance.view_count += 1
                instance.save(update_fields=['view_count'])
                # 设置5分钟的缓存
                cache.set(cache_key, True, 300)

        serializer = self.get_serializer(instance)
        return Response(serializer.data)

    def get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class PostUpdateView(generics.UpdateAPIView):
    """文章更新视图"""
    serializer_class = PostCreateUpdateSerializer
    permission_classes = [permissions.IsAuthenticated]
    lookup_field = 'post_id'

    def get_queryset(self):
        return Post.objects.filter(author=self.request.user)


class PostDeleteView(generics.DestroyAPIView):
    """文章删除视图"""
    permission_classes = [permissions.IsAuthenticated]
    lookup_field = 'post_id'

    def get_queryset(self):
        return Post.objects.filter(author=self.request.user)


class PostCommentsView(generics.ListCreateAPIView):
    """文章评论视图"""
    serializer_class = CommentSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = BlogPagination

    def get_queryset(self):
        post_id = self.kwargs['post_id']
        return Comment.objects.filter(
            post_id=post_id,
            post__author=self.request.user,
            is_approved=True,
            parent=None  # 只获取顶级评论
        ).select_related('author').prefetch_related('replies').order_by('-created_at')

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return CommentCreateSerializer
        return CommentSerializer

    def get_serializer_context(self):
        context = super().get_serializer_context()
        post_id = self.kwargs['post_id']
        post = get_object_or_404(Post, post_id=post_id, author=self.request.user)
        context['post'] = post
        return context


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def like_post(request, post_id):
    """点赞文章"""
    post = get_object_or_404(Post, post_id=post_id, author=request.user)
    post.like_count += 1
    post.save(update_fields=['like_count'])
    return Response({'like_count': post.like_count})


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
@cache_with_config('statistics')
@monitor_db_queries
def blog_stats(request):
    """博客统计 - 缓存优化版本"""
    user = request.user

    # 使用查询优化减少数据库查询
    posts = Post.objects.filter(author=user).select_related('author')
    total_posts = posts.count()
    published_posts = posts.filter(status='published').count()
    draft_posts = posts.filter(status='draft').count()

    # 浏览和点赞统计
    stats = posts.aggregate(
        total_views=Sum('view_count'),
        total_likes=Sum('like_count')
    )

    # 评论统计 - 优化查询
    total_comments = Comment.objects.filter(
        post__author=user,
        is_approved=True
    ).select_related('post').count()

    # 分类和标签统计
    categories_count = Category.objects.count()
    tags_count = Tag.objects.count()

    data = {
        'total_posts': total_posts,
        'published_posts': published_posts,
        'draft_posts': draft_posts,
        'total_views': stats['total_views'] or 0,
        'total_likes': stats['total_likes'] or 0,
        'total_comments': total_comments,
        'categories_count': categories_count,
        'tags_count': tags_count,
    }

    serializer = PostStatsSerializer(data)
    return Response(serializer.data)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def recent_posts(request):
    """最近文章"""
    posts = Post.objects.filter(
        author=request.user
    ).select_related('author', 'category').prefetch_related('tags').order_by('-created_at')[:5]

    serializer = PostListSerializer(posts, many=True)
    return Response(serializer.data)
