#!/usr/bin/env python
"""
测试运行脚本 - 灵境(Mentia)后端

文件功能：
    提供便捷的测试运行和报告生成功能

主要功能：
    1. 运行所有测试或指定应用的测试
    2. 生成测试覆盖率报告
    3. 输出详细的测试结果
    4. 支持不同的测试配置

使用方式：
    python run_tests.py                    # 运行所有测试
    python run_tests.py --app users        # 运行用户应用测试
    python run_tests.py --coverage         # 生成覆盖率报告
    python run_tests.py --verbose          # 详细输出

作者: Mentia后端团队
创建时间: 2025-09-05
版本: v1.0
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'mentia_backend.settings')

import django
django.setup()

from django.test.utils import get_runner
from django.conf import settings
from django.core.management import execute_from_command_line


class TestRunner:
    """测试运行器"""
    
    def __init__(self):
        self.apps = [
            'apps.users',
            'apps.growth',
            'apps.journal',
            'apps.blog',
        ]
        
        # 只有在非SQLite模式下才添加AI应用
        if not getattr(settings, 'USE_SQLITE', False):
            self.apps.append('apps.ai')
    
    def run_tests(self, app=None, coverage=False, verbose=False, failfast=False):
        """
        运行测试
        
        参数:
        - app: 指定应用名称，如果为None则运行所有测试
        - coverage: 是否生成覆盖率报告
        - verbose: 是否详细输出
        - failfast: 是否在第一个失败时停止
        """
        print("🚀 开始运行测试...")
        print("=" * 60)
        
        # 构建测试命令
        cmd = ['python', 'manage.py', 'test']
        
        if app:
            if app not in [a.split('.')[-1] for a in self.apps]:
                print(f"❌ 错误: 应用 '{app}' 不存在")
                print(f"可用应用: {', '.join([a.split('.')[-1] for a in self.apps])}")
                return False
            
            app_module = f'apps.{app}'
            cmd.append(app_module)
            print(f"📦 运行应用测试: {app}")
        else:
            cmd.extend(self.apps)
            print("📦 运行所有应用测试")
        
        if verbose:
            cmd.append('--verbosity=2')
        
        if failfast:
            cmd.append('--failfast')
        
        # 设置测试数据库
        cmd.extend(['--settings=mentia_backend.settings'])
        
        print(f"🔧 执行命令: {' '.join(cmd)}")
        print("-" * 60)
        
        # 运行测试
        if coverage:
            return self._run_with_coverage(cmd, app)
        else:
            return self._run_without_coverage(cmd)
    
    def _run_without_coverage(self, cmd):
        """运行测试（不生成覆盖率）"""
        try:
            result = subprocess.run(cmd, check=True)
            print("\n✅ 所有测试通过!")
            return True
        except subprocess.CalledProcessError as e:
            print(f"\n❌ 测试失败，退出代码: {e.returncode}")
            return False
        except KeyboardInterrupt:
            print("\n⚠️ 测试被用户中断")
            return False
    
    def _run_with_coverage(self, cmd, app=None):
        """运行测试并生成覆盖率报告"""
        try:
            # 安装coverage包（如果未安装）
            try:
                import coverage
            except ImportError:
                print("📦 安装coverage包...")
                subprocess.run([sys.executable, '-m', 'pip', 'install', 'coverage'], check=True)
                import coverage
            
            # 构建coverage命令
            coverage_cmd = ['coverage', 'run', '--source=.'] + cmd[1:]  # 去掉'python'
            
            print("📊 运行测试并收集覆盖率数据...")
            result = subprocess.run(coverage_cmd, check=True)
            
            print("\n📈 生成覆盖率报告...")
            
            # 生成控制台报告
            subprocess.run(['coverage', 'report'], check=False)
            
            # 生成HTML报告
            html_dir = project_root / 'htmlcov'
            subprocess.run(['coverage', 'html', '--directory', str(html_dir)], check=False)
            
            if html_dir.exists():
                print(f"📄 HTML覆盖率报告已生成: {html_dir}/index.html")
            
            print("\n✅ 测试完成，覆盖率报告已生成!")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"\n❌ 测试失败，退出代码: {e.returncode}")
            return False
        except KeyboardInterrupt:
            print("\n⚠️ 测试被用户中断")
            return False
    
    def list_apps(self):
        """列出可用的应用"""
        print("📋 可用的应用:")
        for app in self.apps:
            app_name = app.split('.')[-1]
            print(f"  - {app_name}")
    
    def check_test_files(self):
        """检查测试文件是否存在"""
        print("🔍 检查测试文件...")
        
        missing_tests = []
        for app in self.apps:
            app_path = project_root / app.replace('.', '/')
            test_file = app_path / 'tests.py'
            
            if test_file.exists():
                print(f"✅ {app}: tests.py 存在")
            else:
                print(f"❌ {app}: tests.py 不存在")
                missing_tests.append(app)
        
        if missing_tests:
            print(f"\n⚠️ 缺少测试文件的应用: {', '.join(missing_tests)}")
        else:
            print("\n✅ 所有应用都有测试文件")
        
        return len(missing_tests) == 0


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Mentia后端测试运行器')
    parser.add_argument('--app', help='指定要测试的应用名称')
    parser.add_argument('--coverage', action='store_true', help='生成覆盖率报告')
    parser.add_argument('--verbose', action='store_true', help='详细输出')
    parser.add_argument('--failfast', action='store_true', help='在第一个失败时停止')
    parser.add_argument('--list', action='store_true', help='列出可用的应用')
    parser.add_argument('--check', action='store_true', help='检查测试文件是否存在')
    
    args = parser.parse_args()
    
    runner = TestRunner()
    
    if args.list:
        runner.list_apps()
        return
    
    if args.check:
        runner.check_test_files()
        return
    
    # 运行测试
    success = runner.run_tests(
        app=args.app,
        coverage=args.coverage,
        verbose=args.verbose,
        failfast=args.failfast
    )
    
    sys.exit(0 if success else 1)


if __name__ == '__main__':
    main()
