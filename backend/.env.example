# Django 后端环境配置示例文件
# 复制此文件为 .env 并填入实际值

# Django 基础设置
DEBUG=True
SECRET_KEY=django-insecure-change-me-in-production-very-long-secret-key-here
ALLOWED_HOSTS=localhost,127.0.0.1,0.0.0.0

# 前端端口配置（用于CORS设置）
FRONTEND_PORT=3000
# 额外的CORS允许源（可选，用逗号分隔）
CORS_ALLOWED_ORIGINS=

# # 数据库配置
# # 无法使用 Docker/PostgreSQL 时，将 USE_SQLITE 设为 True 启动本地开发
# USE_SQLITE=True
# DB_NAME=mentia_db
# DB_USER=mentia_user
# DB_PASSWORD=mentia_password
# DB_HOST=localhost
# DB_PORT=5432

# # Redis 配置
# REDIS_URL=redis://localhost:6379/0

# # JWT 配置
# JWT_SECRET_KEY=your-jwt-secret-key-change-in-production
# JWT_ACCESS_TOKEN_LIFETIME=60
# JWT_REFRESH_TOKEN_LIFETIME=7

# # AI 服务配置（可选）
# OPENAI_API_KEY=your-openai-api-key-here
# EMBEDDING_MODEL=text-embedding-ada-002
# CHAT_MODEL=gpt-3.5-turbo

# # 邮件配置（开发环境使用控制台输出）
# EMAIL_BACKEND=django.core.mail.backends.console.EmailBackend
# EMAIL_HOST=smtp.gmail.com
# EMAIL_PORT=587
# EMAIL_USE_TLS=True
# EMAIL_HOST_USER=<EMAIL>
# EMAIL_HOST_PASSWORD=your-email-password
