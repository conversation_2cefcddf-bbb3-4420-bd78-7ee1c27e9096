version: '3.8'

services:
  postgres:
    image: pgvector/pgvector:pg16
    container_name: mentia_postgres
    environment:
      POSTGRES_DB: mentia_db
      POSTGRES_USER: mentia_user
      POSTGRES_PASSWORD: mentia_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/init.sql:/docker-entrypoint-initdb.d/init.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U mentia_user -d mentia_db"]
      interval: 30s
      timeout: 10s
      retries: 3

  redis:
    image: redis:7-alpine
    container_name: mentia_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

volumes:
  postgres_data:
  redis_data:
