#!/bin/bash

# =============================================================================
# 灵境 (Mentia) 开发环境一键启动脚本
# =============================================================================
#
# 脚本功能：
#   自动化启动灵境项目的完整开发环境，包括前端、后端和数据库服务
#
# 主要功能：
#   1. 环境检查 - 验证Docker、Node.js等必要工具
#   2. 数据库服务 - 启动PostgreSQL和Redis容器
#   3. 后端服务 - 配置并启动Django开发服务器
#   4. 前端服务 - 配置并启动Next.js开发服务器
#   5. 端口配置 - 支持自定义前后端端口
#   6. 配置同步 - 自动更新CORS和API代理配置
#
# 使用方法：
#   ./start-dev.sh                                    # 使用默认端口
#   ./start-dev.sh --frontend-port 9331 --backend-port 8001  # 自定义端口
#   ./start-dev.sh --help                            # 查看帮助
#
# 默认端口：
#   - 前端: 3000 (Next.js)
#   - 后端: 8000 (Django)
#   - 数据库: 5432 (PostgreSQL)
#   - 缓存: 6379 (Redis)
#
# 环境要求：
#   - Docker & Docker Compose
#   - Node.js 18+
#   - Python 3.9+
#
# 作者: Mentia开发团队
# 创建时间: 2024
# 最后更新: 2025-09-05
# 版本: v2.0 - 支持动态端口配置和智能环境检查
#
# TODO: 添加服务健康检查
# TODO: 支持生产环境部署模式
# TODO: 添加日志聚合功能
# =============================================================================

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_backend() {
    echo -e "${BLUE}[后端]${NC} $1"
}

log_frontend() {
    echo -e "${GREEN}[前端]${NC} $1"
}

log_docker() {
    echo -e "${PURPLE}[Docker]${NC} $1"
}

log_error() {
    echo -e "${RED}[错误]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

log_info() {
    echo -e "${CYAN}[信息]${NC} $1"
}

# 解析命令行参数
FRONTEND_PORT=3000
BACKEND_PORT=8000

while [[ $# -gt 0 ]]; do
  case $1 in
    --frontend-port)
      FRONTEND_PORT="$2"
      shift 2
      ;;
    --backend-port)
      BACKEND_PORT="$2"
      shift 2
      ;;
    -h|--help)
      echo "用法: $0 [选项]"
      echo "选项:"
      echo "  --frontend-port PORT    设置前端端口 (默认: 3000)"
      echo "  --backend-port PORT     设置后端端口 (默认: 8000)"
      echo "  -h, --help             显示此帮助信息"
      exit 0
      ;;
    *)
      echo "未知选项: $1"
      echo "使用 -h 或 --help 查看帮助"
      exit 1
      ;;
  esac
done

log_info "🚀 启动灵境 (Mentia) 开发环境..."
log_info "📱 前端端口: $FRONTEND_PORT"
log_info "🔧 后端端口: $BACKEND_PORT"

# 创建日志目录
mkdir -p logs

# 基础命令检查（可选，缺失则跳过对应部分）
has_cmd() {
    command -v "$1" >/dev/null 2>&1
}

require_cmd() {
    if ! has_cmd "$1"; then
        log_error "❌ $1 未安装，请先安装 $1"
        exit 1
    fi
}

# 必需：Python/Node
require_cmd python3
require_cmd node
require_cmd npm

# 检查 Docker 可用性（必需）
if ! has_cmd docker || ! has_cmd docker-compose; then
    log_error "❌ 未检测到 docker 或 docker-compose，请先安装"
    exit 1
fi

# 检查 Docker 是否可用，优先尝试无 sudo，如果失败则使用 sudo
DOCKER_CMD="docker"
DOCKER_COMPOSE_CMD="docker-compose"

if ! docker info >/dev/null 2>&1; then
    log_warning "⚠️  Docker 需要 sudo 权限，将使用 sudo 运行 Docker 命令"
    if ! sudo docker info >/dev/null 2>&1; then
        log_error "❌ Docker 服务未运行或无法访问，请启动 Docker"
        exit 1
    fi
    DOCKER_CMD="sudo docker"
    DOCKER_COMPOSE_CMD="sudo docker-compose"
fi

log_docker "✅ Docker 环境检查通过"

# 准备后端环境配置
if [ ! -f "backend/.env" ]; then
    log_backend "📝 创建后端环境配置..."
    cp backend/.env.example backend/.env
fi

# 更新后端环境配置中的前端端口
log_backend "📝 更新后端CORS配置..."
if grep -q "FRONTEND_PORT=" backend/.env; then
    sed -i "s/FRONTEND_PORT=.*/FRONTEND_PORT=$FRONTEND_PORT/" backend/.env
else
    echo "FRONTEND_PORT=$FRONTEND_PORT" >> backend/.env
fi

# 启动数据库服务
log_docker "🗄️  启动 Docker 数据库服务..."
$DOCKER_COMPOSE_CMD up -d postgres redis
log_docker "⏳ 等待数据库启动..."

# 等待 PostgreSQL 健康检查通过
log_docker "🔍 检查 PostgreSQL 连接..."
for i in {1..30}; do
  if $DOCKER_COMPOSE_CMD exec -T postgres pg_isready -U mentia_user -d mentia_db >/dev/null 2>&1; then
    log_docker "✅ PostgreSQL 已就绪"
    break
  fi
  if [ $i -eq 30 ]; then
    log_error "❌ PostgreSQL 启动超时，请检查 Docker 服务"
    exit 1
  fi
  log_docker "⏳ 等待 PostgreSQL 启动... ($i/30)"
  sleep 2
done

# 确保使用 PostgreSQL
export USE_SQLITE=False

# 启动后端
log_backend "🔧 启动Django后端..."
cd backend

# 使用独立虚拟环境目录，避免历史权限问题
VENV_DIR=".venv"

# 检查虚拟环境
if [ ! -d "$VENV_DIR" ]; then
    log_backend "📦 创建Python虚拟环境..."
    python3 -m venv "$VENV_DIR"
fi

# 激活虚拟环境
source "$VENV_DIR/bin/activate"

# 安装依赖（避免升级pip以规避权限问题）
log_backend "📦 安装Python依赖..."
pip install -r requirements.txt

# 运行数据库迁移
log_backend "🔄 运行数据库迁移..."
python manage.py makemigrations
python manage.py migrate

# 创建超级用户（如果不存在）
log_backend "👤 检查超级用户..."
python manage.py shell -c "
from apps.users.models import User
if not User.objects.filter(is_superuser=True).exists():
    User.objects.create_superuser(email='<EMAIL>', password='admin123456')
    print('✅ 创建了默认超级用户: <EMAIL> / admin123456')
else:
    print('✅ 超级用户已存在')
"

# 启动Django开发服务器，并将输出重定向到日志文件
log_backend "🌐 启动Django开发服务器..."
python manage.py runserver 0.0.0.0:$BACKEND_PORT > ../logs/backend.log 2>&1 &
DJANGO_PID=$!

cd ..

# 准备前端环境
if [ ! -f "frontend/.env.local" ]; then
    log_frontend "📝 创建前端环境配置..."
    cp frontend/.env.local.example frontend/.env.local
fi

# 更新前端环境配置
log_frontend "📝 更新前端环境配置..."
# 更新端口配置
if grep -q "PORT=" frontend/.env.local; then
    sed -i "s/PORT=.*/PORT=$FRONTEND_PORT/" frontend/.env.local
else
    echo "PORT=$FRONTEND_PORT" >> frontend/.env.local
fi

# 更新后端配置
if grep -q "BACKEND_PORT=" frontend/.env.local; then
    sed -i "s/BACKEND_PORT=.*/BACKEND_PORT=$BACKEND_PORT/" frontend/.env.local
else
    echo "BACKEND_PORT=$BACKEND_PORT" >> frontend/.env.local
fi

# 更新API URL
API_URL="http://localhost:$BACKEND_PORT/api"
if grep -q "NEXT_PUBLIC_API_URL=" frontend/.env.local; then
    sed -i "s|NEXT_PUBLIC_API_URL=.*|NEXT_PUBLIC_API_URL=$API_URL|" frontend/.env.local
else
    echo "NEXT_PUBLIC_API_URL=$API_URL" >> frontend/.env.local
fi

# 启动前端
log_frontend "⚛️  启动Next.js前端..."
cd frontend

# 清理缓存以提升性能
log_frontend "🧹 清理前端缓存..."
rm -rf .next/cache 2>/dev/null || true

# 显式加载 .env.local 文件中的环境变量
log_frontend "📋 加载前端环境变量..."
if [ -f ".env.local" ]; then
    # -a 是 'allexport' 的缩写，确保所有变量都被导出
    set -a
    source .env.local
    set +a
fi

# 设置环境变量以确保Next.js使用正确的端口和优化选项
export PORT=$FRONTEND_PORT
export BACKEND_PORT=$BACKEND_PORT
export BACKEND_HOST=localhost
export NODE_ENV=development
export NEXT_TELEMETRY_DISABLED=1  # 禁用遥测以提升性能
export WATCHPACK_POLLING=false    # 禁用轮询以提升性能

# 安装依赖
if [ ! -d "node_modules" ]; then
    log_frontend "📦 安装Node.js依赖..."
    npm install --prefer-offline --no-audit --progress=false
fi

# 启动Next.js开发服务器，并将输出重定向到日志文件
log_frontend "🌐 启动Next.js开发服务器..."
npm run dev:fast > ../logs/frontend.log 2>&1 &
NEXTJS_PID=$!

cd ..

# 等待服务启动
log_info "⏳ 等待服务启动..."
sleep 3

# 启动日志监控
log_info "📊 启动实时日志监控..."

# 创建日志监控函数
monitor_logs() {
    # 使用tail -f监控日志文件，并添加颜色标签
    (
        tail -f logs/backend.log 2>/dev/null | while IFS= read -r line; do
            echo -e "${BLUE}[后端]${NC} $line"
        done
    ) &
    BACKEND_LOG_PID=$!

    (
        tail -f logs/frontend.log 2>/dev/null | while IFS= read -r line; do
            # 检测错误和警告
            if [[ "$line" == *"error"* ]] || [[ "$line" == *"Error"* ]] || [[ "$line" == *"ERROR"* ]]; then
                echo -e "${RED}[前端错误]${NC} $line"
            elif [[ "$line" == *"warn"* ]] || [[ "$line" == *"Warning"* ]] || [[ "$line" == *"WARN"* ]]; then
                echo -e "${YELLOW}[前端警告]${NC} $line"
            else
                echo -e "${GREEN}[前端]${NC} $line"
            fi
        done
    ) &
    FRONTEND_LOG_PID=$!
}

# 启动日志监控
monitor_logs

echo ""
log_info "🎉 灵境 (Mentia) 开发环境启动完成！"
echo ""
log_info "📱 前端地址: http://localhost:$FRONTEND_PORT"
log_info "🔧 后端API: http://localhost:$BACKEND_PORT/api"
log_info "📊 API文档: http://localhost:$BACKEND_PORT/api/docs"
log_info "🛠️  Django管理: http://localhost:$BACKEND_PORT/admin"
echo ""
log_info "默认管理员账户:"
log_info "邮箱: <EMAIL>"
log_info "密码: admin123456"
echo ""
log_info "📋 日志文件位置:"
log_info "  - 后端日志: logs/backend.log"
log_info "  - 前端日志: logs/frontend.log"
echo ""
log_warning "按 Ctrl+C 停止所有服务"

# 等待用户中断
cleanup() {
  log_warning "🛑 正在停止服务..."

  # 停止日志监控进程
  if [ ! -z "$BACKEND_LOG_PID" ]; then
    kill $BACKEND_LOG_PID 2>/dev/null || true
  fi
  if [ ! -z "$FRONTEND_LOG_PID" ]; then
    kill $FRONTEND_LOG_PID 2>/dev/null || true
  fi

  # 停止主服务进程
  kill $DJANGO_PID $NEXTJS_PID 2>/dev/null || true

  log_docker "🗄️  停止 Docker 服务..."
  $DOCKER_COMPOSE_CMD down

  log_info "✅ 所有服务已停止"
  exit
}
trap cleanup INT
wait
